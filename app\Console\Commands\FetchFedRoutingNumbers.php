<?php
namespace App\Console\Commands;

use App\Http\Clients\FedHttpClient;
use App\Models\BankAccountInfo;
use App\Models\FinancialInstitutionMaster;
use App\Models\FinancialInstitutionRoutingNumber;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class FetchFedRoutingNumbers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fetch:fedroutingnumbers';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will fetch the routing number along with their financial institutions.';

    private $fedClient;

    /**
     * Create a new command instance
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->fedClient = new FedHttpClient();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Log::channel('fed-routing-no')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Fetching routing numbers from Fed started...");
        $this->info("Fetching routing numbers from Fed started...");
        $bank_name_array = BankAccountInfo::whereNotNull('finicity_id')->whereNotNull('bank_name')->distinct()->pluck('bank_name')->toArray(); // Fetch the unique Bank names from finicity linked accounts
        $response_routing_array = $this->fedClient->getFedwire(); // Call the client to receive response
        if ($response_routing_array) {
            $fed_db_routing_numbers_master = FinancialInstitutionRoutingNumber::select('financial_institution_routing_numbers.*')->join('financial_institution_masters', 'financial_institution_routing_numbers.financial_institution_id', '=', 'financial_institution_masters.id')->whereNull('financial_institution_routing_numbers.deleted_at')->where('financial_institution_masters.is_fed_excluded', 0)->get()->toArray();
            $fed_db_routing_numbers = array_column($fed_db_routing_numbers_master, 'routing_no');
            $response_routing_numbers = array_column($response_routing_array, 'routingNumber');
            $delete_routing_numbers = array_diff($fed_db_routing_numbers, $response_routing_numbers);
            
            // Update if new routing number not match
            // Iterate over each array in $fed_db_routing_numbers_master
            foreach ($fed_db_routing_numbers_master as $db_item) {
                // Iterate over each array in $response_routing_array
                foreach ($response_routing_array as $response_item) {
                    // Compare the "routingNumber" values
                    if ($db_item['routing_no'] === $response_item['routingNumber'] && $db_item['new_routing_no'] !== $response_item['newRoutingNumber']) {
                        Log::channel('fed-routing-no')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "The new routing number update was successful for Routing Number " . $db_item['routing_no'] . " New Routing Number " . $response_item['newRoutingNumber']);
                        FinancialInstitutionRoutingNumber::where('routing_no', $db_item['routing_no'])->whereNull('deleted_at')->update(['new_routing_no' => $response_item['newRoutingNumber']]);
                    }
                }
            }
            
            if (count($delete_routing_numbers) > 0) {
                Log::channel('fed-routing-no')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Deleting outdated routing numbers...");
                $this->info("Deleting outdated routing numbers...");
                FinancialInstitutionRoutingNumber::whereIn('routing_no', $delete_routing_numbers)->update(['deleted_at' => Carbon::now()]);
                Log::channel('fed-routing-no')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Deleted outdated routing numbers successfully.");
                $this->info("Deleted outdated routing numbers successfully.");
            }

            $new_routing_numbers = array_diff($response_routing_numbers, $fed_db_routing_numbers);
            if (count($new_routing_numbers) > 0) {
                Log::channel('fed-routing-no')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . count($new_routing_numbers) . " new routing numbers found. Processing...");
                $this->info(count($new_routing_numbers) . " new routing numbers found. Processing...");
                // Filter the $jsonData array based on the routing numbers
                $filteredData = array_filter($response_routing_array, function ($item) use ($new_routing_numbers) {
                    return in_array($item['routingNumber'], $new_routing_numbers);
                });
                // Reindex the array to reset keys
                $filteredNewData = array_values($filteredData);

                $skip_bank_array = [CITIZENS_BANK, UNION_BANK];
                // Constructing the regular expression pattern
                $pattern = '/(?:' . implode('|', array_map('preg_quote', $skip_bank_array)) . ')/i';

                foreach ($filteredNewData as $new_routing_number) {
                    $bank_name_match = null;
                    // Check if the customerName matches any of the bank names in $skip_bank_array
                    if (!preg_match($pattern, $new_routing_number['customerName'])) {
                        // Process if the customerName does not match any of the bank names in $skip_bank_array
                        // Check for the best partial match in $bank_name_array
                        foreach ($bank_name_array as $bank_name) {
                            if ($bank_name !== null && $bank_name !== '' && stripos($new_routing_number['customerName'], $bank_name) !== false) {
                                $bank_name_match = $bank_name;
                                Log::channel('fed-routing-no')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Bank name matched for " . $new_routing_number['customerName'] . " is " . $bank_name);
                                $this->info("Bank name matched for " . $new_routing_number['customerName'] . " is " . $bank_name);
                                break;
                            }
                        }
                    }

                    $is_finicity = $bank_name_match != null ? 1 : 0;
                    $bank_name = $bank_name_match != null ? $bank_name_match : $new_routing_number['customerName'];

                    $institution = FinancialInstitutionMaster::where('bank_name', $bank_name)->first();
                    if (!$institution) {
                        $institution                       = new FinancialInstitutionMaster();
                        $institution->bank_name            = $bank_name;
                        $institution->is_finicity          = $is_finicity;
                        $institution->save();
                    }
                    // Insert the data in financial_institution_routing_numbers table
                    $institution_routing = new FinancialInstitutionRoutingNumber();
                    $institution_routing->routing_no = $new_routing_number['routingNumber'];
                    $institution_routing->new_routing_no = $new_routing_number['newRoutingNumber'];
                    $institution_routing->financial_institution_id  = $institution->id;
                    $institution_routing->state = $new_routing_number['customerState'];
                    $institution_routing->save();
                    Log::channel('fed-routing-no')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Inserted routing number for " . $bank_name . " and Routing Number " . $new_routing_number['routingNumber'] . " successfully.");
                    $this->info("Inserted routing number for " . $bank_name . " and Routing Number " . $new_routing_number['routingNumber'] . " successfully.");
                }
            }
        }
        Log::channel('fed-routing-no')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Processing of new routing numbers completed.");
        $this->info("Processing of new routing numbers completed.");
    }
}
