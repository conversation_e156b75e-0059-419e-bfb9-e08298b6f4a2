<?php

namespace App\Console\Commands;

use App\Models\UserBankAccountInfo;
use App\Models\UserBankAccountInfoHistory;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FixDelinkBankAccount extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:delinkaccount';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix delink bank account.';
    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->info("Delink data fixing...");
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Delink data fixing...");
        $active_status = getStatus(BANK_ACTIVE);
        $bank_delink = getStatus(BANK_DELINK);
        $user_sql = "SELECT DISTINCT u.user_id, if (ubaih.consumer_id IS NOT NULL, 1, 0) as active_bank_history_exists
        FROM users u
        LEFT JOIN user_bank_account_info ubai ON u.user_id = ubai.user_id AND ubai.status = ?
        JOIN user_bank_account_info dubai ON u.user_id = dubai.user_id AND dubai.status = ? AND dubai.updated_at between '2024-04-05 20:37' and '2024-04-05 20:38'
        LEFT JOIN user_bank_account_info_history ubaih ON u.user_id = ubaih.consumer_id AND ubaih.status = ?
        WHERE ubai.user_id IS NULL";
                    
        $users = DB::connection(MYSQL_RO)->select($user_sql, [$active_status, $bank_delink, $active_status]);
        if (count($users) > 0) {
            $this->info(count($users) . " users found for fix delink bank account.");
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . count($users) . " users found for fix delink bank account.");
            foreach ($users as $user) {
                $last_active_bank_history = null;
                if ($user->active_bank_history_exists == 1) {
                   $last_active_bank_history = UserBankAccountInfoHistory::where(['consumer_id' => $user->user_id, 'status' => $active_status])->orderBy('created_at', 'DESC')->first();
                }
                if ($last_active_bank_history) {
                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Previous active bank found from history table for User ID: " . $user->user_id . " and Account ID: " . $last_active_bank_history->account_id);
                   $last_active_bank = UserBankAccountInfo::where(['id' => $last_active_bank_history->account_id])->orderBy('created_at', 'DESC')->first();
                } else {
                    $last_active_bank = UserBankAccountInfo::where(['user_id' => $user->user_id])->orderBy('created_at', 'DESC')->first();
                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Previous active bank not found from history table for User ID: " . $user->user_id);
                }
                if ($last_active_bank) {
                    $last_active_bank->status = $active_status;
                    $last_active_bank->save();
                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Bank account status updated successfully for User ID: " . $user->user_id . " and Account ID: " . $last_active_bank->id);
                }
            }
        } else {
            $this->info("User not found for fix delink bank account.");
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "User not found for fix delink bank account");
        }
        $this->info("Delink data fixed successfully.");
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Delink data fixed successfully.");
    }


}
