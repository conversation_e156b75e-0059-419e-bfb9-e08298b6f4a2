<?php
namespace App\Console\Commands;

use App\Models\MicrobiltRuleMatchHistory;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class MicrobiltIsFinalOutComeDataFix extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'microbilt:is_final_out_come';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command fix microbilt is final out come data.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Fix microbilt is final out come data...");
        $this->info("Fix microbilt is final out come data...");
        $allMatchingRulePhone = MicrobiltRuleMatchHistory::groupBy('phone')->pluck('phone')->toArray();
        foreach ($allMatchingRulePhone as $phone) {
            $latestRow = MicrobiltRuleMatchHistory::where('phone', $phone)->orderBy('id', 'desc')->first();
            if ($latestRow) {
                MicrobiltRuleMatchHistory::where('phone', $phone)->where('batch_id', '!=', $latestRow->batch_id)->update(['is_final_outcome' => 0]); // Update previous batch data
                $this->updateFinalOutcomeRuleMatchHistory($latestRow->batch_id); // Update current batch data
            }
        }
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Fixed microbilt is final out come data...");
        $this->info("Fixed microbilt is final out come data...");
    }

    // Check highest pririty rule and set is_final_outcome
    private function updateFinalOutcomeRuleMatchHistory($batch_id)
    {
        $microbiltRuleMatchHistory = MicrobiltRuleMatchHistory::join('microbilt_rules as mr', 'microbilt_rule_match_history.rule_id', '=', 'mr.id')->where('microbilt_rule_match_history.batch_id', $batch_id)->select('microbilt_rule_match_history.*', 'mr.matching_decision_code')->get();

        $finalOutComeData = [];
        // If we found MICROBILT_OUTCOME_TWO for current barch then set final outcome 1 for MICROBILT_OUTCOME_TWO
        if (count($microbiltRuleMatchHistory->where('matching_decision_code', MICROBILT_OUTCOME_TWO)) > 0) {
            $finalOutComeData = $microbiltRuleMatchHistory->where('matching_decision_code', MICROBILT_OUTCOME_TWO);
        } else if (count($microbiltRuleMatchHistory->where('matching_decision_code', MICROBILT_OUTCOME_THREE)->where('canpay_positive_transaction', 0)) > 0) {
            $finalOutComeData = $microbiltRuleMatchHistory->where('matching_decision_code', MICROBILT_OUTCOME_THREE)->where('canpay_positive_transaction', 0);
        } else if (count($microbiltRuleMatchHistory->where('matching_decision_code', MICROBILT_OUTCOME_THREE)->where('canpay_positive_transaction', 1)) > 0) {
            $finalOutComeData = $microbiltRuleMatchHistory->where('matching_decision_code', MICROBILT_OUTCOME_THREE)->where('canpay_positive_transaction', 1);
        } else if (count($microbiltRuleMatchHistory->where('matching_decision_code', MICROBILT_OUTCOME_FOUR)) > 0) {
            $finalOutComeData = $microbiltRuleMatchHistory->where('matching_decision_code', MICROBILT_OUTCOME_FOUR);
        } else {
            $finalOutComeData = $microbiltRuleMatchHistory;
        }
        if (count($finalOutComeData) > 0) {
            $ids = $finalOutComeData->pluck('id')->toArray();
            MicrobiltRuleMatchHistory::whereIn('id', $ids)->update(['is_final_outcome' => 1]); // update final outcome 1 for high priority rules
        }
    }

}
