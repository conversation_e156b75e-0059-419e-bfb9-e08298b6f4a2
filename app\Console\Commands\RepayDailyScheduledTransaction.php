<?php
namespace App\Console\Commands;

use App\Models\ReturnTransactionRepaymentSchedule;
use App\Models\TransactionDetails;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class RepayDailyScheduledTransaction extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'repay:dailyscheduledtransaction {--bankpostingdate=}';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command updates the daily scheduled transactions.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->info("Repay Scheduled transactions...");
        $bankPostingDate = $this->option('bankpostingdate');
        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "=========== Fetch the Transactions for Repayment for Date : ".$bankPostingDate);

        // fetch all the approved by consumer returned transactions
        $repaymentReturnSchedules = ReturnTransactionRepaymentSchedule::where(['repayment_bank_processing_date' => $bankPostingDate, 'repayment_initiated' => 0])->get();

        foreach($repaymentReturnSchedules as $schedules){
            //Update the Transaction for Repayment
            $transaction = TransactionDetails::where("id", $schedules->transaction_id)->first();
            $transaction->represent_block = 1;
            $transaction->approved_to_represent = 1;
            $transaction->consumer_approval = 1;
            $transaction->repayment_scheduled = 0;
            $transaction->save();

            // update the Return Transaction Repayment Schedule that rEturn is Initiated
            $repaymentSchedule = ReturnTransactionRepaymentSchedule::where(['id' => $schedules->id])->first();
            $repaymentSchedule->repayment_initiated = 1;
            $repaymentSchedule->save();

            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "=========== Repayment Initiated for Transaction ID  : ".$schedules->transaction_id);
        }
        
        return true;
    }
    
}
