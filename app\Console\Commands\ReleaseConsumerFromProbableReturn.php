<?php
namespace App\Console\Commands;

use App\Models\BankDetailsForProbableReturn;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ReleaseConsumerFromProbableReturn extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'release:consumer';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will release all the consumers form probable return after 3 banking days';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Log::channel('release-consumer-from-return')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Release consumer from probable return process started...");
        $allBankingDetails = BankDetailsForProbableReturn::all();
        Log::channel('release-consumer-from-return')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Total records to be processed: " . count($allBankingDetails));
        if (count($allBankingDetails) > 0) {
            foreach ($allBankingDetails as $details) {
                Log::channel('release-consumer-from-return')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Release date calculation started for date: " . $details->created_at);
                $releaseDate = $this->_getTransactionReleaseDate($details->created_at);
                if ($releaseDate && $releaseDate <= date('Y-m-d')) {
                    Log::channel('release-consumer-from-return')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Release condition matched for date: " . $details->created_at . ". Proceed for deletion.");
                    $details->delete(); // Delete the data from the table
                } else {
                    Log::channel('release-consumer-from-return')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Release condition not matched for date: " . $details->created_at . ". Skip the deletion process.");
                }
            }
        } else {
            Log::channel('release-consumer-from-return')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exiting process as the number of records to be processed is 0.");
        }
    }

    /**
     * _getTransactionReleaseDate
     * This fucntion will calculate the release date based on weekend and holidays
     * @param  mixed $date
     * @return void
     */
    private function _getTransactionReleaseDate($import_date)
    {
        $date = date('Y-m-d', strtotime($import_date . ' +1 day'));

        $releaseDate = "";
        $nextDay = $date;
        $inc = 0;

        for ($i = 1; $i <= env('RELEASE_LOOP_PERIOD'); $i++) {
            $releaseDate = $nextDay;
            $inc++;
            $nextDay = date('Y-m-d', strtotime($nextDay . ' +1 Weekday'));

            if ($inc == env('RELEASE_CONSUMER_FROM_PROBABLE_RETURN')) {
                break;
            }
        }

        Log::channel('release-consumer-from-return')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Release Date for date: " . $date . " is " . $releaseDate);
        return $releaseDate;
    }

}
