<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\UserBankAccountOwnerInfo;
use App\Models\UserBankAccountOwnerInfoMatchDetail;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CompareOwnerInfo extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'compare:owner-info';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Compare and store user bank account owner info match details';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        Log::info('Starting comparison of user bank account owner info...');

        // Fetch records from user_bank_account_owner_info where no match details exist
        $accountOwnerInfos = UserBankAccountOwnerInfo::leftJoin('user_bank_account_owner_info_match_details',
            'user_bank_account_owner_info.id', '=', 'user_bank_account_owner_info_match_details.user_bank_account_owner_info_id')
            ->whereNull('user_bank_account_owner_info_match_details.id')
            ->select('user_bank_account_owner_info.*')
            ->get();

        if ($accountOwnerInfos->isEmpty()) {
            Log::info('No new records found to compare.');
            $this->info('No new records found to compare.');
            return Command::SUCCESS;
        }

        $this->info('Found ' . $accountOwnerInfos->count() . ' records to compare.');
        Log::info('Found ' . $accountOwnerInfos->count() . ' records to compare.');

        foreach ($accountOwnerInfos as $account_owner_info) {
            $data = $account_owner_info->toArray();

            $user = User::where('user_id', $data['consumer_id'])->select('first_name', 'middle_name', 'last_name', 'street_address', 'city', 'state', 'zipcode')->first();

            if (!$user) {
                Log::error("User not found for consumer ID: " . $data['consumer_id']);
                $this->warn("User not found for consumer ID: " . $data['consumer_id']);
                continue;
            }

            // Normalize and concatenate user's full name
            $userFullName = preg_replace('/\s+/', ' ', trim($user->first_name . ' ' . ($user->middle_name ?? '') . ' ' . $user->last_name));

            // Normalize owner name from $data
            $ownerName = preg_replace('/\s+/', ' ', trim($data['owner_name'] ?? ''));

            // Compare name
            $nameMatchPercentage = round(getWeightedMatch(strtolower($userFullName), strtolower($ownerName)), 2);

            // Normalize and concatenate user's full address
            $userFullAddress = preg_replace('/\s+/', ' ', trim($user->street_address . ', ' . $user->city . ', ' . $user->state . ' ' . $user->zipcode));

            // Normalize owner address from $data
            $ownerFullAddress = preg_replace('/\s+/', ' ', trim($data['owner_address'] ?? ''));

            // Compare address
            $addressMatchPercentage = round(getWeightedMatch(strtolower($userFullAddress), strtolower($ownerFullAddress)), 2);

            // Insert comparison data into user_bank_account_owner_info_match_details table
            $matchDetails = new UserBankAccountOwnerInfoMatchDetail();
            $matchDetails->user_bank_account_owner_info_id = $account_owner_info->id;
            $matchDetails->consumer_id = $data['consumer_id'];
            $matchDetails->account_id = $data['account_id'];
            $matchDetails->name_during_registration = $userFullName;
            $matchDetails->address_during_registration = $userFullAddress;
            $matchDetails->name_from_banking_solution = $ownerName;
            $matchDetails->address_from_banking_solution = $ownerFullAddress;
            $matchDetails->name_match_percentage = $nameMatchPercentage;
            $matchDetails->address_match_percentage = $addressMatchPercentage;
            $matchDetails->save();

            Log::info("Comparison completed and data stored for owner info ID: {$account_owner_info->id}, Consumer ID: {$data['consumer_id']}");
            $this->info("Data stored for owner info ID: {$account_owner_info->id}, Consumer ID: {$data['consumer_id']}");
        }

        Log::info('Comparison of user bank account owner info completed.');
        $this->info('Comparison completed successfully.');
    }
}
