<?php

use App\Models\StatusMaster;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        StatusMaster::firstOrCreate(['status' => 'Awaiting Admin Approval', 'code' => '1004']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {}
};
