<?php
namespace App\Console\Commands;

use App\Models\AggregationHistory;
use App\Models\ConsumerAccountBalance;
use App\Models\User;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Console\Command;

class ScheduleAggregationHistory extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'schedule:aggregation-history';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will save the aggregation history for each of the account for whom balance fetched previous day.';
    private $chunkSize = 1000;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->info("fetch batch of users started....");

        $offset = 0;
        $totalCountOfUsers = 0;
        $this->info("save aggregation history started....");
        $limit = $this->chunkSize;

        do {
            $active = getStatus(ACTIVE);
            $userActive = getStatus(USER_ACTIVE);
            $suspected = getStatus(SUSPECTED_FRAUD);
            $status_array = [$active, $userActive, $suspected];
            try {
                // fetch all active conumers id
                $userIds = User::on(MYSQL_REPORTS)->where([
                    'role_id' => getRole(CONSUMER),
                    'bank_link_type' => 1
                ])->whereIn('status', $status_array)
                ->select('user_id')->orderBy('created_at', 'DESC')->limit($limit)->offset($offset)->pluck('user_id')->toArray();
                Log::channel('aggregation-history')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "User batch no of user is: " . count($userIds));
            } catch (\Exception $e) {
                Log::channel('aggregation-history')->error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "There was some problem trying to fetch user Ids for limit and offset is: " . $limit . " and " .$offset, [EXCEPTION => $e]);
            }
            // get the previous day (As ETL DB will be synced with main DB in late night so here previous day means 2 days prior)
            $previousDay = Carbon::now()->subDays(2)->format('Y-m-d');
            Log::channel('aggregation-history')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Previous day is: " . $previousDay);
            // fetch pending status
            $pending = getStatus(PENDING);
            // Fetch all the unique account records from previous day blance fetch details
            $this->info("previous day record fetch started....");
            try {
                $previuosDayRecords = ConsumerAccountBalance::on(MYSQL_REPORTS)->select('consumer_account_balances.consumer_id', 'consumer_account_balances.account_id', 'consumer_account_balances.banking_solution_response', 'transaction_details.id as tr_id')
                    ->leftjoin("transaction_details", function ($join) use ($previousDay, $pending) {
                        $join->on("transaction_details.account_id", "=", "consumer_account_balances.account_id")
                            ->whereNull('transaction_details.transaction_ref_no')
                            ->where('transaction_details.status_id', $pending)
                            ->where('transaction_details.local_transaction_date', $previousDay)
                            ->where('transaction_details.consumer_bank_posting_amount', '>', 0);
                    })
                    ->whereNotNull('consumer_account_balances.banking_solution_response')
                    ->whereDate('consumer_account_balances.created_at', $previousDay)
                    ->whereIn('consumer_account_balances.consumer_id', $userIds)
                    ->groupBy('consumer_account_balances.account_id')
                    ->orderBy('consumer_account_balances.created_at', 'DESC')
                    ->get();
                Log::channel('aggregation-history')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Previous day records count: " . count($previuosDayRecords));
            } catch (\Exception $e) {
                Log::channel('aggregation-history')->error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "There was some problem trying to fetch previous day records for number of user Ids: " . count($userIds), [EXCEPTION => $e]);
            }

            $resultArray = [];
            if (count($previuosDayRecords) > 0) {
                // loop through records and save aggregation history for each record
                foreach ($previuosDayRecords as $record) {
                    $currentDate = Carbon::now()->format('Y-m-d H:i:s');
                    $response = json_decode($record['banking_solution_response'], true);
                    $data['user_id'] = $record['consumer_id'];
                    $data['account_id'] = $record['account_id'];
                    $data['aggregation_success_date'] = isset($response['aggregationSuccessDate']) ? Carbon::parse($response['aggregationSuccessDate'])->format('Y-m-d H:i:s') : null;
                    $data['aggregation_attempt_date'] = isset($response['aggregationAttemptDate']) ? Carbon::parse($response['aggregationAttemptDate'])->format('Y-m-d H:i:s') : null;
                    $data['aggregation_status_code'] = isset($response['aggregationStatusCode']) ? $response['aggregationStatusCode'] : null;
                    $data['has_transaction'] = $record['tr_id'] != null ? 1 : 0;
                    $data['created_at'] = $currentDate;
                    $data['updated_at'] = $currentDate;
                    array_push($resultArray, $data);
                    if($this->chunkSize == count($resultArray)){
                        // bulk insert aggregation history
                        $this->info("inserting in loop....");
                        AggregationHistory::insert($resultArray);
                        Log::channel('aggregation-history')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Aggregation History Inserted rows count: " . count($resultArray));
                        $this->info("Inserted " . count($resultArray) . " rows in aggragation history table.");
                        $resultArray = [];
                    }
                }
            }

            if(!empty($resultArray)){
                // bulk insert aggregation history
                $this->info("inserting outside loop....");
                AggregationHistory::insert($resultArray);
                Log::channel('aggregation-history')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Aggregation History Inserted rows count: " . count($resultArray));
                $this->info("Inserted " . count($resultArray) . " rows in aggragation history table.");
                $resultArray = [];
            }
            // Increment the total processed users count
            $totalCountOfUsers += count($userIds);
            $this->info("Total users processed till now is: " . $totalCountOfUsers);

            // Update the offset for the next batch
            $offset += $this->chunkSize;

        } while (count($userIds) > 0 && count($userIds) === $this->chunkSize);
        Log::channel('aggregation-history')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Total number of users processed is: " . $totalCountOfUsers);
        Log::channel('aggregation-history')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Aggregation history successfully updated at UTC Time: " . Carbon::now() . ".");
        $this->info("Schedule to save the aggregation history completed successfully");
    }
}
