<?php
namespace App\Console\Commands;

use App\Http\Factories\FakeDataGenerator\FakeDataGeneratorFactory;
use Illuminate\Console\Command;

class CreateFakeTransactions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'transactions:create {--transactions=}';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will generate test data based on inputs';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->fakedatagenerator = new FakeDataGeneratorFactory();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->info("Processing....");
        $this->fakedatagenerator->populateTransactionData($this->option('transactions'));
        $this->info("Fake Transactions Generated Successfully.");
    }
}
