<?php

namespace App\Console\Commands;

use App\Http\Factories\Transaction\TransactionFactory;
use App\Models\MerchantStores;
use App\Models\RegisteredMerchantMaster;
use App\Models\TransactionDetails;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PostSingleMerchantTransaction extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'post:singletransaction {--transaction_date=} {--merchant_id=}';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command schedules the Merchant and CanPay end transactions to Acheck21';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->transaction = new TransactionFactory();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $transaction_date = $this->option('transaction_date');
        $merchant_id = $this->option('merchant_id');
        Log::channel('transaction-scheduler')->info(addslashes(__METHOD__) . "(" . LINE . __LINE__ . ") - " . "Single Merchant transaction scheduler started running at " . Carbon::now() . " UTC for Transaction date " . $transaction_date);
        $this->_createMerchantCreditTransaction($transaction_date, $merchant_id);
        Log::channel('transaction-scheduler')->info(addslashes(__METHOD__) . "(" . LINE . __LINE__ . ") - " . "Single Merchant credit transaction finished at " . Carbon::now() . " UTC for Transaction date " . $transaction_date);
        $this->_createMerchantDebitTransaction($transaction_date, $merchant_id);
        Log::channel('transaction-scheduler')->info(addslashes(__METHOD__) . "(" . LINE . __LINE__ . ") - " . "Single Merchant debit transaction finished at " . Carbon::now() . " UTC for Transaction date " . $transaction_date);
        // $this->_createCanPayTransaction($transaction_date, $merchant_id);
        Log::channel('transaction-scheduler')->info(addslashes(__METHOD__) . "(" . LINE . __LINE__ . ") - " . "Single CanPay credit transaction finished at " . Carbon::now() . " UTC for Transaction date " . $transaction_date);
        Log::channel('transaction-scheduler')->info(addslashes(__METHOD__) . "(" . LINE . __LINE__ . ") - " . "Single Merchant transaction scheduler finished running at " . Carbon::now() . " UTC for Transaction date " . $transaction_date);
    }
    /**
     * Create merchant end transaction with all the accumulated amount
     */
    private function _createMerchantCreditTransaction($transaction_date, $merchant_id)
    {
        $transactions = MerchantStores::join('terminal_master', 'terminal_master.merchant_store_id', '=', 'merchant_stores.id')
            ->join('transaction_details', 'terminal_master.id', '=', 'transaction_details.terminal_id')
            ->join('registered_merchant_master', 'registered_merchant_master.id', '=', 'merchant_stores.merchant_id')
            ->join('user_bank_account_info', 'user_bank_account_info.merchant_id', '=', 'merchant_stores.merchant_id')
            ->leftJoin('timezone_masters', 'timezone_masters.id', '=', 'transaction_details.timezone_id')
            ->select(DB::raw('sum(transaction_details.amount) as sum'), DB::raw('sum(transaction_details.tip_amount) as tip_sum'), 'user_bank_account_info.*', 'registered_merchant_master.merchant_name', 'timezone_masters.timezone_name', 'merchant_stores.store_id as store_id', 'transaction_details.local_transaction_time', 'transaction_details.scheduled_posting_date')
            ->where('transaction_details.status_id', getStatus(PENDING))
            ->where('transaction_details.is_v1', 0)
            ->where('registered_merchant_master.id', $merchant_id)
            ->whereRaw("transaction_details.scheduled_posting_date = ?", [$transaction_date])
            ->whereRaw('transaction_details.transaction_ref_no is null AND transaction_details.merchant_id is null and transaction_details.consumer_id is not null AND transaction_details.id NOT IN (SELECT COALESCE(transaction_details.transaction_ref_no,0) FROM transaction_details inner join `status_master` on `transaction_details`.`status_id` = `status_master`.`id` WHERE `status_master`.`code` IN (' . RETURNED . '))')
            ->groupBy('merchant_stores.merchant_id')
            ->get();

        DB::beginTransaction();
        foreach ($transactions as $transaction) {
            $this->info("Posting merchant credit transaction to acheck21 for merchant id: " . $transaction->merchant_id);
            Log::channel('transaction-scheduler')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Posting merchant credit transaction to acheck21 for merchant id: " . $transaction->merchant_id);
            try {
                //create the transaction through acheck21
                $total_amount = ($transaction->sum + $transaction->tip_sum);
                $params['amount'] = -$total_amount;
                $params['merchant_id'] = $transaction->merchant_id;
                $params['acheck_account_id'] = ENV('CANPAY_SETTLEMENT_ID');
                // sending store id and transaction date time as timestamp to create the check number for acheck21 posting
                $params['store_id'] = $transaction->store_id;
                $params['transaction_time'] = strtotime($transaction->local_transaction_time);

                if (env('ACHECK_POSTING')) {
                    //calling the factory function to create merchant credit transaction into acheck21
                    $response = $this->transaction->createMerchantDepositTransaction($params);
                    $response_decoded = json_decode($response, true);
                    Log::channel('transaction-scheduler')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is enabled. Posting in ACHECK21.");
                } else {
                    $response_decoded['documentId'] = rand(********, ********);
                    Log::channel('transaction-scheduler')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is disabled. Continue with a random number.");
                }


                //create a new transaction
                $transaction_details = new TransactionDetails();
                $transaction_details->transaction_number = generateTransactionId();
                $transaction_details->transaction_ref_no = $transaction->id;
                $transaction_details->merchant_id = $transaction->merchant_id;
                if (Carbon::parse($transaction_date)->addDays(1)->lt(Carbon::now())) {
                    $transaction_details->transaction_time = Carbon::parse($transaction_date)->addDays(1);
                    $transaction_details->local_transaction_time = Carbon::parse($transaction_date)->addDays(1);
                } else {
                    $transaction_details->transaction_time = Carbon::now();
                    $transaction_details->local_transaction_time = Carbon::now($transaction->timezone_name);
                }
                $transaction_details->local_transaction_year = date("Y", strtotime($transaction_details->local_transaction_time));
                $transaction_details->local_transaction_month = date("m", strtotime($transaction_details->local_transaction_time));
                $transaction_details->local_transaction_date = date("Y-m-d", strtotime($transaction_details->local_transaction_time));
                $transaction_details->scheduled_posting_date = $transaction->scheduled_posting_date;
                $transaction_details->timezone_id = $transaction->timezone_id;
                $transaction_details->amount = $total_amount;
                $transaction_details->entry_type = 'Cr';
                $transaction_details->status_id = getStatus(PROCESSED_FOR_ACHECK21);
                $transaction_details->transaction_place = ACHECK21;
                $transaction_details->acheck_document_id = $response_decoded['documentId'];
                $transaction_details->save();
                DB::commit();
                Log::channel('transaction-scheduler')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction details was stored successfully for merchant id: " . $transaction->merchant_id);
                $this->info("Transaction details was stored successfully for merchant id: " . $transaction->merchant_id);
                //store transaction details into transaction table
            } catch (\Exception $e) {
                Log::channel('transaction-scheduler')->error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured during Transaction", [EXCEPTION => $e]);
                // DB::rollback();
                continue;
            }
        }
    }

    /**
     * Create merchant end Debit transaction with all the commission amount
     */
    private function _createMerchantDebitTransaction($transaction_date, $merchant_id)
    {
        $transactions = MerchantStores::join('terminal_master', 'terminal_master.merchant_store_id', '=', 'merchant_stores.id')
            ->join('transaction_details', 'terminal_master.id', '=', 'transaction_details.terminal_id')
            ->join('registered_merchant_master', 'registered_merchant_master.id', '=', 'merchant_stores.merchant_id')
            ->join('user_bank_account_info', 'user_bank_account_info.merchant_id', '=', 'merchant_stores.merchant_id')
            ->leftJoin('timezone_masters', 'timezone_masters.id', '=', 'transaction_details.timezone_id')
            ->select(DB::raw('count(*) as count'), DB::raw('SUM(transaction_details.amount) as sum'), DB::raw('IFNULL(SUM(transaction_details.tip_amount),0) as tip_sum'), 'user_bank_account_info.*', 'registered_merchant_master.merchant_name', 'timezone_masters.timezone_name', 'merchant_stores.store_id as store_id', 'transaction_details.local_transaction_time', 'transaction_details.scheduled_posting_date')
            ->whereRaw('transaction_details.transaction_ref_no is null and transaction_details.merchant_id is null and transaction_details.consumer_id is not null')
            ->where('transaction_details.status_id', getStatus(PENDING))
            ->where('transaction_details.is_v1', 0)
            ->where('registered_merchant_master.id', $merchant_id)
            ->whereRaw("transaction_details.scheduled_posting_date = ?", [$transaction_date])
            ->whereRaw('transaction_details.transaction_ref_no is null AND transaction_details.merchant_id is null and transaction_details.consumer_id is not null AND transaction_details.id NOT IN (SELECT COALESCE(transaction_details.transaction_ref_no,0) FROM transaction_details inner join `status_master` on `transaction_details`.`status_id` = `status_master`.`id` WHERE `status_master`.`code` IN (' . RETURNED . '))')
            ->groupBy('merchant_stores.merchant_id')
            ->get();

        DB::beginTransaction();
        foreach ($transactions as $transaction) {
            try {
                $fee = RegisteredMerchantMaster::find($transaction->merchant_id);
                //create the transaction through acheck21
                $this->info("Posting merchant debit transaction to acheck21 for merchant id: " . $transaction->merchant_id);
                Log::channel('transaction-scheduler')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Posting merchant debit transaction to acheck21 for merchant id: " . $transaction->merchant_id);
                //calculate the commission amount that needs to be debited from the merchant account
                //volumn calculation
                $total_amount = ($transaction->sum + $transaction->tip_sum);
                if ($fee->volume_value_type == PERCENTAGE) {
                    $fee_percentage = ($fee->volume_value / 100);
                    $volume_amount = $total_amount * $fee_percentage;
                } else {
                    $volume_amount = $total_amount * $fee->volume_value;
                }
                $rounded_up_volume_amount = round_up($volume_amount, 2);
                //per transaction calculation
                if ($fee->per_transaction_value_type == PERCENTAGE) {
                    $fee_percentage = ($fee->per_transaction_value / 100);
                    $per_transaction_amount = $transaction->count * $fee_percentage;
                } else {
                    $per_transaction_amount = $transaction->count * $fee->per_transaction_value;
                }
                $per_transaction_amount = round($per_transaction_amount, 2);
                $params['amount'] = $per_transaction_amount + $rounded_up_volume_amount;
                $params['merchant_id'] = $transaction->merchant_id;
                $params['acheck_account_id'] = ENV('CANPAY_FEES_ID');
                // sending store id and transaction date time as timestamp to create the check number for acheck21 posting
                $params['store_id'] = $transaction->store_id;
                $params['transaction_time'] = strtotime($transaction->local_transaction_time);

                if (env('ACHECK_POSTING')) {
                    //calling the factory function to create merchant debit transaction into acheck21
                    $response = $this->transaction->createMerchantFeeTransaction($params);
                    $response_decoded = json_decode($response, true);
                    Log::channel('transaction-scheduler')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is enabled. Posting in ACHECK21.");
                } else {
                    $response_decoded['documentId'] = rand(********, ********);
                    Log::channel('transaction-scheduler')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is disabled. Continue with a random number.");
                }
                //create a new transaction
                $transaction_details = new TransactionDetails();
                $transaction_details->transaction_number = generateTransactionId();
                $transaction_details->transaction_ref_no = $transaction->id;
                $transaction_details->merchant_id = $transaction->merchant_id;
                if (Carbon::parse($transaction_date)->addDays(1)->lt(Carbon::now())) {
                    $transaction_details->transaction_time = Carbon::parse($transaction_date)->addDays(1);
                    $transaction_details->local_transaction_time = Carbon::parse($transaction_date)->addDays(1);
                } else {
                    $transaction_details->transaction_time = Carbon::now();
                    $transaction_details->local_transaction_time = Carbon::now($transaction->timezone_name);
                }
                $transaction_details->local_transaction_year = date("Y", strtotime($transaction_details->local_transaction_time));
                $transaction_details->local_transaction_month = date("m", strtotime($transaction_details->local_transaction_time));
                $transaction_details->local_transaction_date = date("Y-m-d", strtotime($transaction_details->local_transaction_time));
                $transaction_details->scheduled_posting_date = $transaction->scheduled_posting_date;
                $transaction_details->timezone_id = $transaction->timezone_id;
                $transaction_details->amount = $params['amount'];
                $transaction_details->status_id = getStatus(PROCESSED_FOR_ACHECK21);
                $transaction_details->transaction_place = ACHECK21;
                $transaction_details->acheck_document_id = $response_decoded['documentId'];
                $transaction_details->save();
                DB::commit();
                Log::channel('transaction-scheduler')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction details was stored successfully for merchant id: " . $transaction->merchant_id);
                $this->info("Transaction details was stored successfully for merchant id: " . $transaction->merchant_id);
                //store transaction details into transaction table
            } catch (\Exception $e) {
                Log::channel('transaction-scheduler')->error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured during Transaction", [EXCEPTION => $e]);
                // DB::rollback();
                continue;
            }
        }
    }
    /**
     * Creates CanPay end transactions along with the accumulated commission amount
     */
    private function _createCanPayTransaction($transaction_date, $merchant_id)
    {
        $transactions = MerchantStores::join('terminal_master', 'terminal_master.merchant_store_id', '=', 'merchant_stores.id')
            ->join('transaction_details', 'terminal_master.id', '=', 'transaction_details.terminal_id')
            ->join('registered_merchant_master', 'registered_merchant_master.id', '=', 'merchant_stores.merchant_id')
            ->join('user_bank_account_info', 'user_bank_account_info.merchant_id', '=', 'merchant_stores.merchant_id')
            ->leftJoin('timezone_masters', 'timezone_masters.id', '=', 'transaction_details.timezone_id')
            ->select(DB::raw('count(*) as count'), DB::raw('sum(transaction_details.amount) as sum'), DB::raw('sum(transaction_details.tip_amount) as tip_sum'), 'timezone_masters.timezone_name', 'merchant_stores.merchant_id')
            ->whereRaw('transaction_details.transaction_ref_no is null and transaction_details.merchant_id is null and transaction_details.consumer_id is not null')
            ->where('transaction_details.status_id', getStatus(PENDING))
            ->where('transaction_details.is_v1', 0)
            ->where('registered_merchant_master.id', $merchant_id)
            ->whereRaw("transaction_details.scheduled_posting_date = ?", [$transaction_date])
            ->whereRaw('transaction_details.transaction_ref_no is null AND transaction_details.merchant_id is null and transaction_details.consumer_id is not null AND transaction_details.id NOT IN (SELECT COALESCE(transaction_details.transaction_ref_no,0) FROM transaction_details inner join `status_master` on `transaction_details`.`status_id` = `status_master`.`id` WHERE `status_master`.`code` IN (' . RETURNED . '))')
            ->groupBy('merchant_stores.merchant_id')
            ->get();
        $total_fee = 0;
        DB::beginTransaction();
        try {
            foreach ($transactions as $transaction) {
                $fee = RegisteredMerchantMaster::find($transaction->merchant_id);
                $total_amount = ($transaction->sum + $transaction->tip_sum);
                //calculate the commission amount that needs to be debited from the merchant account
                //volumn calculation
                if ($fee->volume_value_type == PERCENTAGE) {
                    $fee_percentage = ($fee->volume_value / 100);
                    $volume_amount = $total_amount * $fee_percentage;
                } else {
                    $volume_amount = $total_amount * $fee->volume_value;
                }
                $rounded_up_volume_amount = round_up($volume_amount, 2);
                //per transaction calculation
                if ($fee->per_transaction_value_type == PERCENTAGE) {
                    $fee_percentage = ($fee->per_transaction_value / 100);
                    $per_transaction_amount = $transaction->count * $fee_percentage;
                } else {
                    $per_transaction_amount = $transaction->count * $fee->per_transaction_value;
                }
                $per_transaction_amount = round($per_transaction_amount, 2);
                $total_fee = $total_fee + $per_transaction_amount + $rounded_up_volume_amount;
            }
            //create the transaction through acheck21
            Log::channel('transaction-scheduler')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Posting merchant transaction to acheck21 for CanPay Commission");
            $params['amount'] = -$total_fee;
            $params['acheck_account_id'] = ENV('CANPAY_DEPOSIT');

            if (env('ACHECK_POSTING')) {
                //calling the factory function to create canpay credit transaction into acheck21
                $response = $this->transaction->createCanPayTransaction($params);
                $response_decoded = json_decode($response, true);
                Log::channel('transaction-scheduler')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is enabled. Posting in ACHECK21.");
            } else {
                $response_decoded['documentId'] = rand(********, ********);
                Log::channel('transaction-scheduler')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is disabled. Continue with a random number.");
            }
            //create a new transaction
            $transaction_details = new TransactionDetails();
            $transaction_details->transaction_number = generateTransactionId();
            if (Carbon::parse($transaction_date)->addDays(1)->lt(Carbon::now())) {
                $transaction_details->transaction_time = Carbon::parse($transaction_date)->addDays(1);
                $transaction_details->local_transaction_time = Carbon::parse($transaction_date)->addDays(1);
            } else {
                $transaction_details->transaction_time = Carbon::now();
                $transaction_details->local_transaction_time = Carbon::now($transaction->timezone_name);
            }
            $transaction_details->local_transaction_year = date("Y", strtotime($transaction_details->local_transaction_time));
            $transaction_details->local_transaction_month = date("m", strtotime($transaction_details->local_transaction_time));
            $transaction_details->local_transaction_date = date("Y-m-d", strtotime($transaction_details->local_transaction_time));
            $transaction_details->scheduled_posting_date = $transaction_details->scheduled_posting_date;
            $transaction_details->timezone_id = $transaction->timezone_id;
            $transaction_details->amount = $total_fee;
            $transaction_details->entry_type = 'Cr';
            $transaction_details->status_id = getStatus(PROCESSED_FOR_ACHECK21);
            $transaction_details->transaction_place = ACHECK21;
            $transaction_details->acheck_document_id = $response_decoded['documentId'];
            $transaction_details->isCanpay = 1;
            $transaction_details->save();
            DB::commit();
            Log::channel('transaction-scheduler')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction details was stored successfully for CanPay Commission");
            $message = trans('message.transaction_success');
            return renderResponse(SUCCESS, $message, null);
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured during Transaction", [EXCEPTION => $e]);
            DB::rollback();
            $message = trans('message.transaction_error');
            return renderResponse(FAIL, $message, null); // Exception Returned
        }
    }
}
