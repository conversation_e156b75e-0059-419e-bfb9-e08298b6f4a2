<?php

namespace App\Console\Commands;

use App\Models\Reward;
use App\Models\UserCurrentRewardDetail;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UpdateRewardTransactionIds extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:rewardtransactions';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command updates the reward points rewards having V1 Transaction IDs.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Log::channel('update-reward-points')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . ": Fetching Rewards having V1 Transaction IDs,,,");
        $pending = getStatus(PENDING);
        $success = getStatus(SUCCESS);
        $year = 2021;
        $reward_sql = "SELECT r.* FROM " . env('DB_DATABASE_REWARD_WHEEL') . ".rewards r join transaction_details td on r.transaction_id = td.id where r.status_id = ? and td.local_transaction_year = ? ORDER BY td.created_at";
        $rewards = DB::select($reward_sql, [$pending, $year]);
        if (!empty($rewards)) {
            Log::channel('update-reward-points')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . ": Total number of rewards that need to be processed are: " . count($rewards));
            foreach ($rewards as $reward) {
                $transaction = $this->_fetchTransactionId($reward);
                if ($transaction[0]->status_id == $success) {
                    Log::channel('update-reward-points')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . ": Transaction Status is Success, proceeding to update reward points.");
                    $this->_updateRewardPoints($transaction, $reward);
                } else {
                    Log::channel('update-reward-points')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . ":  Transaction Status ID: " . $transaction[0]->status_id . ", cannot update reward points.");
                }
            }
        } else {
            Log::channel('update-reward-points')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . ": No Rewards found to update Transaction IDs.");
        }
    }

    /**
     * _fetchTransactionId
     * Fetch the Transaction ID of the same date as Reward date.
     * @param  mixed $reward
     * @return void
     */
    private function _fetchTransactionId($reward)
    {
        Log::channel('update-reward-points')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . ": Fetching Transaction ID of Reward : " . $reward->id);
        $active = getStatus(ACTIVE);
        $transaction_sql = "SELECT * from transaction_details WHERE date(created_at) = ? AND consumer_id = ? AND transaction_ref_no IS NULL ORDER BY created_at DESC LIMIT 1";
        $transaction = DB::select($transaction_sql, [date("Y-m-d", strtotime($reward->created_at)), $reward->user_id]);

        if (empty($transaction)) {
            $transaction_sql = "SELECT * from transaction_details WHERE date(created_at) < ? AND consumer_id = ? AND transaction_ref_no IS NULL ORDER BY created_at DESC LIMIT 1";
            $transaction = DB::select($transaction_sql, [date("Y-m-d", strtotime($reward->created_at)), $reward->user_id]);
        }

        // Update the Reward with Transaction IDs
        Reward::where('id', $reward->id)->update(['transaction_id' => $transaction[0]->id]);

        // Update the Free Spin with Transaction IDs
        Reward::where(['transaction_id' => $reward->transaction_id, 'status_id' => $active])->update(['transaction_id' => $transaction[0]->id]);

        Log::channel('update-reward-points')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . ": Transaction ID for Reward : " . $reward->id . " is : " . $transaction[0]->id);
        return $transaction;
    }

    /**
     * _updateRewardPoints
     * Update Reward to Active if Reward exists from this Transaction
     * @param  mixed $transaction
     * @return void
     */
    private function _updateRewardPoints($transaction, $reward)
    {
        $active = getStatus(ACTIVE);
        $reward_inactive = getStatus(REWARD_WHEEL_INACTIVE);

        $reward_status = [$active, $reward_inactive];
        // Check if Reward Exists against this Transaction ID
        $rewards = Reward::join('user_reward_usage_history', 'rewards.id', '=', 'user_reward_usage_history.reward_id')
            ->join('reward_wheels', 'reward_wheels.id', '=', 'user_reward_usage_history.reward_wheel_id')
            ->select('rewards.*', 'user_reward_usage_history.reward_point', 'user_reward_usage_history.reward_amount', 'user_reward_usage_history.is_generic_point')
            ->where(['rewards.id' => $reward->id])
            ->whereIn('reward_wheels.status_id', $reward_status)
            ->get();
        if (!empty($rewards)) {
            foreach ($rewards as $reward) {
                // Update Reward to Active status after the Transaction is success
                $reward_update = Reward::find($reward->id);
                $reward_update->status_id = $active;
                $reward_update->save();

                // Update Reward points
                $user_current_reward_details = UserCurrentRewardDetail::where(['user_id' => $reward->user_id, 'is_generic_point' => $reward->is_generic_point])->whereNull('sponsor_link_id')->whereNull('campaign_id');
                $reward->is_generic_point == 0 ? $user_current_reward_details->where('corporate_parent_id', $reward->corporate_parent_id) : '';
                $user_current_reward_details = $user_current_reward_details->first();
                if (!empty($user_current_reward_details)) {
                    $user_current_reward_details->increment('reward_amount', $reward->reward_amount);
                    $user_current_reward_details->increment('reward_point', $reward->reward_point);
                } else {
                    $user_current_reward_details = new UserCurrentRewardDetail();
                    if ($reward->is_generic_point == 0) {
                        $user_current_reward_details->corporate_parent_id = $reward->corporate_parent_id;
                    }
                    $user_current_reward_details->user_id = $reward->user_id;
                    $user_current_reward_details->reward_amount = $reward->reward_amount;
                    $user_current_reward_details->reward_point = $reward->reward_point;
                    $user_current_reward_details->is_generic_point = $reward->is_generic_point;
                    $user_current_reward_details->save();
                }

                $this->info("Reward Status updated to Status ID: " . $active . " for transaction ID: " . $transaction[0]->id);
                Log::channel('update-reward-points')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Reward Status updated to Status ID: " . $active . " for transaction ID: " . $transaction[0]->id);
            }
        } else {
            Log::channel('update-reward-points')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . ": No Reward found for Transaction ID: " . $transaction[0]->id);
        }
    }
}
