<?php
namespace App\Console\Commands;

use App\Http\Clients\Acheck21HttpClient;
use App\Http\Factories\Transaction\TransactionFactory;
use App\Models\TransactionDetails;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DeleteTransactionFromAcheck21 extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'delete:transaction {--transaction_ids=}';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command deletes duplicate transactions posted into acheck21.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->transaction = new TransactionFactory();
        $this->acheck = new Acheck21HttpClient();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->info("Deleting duplicate transactions...");
        $id_array = explode(",", $this->option('transaction_ids'));
        Log::channel('post-void-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Deleting duplicate transactions from acheck21!!");
        $transactions = TransactionDetails::join('terminal_master', 'terminal_master.id', '=', 'transaction_details.terminal_id')->join('merchant_stores', 'merchant_stores.id', '=', 'terminal_master.merchant_store_id')->join('registered_merchant_master', 'registered_merchant_master.id', '=', 'merchant_stores.merchant_id')->select('transaction_details.*','registered_merchant_master.acheck_account_id')->whereIn('transaction_details.id', $id_array)->get();
        if (sizeof($transactions) != 0) {
            foreach ($transactions as $transaction) {
                $params['acheck_account_id'] = $transaction->acheck_account_id;
                $params['acheck_document_id'] = $transaction->acheck_document_id;
                $response = $this->acheck->deleteTransaction($params);
                if ($response == 204) {
                    Log::channel('post-void-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Deleted transaction with documentId: " . $transaction->acheck_document_id ." from acheck21.");
                    TransactionDetails::find($transaction->id)->delete();
                    Log::channel('post-void-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Deleted transaction from DB with id: " . $transaction->id);
                }else{
                    Log::channel('post-void-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Could not delete transaction with documentId: " . $transaction->acheck_document_id ." from acheck21.");
                    Log::channel('post-void-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Coould not delete transaction from DB with id: " . $transaction->id);
                }
            }
        }
        Log::channel('post-void-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "All transactions deleted successfully.");
        $this->info("All transactions deleted successfully.");
    }
}
