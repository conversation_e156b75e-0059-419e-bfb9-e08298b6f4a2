<?php
namespace App\Console\Commands;

use App\Http\Clients\Acheck21HttpClient;
use App\Models\DailyRepresentment;
use App\Models\TransactionDetails;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ScheduleFetchDailyRepresentment extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'schedule:fetchrepresentments';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command add the daily representments into daily_representments table.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->acheck = new Acheck21HttpClient();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->info("Fetching daily reresentments...");
        Log::channel('fetch-daily-representments')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Fetching daily representment from Acheck21...");

        $response = $this->acheck->getDailyRepresentments();
        $response_decoded = json_decode($response, true);

        if(!empty($response_decoded)){
            foreach($response_decoded as $val_representment){
                //Fetch the transaction ID
                $transaction_detail = TransactionDetails::select('transaction_ref_no')->where(['acheck_document_id' => $val_representment['documentId']])->first();
                // Add representment into db
                $representment = new DailyRepresentment;
                $representment->transaction_id = !empty($transaction_detail) ? $transaction_detail->transaction_ref_no : NULL;
                $representment->document_id = $val_representment['documentId'];
                $representment->client_id = $val_representment['clientId'];
                $representment->client_tag = $val_representment['clientTag'];
                $representment->individual_name = $val_representment['individualName'];
                $representment->company_name = $val_representment['companyName'];
                $representment->account_type = $val_representment['accountType'];
                $representment->amount = $val_representment['amount'];
                $representment->routing_number = $val_representment['routingNumber'];
                $representment->account_number = $val_representment['accountNumber'];
                $representment->check_number = $val_representment['checkNumber'];
                $representment->entry_class = $val_representment['entryClass'];
                $representment->entry_description = $val_representment['entryDescription'];
                $representment->received_on = Carbon::parse($val_representment['receivedOn'])->format('Y-m-d');
                $representment->effective_date = Carbon::parse($val_representment['effectiveDate'])->format('Y-m-d');
                $representment->error = $val_representment['error'];
                $representment->current_state = $val_representment['currentState'];
                $representment->save();

                Log::channel('fetch-daily-representments')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Canpay representment was stored successfully for Document ID: " . $val_representment['documentId']);
                $this->info("CanPay representment was stored successfully for Document ID: " . $val_representment['documentId']);
            }
        }else{
            Log::channel('fetch-daily-representments')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No representments found.");
            $this->info("No representments found.");
        }
    }
}
