<?php
namespace App\Console\Commands;

use App\Http\Factories\GlobalRadar\GlobalRadarFactory;
use App\Models\GlobalRadarReview;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ScheduleGlobalRadarReview extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'schedule:review';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command calls the Global Radar API and posts all the consumer details for review.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->globalradar = new GlobalRadarFactory();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->_reviewConsumer();
    }
    /**
     * This function fetches all the valid consumers are posts the review request into Global Radar
     * Case 1: Include Consumers who have initiated a trasnaction within the last mentioned(variable defined in the env) days
     * Case 2: Don't include consumers who have enrolled into the system in last mentioned(variable defined in the env) days
     */
    private function _reviewConsumer()
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "STARTING CONSUMER MONTHLY REVIEW USING GLOBAL RADAR");
        $consumers = User::join('transaction_details', 'transaction_details.consumer_id', '=', 'users.user_id')
            ->join('user_roles', 'user_roles.role_id', '=', 'users.role_id')
            ->join('status_master', 'status_master.id', '=', 'users.status')
            ->whereRaw('transaction_details.transaction_time >= (DATE_SUB(CURDATE(), INTERVAL ' . env('GLOBAL_RADAR_PURCHASE_WITHIN_DAYS') . ' DAY)) and users.created_at < (DATE_SUB(CURDATE(), INTERVAL ' . env('GLOBAL_RADAR_ENROLLMENT_WITHIN_DAYS') . ' DAY))')
            ->where('status_master.status', USER_ACTIVE)
            ->get();
        try {
            if (!$consumers->isEmpty()) {
                foreach ($consumers as $consumer) {
                    $this->_performGlobalRadarReview($consumer);
                }
            }
            $message = trans('message.transaction_success');
            return renderResponse(SUCCESS, $message, null);
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured during consumer monthly review", [EXCEPTION => $e]);
            DB::rollback();
            $message = trans('message.transaction_error');
            return renderResponse(FAIL, $message, null); // Exception Returned
        }
    }

    /**
     * This function performs golbar radar review for the current consumer
     */
    private function _performGlobalRadarReview($user)
    {
        $params['firstName'] = $user->first_name;
        $params['lastName'] = $user->last_name;
        $params['address'] = $user->street_address;
        $params['city'] = $user->city;
        $params['state'] = $user->state;
        $params['zip'] = $user->zipcode;
        // validate user informations using global radar
        $response = $this->globalradar->checkConsumerIDForFraudDetection($params);
        // based on the result returned from gobal radar the data will be stored into global_radar_review table
        $global_radar_data = new GlobalRadarReview();
        $global_radar_data->response_returned = $response;
        $global_radar_data->user_id = $user->user_id;
        $response_decoded = json_decode($response, true);

        // if global radar validation failed then return proper messaging and save data with pending status.
        if ($response_decoded['found'] === true && $response_decoded['status'] != 'ok') {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Global Radar Review Failed. Manual Validation Required.");
            $global_radar_data->status = getStatus(PENDING);
            $global_radar_data->description = "Global Radar Review Failed. Manual Validation Required.";
            $global_radar_data->save();
            return false;
        }
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Global Radar Review Succeeded.");
        $global_radar_data->status = getStatus(APPROVED);
        $global_radar_data->description = "Global Radar Review Succeeded.";
        $global_radar_data->save();
        return true;
    }
}
