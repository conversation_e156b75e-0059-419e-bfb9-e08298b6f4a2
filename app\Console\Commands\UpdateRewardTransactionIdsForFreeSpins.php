<?php

namespace App\Console\Commands;

use App\Models\Reward;
use App\Models\UserCurrentRewardDetail;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UpdateRewardTransactionIdsForFreeSpins extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:rewardtransactionsforfreespins';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command updates the Transaction IDs for Free Spins not having not used yet.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Log::channel('update-reward-points')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . ": Fetching Free Spins Rewards having V1 Transaction IDs...");
        $reward_sql = "SELECT r.* from " . env('DB_DATABASE_REWARD_WHEEL') . ".rewards r join transaction_details td ON r.transaction_id = td.id
        WHERE td.is_v1 = 1 ORDER BY r.created_at desc";
        $rewards = DB::select($reward_sql);
        if (!empty($rewards)) {
            Log::channel('update-reward-points')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . ": Total number of rewards that need to be processed are: " . count($rewards));
            foreach ($rewards as $reward) {
                $this->_fetchTransactionId($reward);
            }
        } else {
            Log::channel('update-reward-points')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . ": No Rewards found to update Transaction IDs.");
        }
    }

    /**
     * _fetchTransactionId
     * Fetch the Transaction ID of the same date as Reward date.
     * @param  mixed $reward
     * @return void
     */
    private function _fetchTransactionId($reward)
    {
        Log::channel('update-reward-points')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . ": Fetching Transaction ID of Reward : " . $reward->id);
        $active = getStatus(ACTIVE);
        $transaction_sql = "SELECT * from transaction_details WHERE date(created_at) = ? AND consumer_id = ? AND transaction_ref_no IS NULL ORDER BY created_at DESC LIMIT 1";
        $transaction = DB::select($transaction_sql, [date("Y-m-d", strtotime($reward->created_at)), $reward->user_id]);

        if (empty($transaction)) {
            $transaction_sql = "SELECT * from transaction_details WHERE date(created_at) < ? AND consumer_id = ? AND transaction_ref_no IS NULL ORDER BY created_at DESC LIMIT 1";
            $transaction = DB::select($transaction_sql, [date("Y-m-d", strtotime($reward->created_at)), $reward->user_id]);
        }

        // Update the Reward with Transaction IDs
        Reward::where('id', $reward->id)->update(['transaction_id' => $transaction[0]->id]);

        // Update the Free Spin with Transaction IDs
        Reward::where(['transaction_id' => $reward->transaction_id, 'status_id' => $active])->update(['transaction_id' => $transaction[0]->id]);

        Log::channel('update-reward-points')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . ": Transaction ID for Reward : " . $reward->id . " is : " . $transaction[0]->id);
        return $transaction;
    }
}
