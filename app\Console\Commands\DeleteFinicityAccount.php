<?php

namespace App\Console\Commands;

use App\Http\Factories\Finicity\FinicityFactory;
use App\Models\BankAccountInfo;
use App\Models\FinicityCustomerDeleteHistory;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DeleteFinicityAccount extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'delete:finicityaccount';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'If the direct link consumer does not make any transaction within 90 days then the finicity account will be deleted.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->finicityFactory = new FinicityFactory();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->info("Deleting finicity account...");
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Deleting finicity account...");

        $interval = env('FINICITY_CONSUMER_DELETE_TRANSACTION_INTERVAL');
        $start_date = env('FINICITY_CONSUMER_DELETE_FROM_DATE'). ' 00:00:00';
        $end_date = env('FINICITY_CONSUMER_DELETE_TO_DATE'). ' 23:59:59';

        $user_sql = "SELECT ubai.finicity_id, u.phone, u.user_id
                    FROM users u
                    JOIN user_bank_account_info ubai ON ubai.user_id = u.user_id AND ubai.finicity_id IS NOT NULL AND ubai.finicity_id !=''
                    LEFT JOIN finicity_customer_delete_history fcdh ON fcdh.consumer_id = u.user_id
                    LEFT JOIN transaction_details td ON td.consumer_id = u.user_id AND td.local_transaction_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
                    WHERE fcdh.consumer_id IS NULL AND td.consumer_id IS NULL AND (
                        (u.existing_user = 1 AND u.migrated_at BETWEEN ? AND ?)
                        OR
                        (u.existing_user = 0 AND u.created_at BETWEEN ? AND ?)
                    ) 
                    GROUP BY u.user_id
                    ORDER BY u.created_at";
                    
        $users = DB::connection(MYSQL_RO)->select($user_sql, [$interval, $start_date, $end_date, $start_date, $end_date]);
        if (count($users) > 0) {
            $this->info(count($users) . " users found for finicity customer delete.");
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . count($users) . " users found for finicity customer delete.");
            $bank_delink = getStatus(BANK_DELINK);
            foreach ($users as $user) {
                // Delete finicity account
                $response = $this->finicityFactory->deleteCustomer($user->finicity_id, $user->phone);
                if ($response == ACTIVE) {
                    $history['consumer_id'] = $user->user_id;
                    $history['finicity_id'] = $user->finicity_id;
                    FinicityCustomerDeleteHistory::create($history);
                    // Update active direct link accounts to Delinked state
                    BankAccountInfo::where(['user_id' => $user->user_id, 'finicity_id' => $user->finicity_id])->update(['status' => $bank_delink]);
                    DB::commit();
                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer account deleted for Consumer Phone: " . $user->phone . " and Account ID: " . $user->finicity_id);
                } else {
                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer account delete failed for Consumer Phone: " . $user->phone . " and Account ID: " . $user->finicity_id);
                }
            }
        } else {
            $this->info("User not found for finicity customer delete.");
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "User not found for finicity customer delete");
        }
        $this->info("Finicity Account deleted successfully.");
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Finicity Account deleted successfully.");
    }


}
