<?php


namespace App\Http\Controllers;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Models\ShortUrl;

class ShortUrlController extends Controller
{
    public function store(Request $request)
    {
        // Validating input requests
        $this->validate($request, [
            'url' => 'required|url'
        ]);

        $code = createShortUrl($request->url);
        $message = trans('message.short_url_created');
        return renderResponse(SUCCESS, $message, $code);
    }

    public function show($code)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Looking up code: " . $code);

        $short = ShortUrl::where('code', $code)->first();

        if (!$short) {
            $message = trans('message.short_url_not_found');
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Code not found: " . $code);
            return renderResponse(FAILED, $message, null);
        }

        $message = trans('message.short_url_found');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Found URL: " . $short->original_url);

        return renderResponse(SUCCESS, $message, $short->original_url);
    }
}
