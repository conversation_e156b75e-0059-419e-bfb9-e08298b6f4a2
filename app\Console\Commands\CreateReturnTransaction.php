<?php
namespace App\Console\Commands;

use App\Models\TransactionDetails;
use App\Models\ReturnReasonMaster;
use App\Models\TimezoneMaster;
use Illuminate\Support\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CreateReturnTransaction extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'create:returntransaction {--transaction_number=} {--return_code=} {--returned_on=}';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will create return transaction based on inputs';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        if (ENV('APP_ENV') == 'production') {
            $this->error("Application is in production. This command is only for sandbox environment.");
            Log::error(__METHOD__ . "(" . __LINE__ . ") - " . "Application is in production. This command is only for sandbox environment.");
            return;
        }

        $transaction_number = $this->option('transaction_number');
        $return_code = $this->option('return_code');
        $returned_on = $this->option('returned_on');
        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Creating return transaction for Transaction number:" . $transaction_number);
        $this->info("Creating return transaction for Transaction number: " . $transaction_number);
        try {
            $transaction_data = TransactionDetails::where('transaction_number', $transaction_number)->whereNull('transaction_ref_no')->first();
            $returned = getStatus(RETURNED);
            $return_reason = ReturnReasonMaster::where('reason_code', $return_code)->first();
            if (empty($return_reason)) {
                $return_reason = ReturnReasonMaster::where('reason_code', 'D100')->first();
                Log::channel('return-transaction')->info(__METHOD__ . "(" . __LINE__ . ") - Unknown return Code: " . $return_code . " received for Transaction ID: " . $transaction_number . ". Hence continuing with the deafult D100 return code.");
            }
            // updating the approve flag to one to indicate that transaction does not need consumer approval
            $approval_to_represent = $return_reason->canpay_represent == 1 && $transaction_data->represent_count < 2 ? 1 : 0;

            TransactionDetails::where('id', $transaction_data->id)->update(array('is_represented' => 0, 'status_id' => $returned, 'return_reason' => $return_reason->id, 'returned_on' => Carbon::parse($returned_on), 'approved_to_represent' => $approval_to_represent, 'consumer_approval' => $approval_to_represent, 'represent_block' => 0, 'approved_by_admin' => 0, 'transaction_returned' => $transaction_data->account_id));

            //create a new row with the returned status
            //create a new transaction
            $transaction_details = new TransactionDetails();
            $transaction_details->transaction_number = $transaction_data->transaction_number;
            $transaction_details->transaction_ref_no = $transaction_data->id;
            $transaction_details->user_id = $transaction_data->user_id;
            $transaction_details->consumer_id = $transaction_data->consumer_id;
            $transaction_details->terminal_id = $transaction_data->terminal_id;
            $transaction_details->transaction_time = Carbon::now();
            //get timezone name
            $timezone = TimezoneMaster::find($transaction_data->timezone_id);
            $transaction_details->local_transaction_time = Carbon::now($timezone->timezone_name);
            $transaction_details->local_transaction_year = date("Y", strtotime($transaction_details->local_transaction_time));
            $transaction_details->local_transaction_month = date("m", strtotime($transaction_details->local_transaction_time));
            $transaction_details->local_transaction_date = date("Y-m-d", strtotime($transaction_details->local_transaction_time));
            $transaction_details->timezone_id = $transaction_data->timezone_id;
            $transaction_details->consumer_bank_posting_amount = $transaction_data->consumer_bank_posting_amount;
            $transaction_details->reward_amount_used = $transaction_data->reward_amount_used;
            $transaction_details->reward_point_used = $transaction_data->reward_point_used;
            $transaction_details->amount = $transaction_data->amount;
            $transaction_details->tip_amount = $transaction_data->tip_amount;
            $transaction_details->tip_type = $transaction_data->tip_type;
            $transaction_details->tip_add_time = $transaction_data->tip_add_time;
            $transaction_details->used_qr_id = $transaction_data->used_qr_id;
            $transaction_details->status_id = $returned;
            $transaction_details->return_reason = $return_reason->id;
            $transaction_details->returned_on = Carbon::parse($returned_on);
            $transaction_details->transaction_type_id = $transaction_data->transaction_type_id;
            $transaction_details->transaction_place = WEB;
            $transaction_details->save();
            $this->info("Return created for Transaction number: " . $transaction_number);
        } catch (\Exception$e) {
            Log::channel('return-transaction')->error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured while trying to create return for the transaction.", [EXCEPTION => $e]);
            $message = trans('message.db_transaction_fail');
            $this->info($message); // Exception Returned
        }
    }
}
