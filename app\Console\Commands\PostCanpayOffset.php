<?php

namespace App\Console\Commands;

use App\Http\Factories\Transaction\TransactionFactory;
use App\Models\TransactionDetails;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class PostCanpayOffset extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'post:canpayoffset {scheduled_posting_date} {amount}';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will post the canpay offset to acheck21';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->transaction = new TransactionFactory();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Log::channel('transaction-scheduler')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Canpay Offset Posting Started...");
        $amount = $this->argument('amount');
        $params['amount'] = -$amount;
        $params['acheck_account_id'] = env('CANPAY_DEPOSIT');
        //calling the factory function to create consumer transaction into acheck21
        if (env('ACHECK_POSTING')) {
            $response = $this->transaction->createCanPayTransaction($params);
            $response_decoded = json_decode($response, true);
            Log::channel('transaction-scheduler')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is enabled. Posting in ACHECK21.");
        } else {
            $response_decoded['documentId'] = rand(********, ********);
            Log::channel('transaction-scheduler')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is disabled. Continue with a random number.");
        }
        if (isset($response_decoded['documentId'])) {
            //create a new transaction
            $transaction_details = new TransactionDetails();
            $transaction_details->transaction_number = generateTransactionId();
            $transaction_details->transaction_time = Carbon::now();
            $transaction_details->local_transaction_time = Carbon::now();
            $transaction_details->local_transaction_year = date("Y");
            $transaction_details->local_transaction_month = date("m");
            $transaction_details->local_transaction_date = date("Y-m-d");
            $transaction_details->amount = $amount;
            $transaction_details->tip_amount = 0;
            $transaction_details->entry_type = 'Cr';
            $transaction_details->isCanpay = 1;
            $transaction_details->status_id = getStatus(PROCESSED_FOR_ACHECK21);
            $transaction_details->transaction_place = ACHECK21;
            $transaction_details->scheduled_posting_date = $this->argument('scheduled_posting_date');
            $transaction_details->acheck_document_id = $response_decoded['documentId'];
            $transaction_details->save();

            Log::channel('transaction-scheduler')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction details was stored successfully for CanPay Commission for amount: " . $params['amount']);
            $this->info("Transaction details was stored successfully for CanPay Commission for amount: " . $params['amount']);
        } else {
            Log::channel('transaction-scheduler')->error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "There was some problem trying to post transaction into Acheck21.");
            $this->info("There was some problem trying to post transaction into Acheck21.");
        }
    }
}
