<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CrewWheelSpinResult extends Model
{
    protected $connection = MYSQL_REWARD_WHEEL;

    /**
     * __construct
     * This function will insert an alphanumeric 32 character unique id as primary key in the table whenever a data get inserted
     * @param  mixed $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        $this->id = generateUUID();

        parent::__construct($attributes);
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'reward_wheel_id',
        'petition_id',
        'consumer_id',
        'definition_id',
        'prize_value',
        'spin_month',
        'spin_year',
        'claimed_at',
    ];

    public $incrementing = false;
    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
}
