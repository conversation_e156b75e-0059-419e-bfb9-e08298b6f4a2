<?php

namespace App\Console\Commands;

use App\Models\Reward;
use App\Models\UserCurrentRewardDetail;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class UpdateRewardPointRewardWise extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:reward {--reward_ids=}';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command updates the reward points for a aprticular reward id.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Log::channel('update-reward-points')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . ": Reward points update program started...");
        $this->_updateRewardStatus($this->option('reward_ids'));
    }

    /**
     * _updateRewardStatus
     * Update Reward to Active if Reward exists from this Transaction
     * @param  mixed $transaction
     * @return void
     */
    private function _updateRewardStatus($reward_ids)
    {
        $pending = getStatus(PENDING);
        $active = getStatus(ACTIVE);
        $reward_inactive = getStatus(REWARD_WHEEL_INACTIVE);

        $reward_status = [$active, $reward_inactive];
        // Check if Reward Exists against this ID
        $rewards = Reward::join('user_reward_usage_history', 'rewards.id', '=', 'user_reward_usage_history.reward_id')
            ->join('reward_wheels', 'reward_wheels.id', '=', 'user_reward_usage_history.reward_wheel_id')
            ->select('rewards.*', 'user_reward_usage_history.reward_point', 'user_reward_usage_history.reward_amount', 'user_reward_usage_history.is_generic_point')
            ->whereIn('rewards.id', explode(',', $reward_ids))
            ->where(['rewards.status_id' => $pending])
            ->whereIn('reward_wheels.status_id', $reward_status)
            ->get();
        if (!empty($rewards)) {
            foreach ($rewards as $reward) {
                // Update Reward to Active status after the Transaction is success
                $reward_update = Reward::find($reward->id);
                $reward_update->status_id = $active;
                $reward_update->save();

                // Update Reward points
                $user_current_reward_details = UserCurrentRewardDetail::where(['user_id' => $reward->user_id, 'is_generic_point' => $reward->is_generic_point])->whereNull('sponsor_link_id')->whereNull('campaign_id');
                $reward->is_generic_point == 0 ? $user_current_reward_details->where('corporate_parent_id', $reward->corporate_parent_id) : '';
                $user_current_reward_details = $user_current_reward_details->first();
                if (!empty($user_current_reward_details)) {
                    $user_current_reward_details->increment('reward_amount', $reward->reward_amount);
                    $user_current_reward_details->increment('reward_point', $reward->reward_point);
                } else {
                    $user_current_reward_details = new UserCurrentRewardDetail();
                    if ($reward->is_generic_point == 0) {
                        $user_current_reward_details->corporate_parent_id = $reward->corporate_parent_id;
                    }
                    $user_current_reward_details->user_id = $reward->user_id;
                    $user_current_reward_details->reward_amount = $reward->reward_amount;
                    $user_current_reward_details->reward_point = $reward->reward_point;
                    $user_current_reward_details->is_generic_point = $reward->is_generic_point;
                    $user_current_reward_details->save();
                }

                $this->info("Reward Status updated to Status ID: " . $active . " for Reward ID: " . $reward->id);
                Log::channel('update-reward-points')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Reward Status updated to Status ID: " . $active . " for Reward ID: " . $reward->id);
            }
        } else {
            Log::channel('update-reward-points')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . ": No pending reward found.");
        }
    }
}
