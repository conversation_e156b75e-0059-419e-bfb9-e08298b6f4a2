<?php
namespace App\Console\Commands;

use App\Http\Factories\FakeDataGenerator\FakeDataGeneratorFactory;
use Illuminate\Console\Command;

class CreateFakeData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'consumerfakedata:create {--consumers=}';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will generate test data based on inputs';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->fakedatagenerator = new FakeDataGeneratorFactory();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $params = [
            'no_of_consumers' => $this->option('consumers'),
        ];
        $this->info("Processing....");
        $this->fakedatagenerator->populateConsumerData($params);
        $this->info("Fake Data Generated Successfully.");
    }
}
