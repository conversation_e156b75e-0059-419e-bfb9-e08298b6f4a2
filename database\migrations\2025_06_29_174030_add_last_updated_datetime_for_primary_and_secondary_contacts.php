<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::Statement("ALTER TABLE `petitions`
            ADD COLUMN IF NOT EXISTS `primary_contact_last_updated_at` DATETIME NULL DEFAULT NULL AFTER `primary_contact_person_title`,
            ADD COLUMN IF NOT EXISTS `secondary_contact_last_updated_at` DATETIME NULL DEFAULT NULL AFTER `secondary_contact_person_title`");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
    }
};
