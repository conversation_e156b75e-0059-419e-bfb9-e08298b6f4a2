<?php
namespace App\Console\Commands;

use App\Http\Factories\Transaction\TransactionFactory;
use App\Http\Factories\Webhook\WebhookFactory;
use App\Models\StatusMaster;
use App\Models\TimezoneMaster;
use App\Models\TransactionDetails;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;

class VoidAdminDrivenNotPostedTransaction extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'expire:admindrivennotpostedtransaction';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'If transaction post is not done then after 7 days void the transaction';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->merchantWebhook = new WebhookFactory();
        $this->transaction = new TransactionFactory();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $expired_status = StatusMaster::where('code', EXPIRED)->first();
        $voided_status = getStatus(VOIDED);
        $pending_status = getStatus(PENDING);
        
        // Get the 7 days before date based on UTC time
        $transaction_date = Carbon::now()->subDays((ENV('ADMIN_DRIVEN_TRANSACTION_EXPIRY_DAYS') + 1))->toDateString();
        // Get 7 days before transaction
        $getAdminDrivenTransactions = TransactionDetails::where('transaction_ref_no', null)->where('admin_driven' , 1)->whereRaw("local_transaction_date = ?", [$transaction_date])
        ->where(function ($q1) use ($pending_status, $voided_status) {
            $q1->where('status_id', $pending_status)
                ->orWhere(function ($q2) use ($voided_status) {
                    $q2->where('status_id', $voided_status)
                        ->where('change_request', 1);
                });
        })
        ->where(function ($q1) {
            $q1->where('posting_approved_by_admin', 0)
                  ->orWhere('posting_approved_by_admin', null);
        })
        ->orderBy('transaction_details.local_transaction_time', 'DESC')->get();
        
        Log::channel('admin-driven-transaction-expired')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Not Posted Admin Driven Transaction expire start for the transaction date ".$transaction_date );
        if(count($getAdminDrivenTransactions) > 0){
            Log::channel('admin-driven-transaction-expired')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Admin Driven Not Posted Transaction Count " . count($getAdminDrivenTransactions) . "==========");
            foreach($getAdminDrivenTransactions as $transaction){
                $timezone_details = TimezoneMaster::find($transaction->timezone_id);
                $transaction_local_time = Carbon::now();
                if ($transaction->timezone_id) {
                    $transaction_local_time = Carbon::now()->timezone($timezone_details->timezone_name);
                }
                $before_void_change_request = $transaction->change_request;
                $latestTransation = $transaction;
                 // check modified transction and expire the modified transaction
                if($transaction->change_request_transaction_ref_no != null){
                    $m_transaction = TransactionDetails::where('id', $transaction->change_request_transaction_ref_no)->first();
                    if($m_transaction){
                        $latestTransation = $m_transaction;
                        $m_transaction->status_id = $expired_status->id;
                        $m_transaction->voided_time = Carbon::now();
                        $m_transaction->voided_local_time = $transaction_local_time;
                        $m_transaction->save();
                    }
                }
                //if modified transaction present then take modified id other wise parent transaction id
                $transactionId = $transaction->change_request_transaction_ref_no ? $transaction->change_request_transaction_ref_no : $transaction->id;

                $transaction->status_id = $expired_status->id;
                $transaction->voided_time = Carbon::now();
                $transaction->change_request = 0;
                $transaction->voided_local_time = $transaction_local_time;
                $transaction->save();
                
                if ($before_void_change_request != 1) {
                    // Revoke the Rewards given to the User against the Transaction ID.
                    $this->transaction->revokeRewardWrapper($latestTransation, 1);
                }
                //webhook call after transaction expire
                $this->merchantWebhook->modificationWebhookCall($transactionId, $expired_status->status);
                Log::channel('admin-driven-transaction-expired')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Admin Driven Not Posted Transaction expired successfully for transaction_id =  " . $transaction->id);
            }
        }else{
            Log::channel('admin-driven-transaction-expired')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No transactions found.");
            $this->info("No transactions found.");
        }
        Log::channel('admin-driven-transaction-expired')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Admin Driven Not Posted Transaction CRON ENDED==========");
    }


}
