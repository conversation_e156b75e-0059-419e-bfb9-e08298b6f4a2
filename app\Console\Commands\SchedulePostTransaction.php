<?php

namespace App\Console\Commands;

use App\Http\Factories\Finicity\FinicityFactory;
use App\Http\Factories\EmailExecutor\EmailExecutorFactory;
use App\Http\Factories\Transaction\TransactionFactory;
use App\Models\Acheck21DocumentIdHistory;
use App\Models\Acheck21HistoryTable;
use App\Models\TransactionDetails;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SchedulePostTransaction extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'post:transaction';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will post all the pending transactions to acheck21 for further processing into the bank.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->finicityFactory = new FinicityFactory();
        $this->transaction = new TransactionFactory();
        $this->emailexecutor = new EmailExecutorFactory();
        $this->failed = getStatus(FAILED);
        $this->pending = getStatus(PENDING);
        $this->process_for_acheck = getStatus(PROCESSED_FOR_ACHECK21);
        $this->success = getStatus(SUCCESS);
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // Check wheather the cron is enabled or disabled
        $checkCronEnabled = getSettingsValue('enable_acheck21_consumer_debit_process', 0);
        if ($checkCronEnabled == 0) {
            $this->info("NOT posting Consumer transaction data to Acheck21. It is DISABLED in settings.");
            Log::channel('post-transaction')->warning(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "NOT posting Consumer transaction data to Acheck21. It is DISABLED in settings.");
            // Send a email to the desired person stating that the cron job is disabled in settings
            $email_params = [
                'cron_job_name' => 'Hourly Transaction Posting',
            ];
            $this->emailexecutor->cronJobDisbaledAlert($email_params);
            return false;
        }

        // Check wheather new ach process is enabled or disabled
        $checkNewAchEnabled = getSettingsValue('enable_new_ach_process_for_all_merchant', 0);
        if ($checkNewAchEnabled == 1) {
            $this->info("New ACH process is enabled. NOT posting Consumer transaction data to Acheck21.");
            Log::channel('post-transaction')->warning(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "New ACH process is enabled. NOT posting Consumer transaction data to Acheck21.");
            return false;
        }

        // Get the Transaction date based on EST time
        if (Carbon::now()->timezone(ACHECK21_TIMEZONE_FOR_WEBHOOK)->lt(Carbon::now()->timezone(ACHECK21_TIMEZONE_FOR_WEBHOOK)->toDateString() . " 07:00:00")) {
            $transaction_date = Carbon::now()->timezone(ACHECK21_TIMEZONE_FOR_WEBHOOK)->subDays(1)->toDateString();
        } else {
            $transaction_date = Carbon::now()->timezone(ACHECK21_TIMEZONE_FOR_WEBHOOK)->toDateString();
        }
        Log::channel('post-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Posting Consumer transaction data to ACHECK21 for date: " . $transaction_date . " started...");

        Log::channel('post-transaction')->debug(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Checking webhook status for date: " . $transaction_date . "...");

        // Check if webhook has been called from ACHECK21 for the transaction date
        $checkForAcheckWebhook = getWebhookDataforSpecificDate($transaction_date);
        if (!empty($checkForAcheckWebhook) && Carbon::now()->gte(Carbon::parse($checkForAcheckWebhook->created_at)->addHour())) {
            // If webhook has been called and it has been past an hour from call time then start posting transactions to ACHECK21
            $this->_postTransaction($transaction_date);
        } else {
            Log::channel('post-transaction')->warning(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Did not post new transactions to ACHECK21. Webhook was not called by ACHECK21 yet for " . $transaction_date);
            $this->info("Did not post new transactions to ACHECK21. Webhook was not called by ACHECK21 yet.");
        }
    }
    /**
     * This function fetches bank balance for all consumers who has added bank from finicity and keeps record in the database
     */
    private function _postTransaction($transaction_date)
    {
        $this->info("Fetching all transactions (non post-blocked) to post into ACHECK21...");
        Log::channel('post-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Fetching all transactions (non post-blocked) to post into ACHECK21...");

        $transaction_sql = "select td.*, rmm.acheck_account_id, tmz.timezone_name
            from transaction_details td
            straight_join terminal_master tm ON tm.id=td.terminal_id
            straight_join merchant_stores ms on tm.merchant_store_id = ms.id
            straight_join registered_merchant_master rmm on rmm.id = ms.merchant_id
            straight_join timezone_masters tmz ON tmz.id = td.timezone_id
            left join transaction_details tdl force index(idx_trans_ref_no) on tdl.transaction_ref_no = td.id and tdl.status_id = ?
            WHERE
            td.is_posting_blocked = 0
            and td.is_v1 = 0
            and td.status_id = ?
            and td.scheduled_posting_date <= ?
            and tdl.transaction_ref_no is null
            and rmm.is_enabled_new_ach_process = 0
            and td.consumer_bank_posting_amount > 0 ORDER BY td.created_at";
        $transactions = DB::connection(MYSQL_RO)->select($transaction_sql, [$this->process_for_acheck, $this->pending, $transaction_date]);

        Log::channel('post-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transactions to be posted: " . count($transactions));
        if (sizeof($transactions) != 0) {
            $this->_postTransactionIntoAcheck21($transactions, false);
            Log::channel('post-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transactions Posted: " . count($transactions) . ". Finished posting to ACHECK21.");
            $this->info("Transaction posted successfully.");
        } else {
            Log::channel('post-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No transactions found.");
            $this->info("No transactions found.");
        }
    }

    /**
     * This function calls the api that posts consumer end transactions to acheck21
     * once the response returned from acheck21 it shores a new row to transaction_details table
     */
    private function _createConsumerTransaction($transaction, $data)
    {
        //create a new transaction
        $transaction_details = new TransactionDetails();
        $transaction_details->transaction_number = $transaction->transaction_number;
        $transaction_details->transaction_ref_no = $transaction->id;
        $transaction_details->user_id = $transaction->user_id;
        $transaction_details->consumer_id = $transaction->consumer_id;
        $transaction_details->terminal_id = $transaction->terminal_id;
        $transaction_details->transaction_time = $data['transaction_time'];
        $transaction_details->local_transaction_time = $data['transaction_local_time'];
        $transaction_details->local_transaction_year = date("Y", strtotime($transaction_details->local_transaction_time));
        $transaction_details->local_transaction_month = date("m", strtotime($transaction_details->local_transaction_time));
        $transaction_details->local_transaction_date = date("Y-m-d", strtotime($transaction_details->local_transaction_time));
        $transaction_details->timezone_id = $transaction->timezone_id;
        $transaction_details->scheduled_posting_date = $transaction->scheduled_posting_date;
        $transaction_details->consumer_bank_posting_amount = $transaction->consumer_bank_posting_amount;
        $transaction_details->reward_amount_used = $transaction->reward_amount_used;
        $transaction_details->reward_point_used = $transaction->reward_point_used;
        $transaction_details->amount = $transaction->amount;
        $transaction_details->tip_amount = $transaction->tip_amount;
        $transaction_details->tip_type = $transaction->tip_type;
        $transaction_details->tip_add_time = $transaction->tip_add_time;
        $transaction_details->used_qr_id = $transaction->used_qr_id;
        $transaction_details->status_id = $this->process_for_acheck;
        $transaction_details->transaction_type_id = $transaction->transaction_type_id;
        $transaction_details->transaction_place = ACHECK21;
        $transaction_details->acheck_document_id = $data['documentId'];
        $transaction_details->save();

        Log::channel('post-transaction')->debug(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction posting data stored into database successfully for Transaction ID: " . $transaction->id);

        //Add Document ID in Acheck21DocumentIdHistory table and update Previous Ignore Flags
        $this->_addDocumentIdHistory($transaction_details);
    }

    private function _postTransactionIntoAcheck21($transactions, $flag)
    {
        $records = 0;

        foreach ($transactions as $transaction) {
            $params['amount'] = $transaction->consumer_bank_posting_amount;
            $params['consumer_id'] = $transaction->consumer_id;
            $params['acheck_account_id'] = $transaction->acheck_account_id;
            $history = array(
                'transaction_id' => $transaction->id,
                'transaction_posting' => 1,
                'status_id' => $this->failed,
            );
            //calling the factory function to create consumer transaction into acheck21
            if ($params['amount'] > 0) {
                try {
                    Log::channel('post-transaction')->debug(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Posting transaction for Transaction ID: " . $transaction->id . " Amount: " . $params['amount'] . " and Merchant Acheck Account ID: " . $params['acheck_account_id']);
                    $params['account_id'] = $transaction->account_id;
                    if (env('ACHECK_POSTING')) {
                        $response = $this->transaction->createConsumerTransaction($params);
                        $response_decoded = json_decode($response, true);
                        Log::channel('post-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is enabled. Posting in ACHECK21.");
                    } else {
                        $response_decoded['documentId'] = rand(********, ********);
                        Log::channel('post-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is disabled. Continue with a random number.");
                    }
                    //check if transaction posted successfully to acheck21
                    if (isset($response_decoded['documentId'])) {
                        $response_decoded['transaction_time'] = $transaction->transaction_time;
                        $response_decoded['transaction_local_time'] = $transaction->local_transaction_time;
                        if (!$flag) {
                            $response_decoded['transaction_time'] = Carbon::now();
                            $response_decoded['transaction_local_time'] = Carbon::now($transaction->timezone_name);
                        }
                        //store transaction data into database
                        $transaction_id = $this->_createConsumerTransaction($transaction, $response_decoded);
                        // store success log into transaction posting history table
                        $history['status_id'] = $this->success;
                        Acheck21HistoryTable::create($history);
                    }
                } catch (\Exception$e) {
                    Acheck21HistoryTable::create($history);
                    // Check if this transaction failed more than 3 times
                    $failCount = Acheck21HistoryTable::where(['transaction_id' => $transaction->id, 'transaction_posting' => 1, 'status_id' => $this->failed])->count();
                    if ($failCount > 3) { // If the count is greater than 3 then send a mail
                        $transaction_details = [
                            'transaction' => $transaction,
                            'exception_message' => $e->getMessage(),
                        ];
                        $this->emailexecutor->transactionPostingFailureEmail($transaction_details);
                    }
                    Log::channel('post-transaction')->error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Exception occured during Transaction", [EXCEPTION => $e]);
                    $this->info($e); // Exception Returned
                    continue;
                    $records++;
                }
            } else {
                Log::channel('post-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Cannot post into Acheck as Transaction Amount is: " . $params['amount'] . " for Transaction ID: " . $transaction->id);
            }

            if (($records % 50) == 0) {
                Log::channel('post-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Processed $records...");
            }
        }
    }

    private function _addDocumentIdHistory($transaction)
    {
        //Update the ignore flags for the previous flags
        Acheck21DocumentIdHistory::where(['transaction_ref_no' => $transaction->transaction_ref_no])->update(['ignore_flag' => 1]);

        Log::channel('post-transaction')->debug(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Ignore Flag updated for Transation Ref No. : " . $transaction->transaction_ref_no);

        //Add new Document ID in Acheck21DocumentIdHistory table
        $document_history = new Acheck21DocumentIdHistory();
        $document_history->transaction_id = $transaction->id;
        $document_history->transaction_ref_no = $transaction->transaction_ref_no;
        $document_history->amount = $transaction->consumer_bank_posting_amount;
        $document_history->acheck_document_id = $transaction->acheck_document_id;
        $document_history->save();

        Log::channel('post-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Document ID " . $transaction->acheck_document_id . " added in acheck21_document_id_history table for Transation ID : " . $transaction->transaction_ref_no);
    }
}
