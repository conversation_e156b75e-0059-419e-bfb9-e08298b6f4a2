<?php

namespace App\Console\Commands;

use App\Models\Reward;
use App\Models\UserCurrentRewardDetail;
use App\Models\UserRewardUsageHistory;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UpdateTransactionIDForRewardUsageHistory extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:transactionidforrewardusagehistory';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will update the Transaction ID in the user_reward_usage_history table with the TRansaction ID in the REwards table.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Log::channel('update-reward-points')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . ": Fetching data where there is mismatch between Transaction ID of rewards and user_reward_usage_history table...");
        $reward_sql = "SELECT r.transaction_id as reward_transaction_id, uruh.transaction_id, uruh.id FROM " . env('DB_DATABASE_REWARD_WHEEL') . ".rewards r JOIN " . env('DB_DATABASE_REWARD_WHEEL') . ".user_reward_usage_history uruh on r.id = uruh.reward_id
                                    WHERE r.transaction_id!= uruh.transaction_id";
        $rewards = DB::select($reward_sql);
        if (!empty($rewards)) {
            Log::channel('update-reward-points')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . ": Total number of rewards that need to be processed are: " . count($rewards));
            foreach ($rewards as $reward) {
                // Update the user_reward_usage_history table with the Transaction ID in the Rewards table
                $update_reward_usage_history = UserRewardUsageHistory::find($reward->id);
                $update_reward_usage_history->transaction_id = $reward->reward_transaction_id;
                $update_reward_usage_history->save();
                Log::channel('update-reward-points')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Table update with new Transaction ID: " . $reward->reward_transaction_id);
            }
        } else {
            Log::channel('update-reward-points')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . ": No Rewards found to update Transaction IDs.");
        }
    }
}
