<?php
namespace App\Console\Commands;

use App\Http\Clients\Acheck21HttpClient;
use App\Http\Factories\EmailExecutor\EmailExecutorFactory;
use App\Models\Acheck21DocumentIdHistory;
use App\Models\ReturnRepresentHistory;
use App\Models\TransactionDetails;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class RepresentDirectLinkR01 extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'represent:directlinkedR01';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command represents returned transaction through acheck21 which are not represnted twice in last 60 days.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->acheck = new Acheck21HttpClient();
        $this->emailexecutor = new EmailExecutorFactory();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // Check wheather the cron is enabled or disabled
        $checkCronEnabled = getSettingsValue('enable_acheck21_return_representment_process', 0);
        if ($checkCronEnabled == 0) {
            $this->info("NOT representing direct linked R01 returns data to Acheck21. It is DISABLED in settings.");
            Log::channel('return-transaction')->warning(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "NOT representing direct linked R01 returns data to Acheck21. It is DISABLED in settings.");
            // Send a email to the desired person stating that the cron job is disabled in settings
            $email_params = [
                'cron_job_name' => 'Transaction Representement of direct linked R01 that has not been represented twice in last 60 days',
            ];
            $this->emailexecutor->cronJobDisbaledAlert($email_params);
            return false;
        }
        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Fetching all the direct linked R01 transactions to represent that are not being represented twice...");
        $this->info("Fetching all the direct linked R01 transactions to represent that are not being represented twice...");
        // Get the Transaction date based on EST time
        if (Carbon::now()->timezone(ACHECK21_TIMEZONE_FOR_WEBHOOK)->lt(Carbon::now()->timezone(ACHECK21_TIMEZONE_FOR_WEBHOOK)->toDateString() . " 07:00:00")) {
            $transaction_date = Carbon::now()->timezone(ACHECK21_TIMEZONE_FOR_WEBHOOK)->subDays(1)->toDateString();
        } else {
            $transaction_date = Carbon::now()->timezone(ACHECK21_TIMEZONE_FOR_WEBHOOK)->toDateString();
        }
        Log::channel('return-transaction')->debug(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Checking webhook status for date: " . $transaction_date . "...");

        // Check if webhook has been called from ACHECK21 for the transaction date
        $checkForAcheckWebhook = getWebhookDataforSpecificDate($transaction_date);
        if (!empty($checkForAcheckWebhook) && Carbon::now()->gte(Carbon::parse($checkForAcheckWebhook->created_at)->addHour())) {
            $this->_representTransaction();
        } else {
            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Couldn't post new transactions to acheck21. Past transactions did not get Processed To Bank.");
            $this->info("Couldn't post new transactions to acheck21. Past transactions did not get Processed To Bank.");
        }
    }
    /**
     * This function fetches bank balance for all consumers who has added bank from finicity and keeps record in the database
     */
    private function _representTransaction()
    {
        $success = getStatus(SUCCESS);
        $failed = getStatus(FAILED);
        // Fetch all the returned transactions that are not being represented twice for a specific date range. Must be Direct bank linked and Return type must be R01
        $transactions = DB::SELECT("SELECT `transaction_details`.*, td1.acheck_document_id acheck_doc_id, `return_reason_masters`.`canpay_represent` AS `canpay_represent`, `return_reason_masters`.`new_banking` AS `new_banking`, `timezone_masters`.`timezone_name`, `users`.`bank_link_type` AS `bank_link_type`, `registered_merchant_master`.`acheck_account_id`
        FROM `transaction_details`
        JOIN transaction_details td1 ON td1.id = (SELECT td2.id FROM transaction_details td2 WHERE transaction_ref_no = transaction_details.id ORDER BY created_at LIMIT 1)
        JOIN `terminal_master` ON `terminal_master`.`id` = `transaction_details`.`terminal_id`
        JOIN `merchant_stores` ON `terminal_master`.`merchant_store_id` = `merchant_stores`.`id`
        JOIN `registered_merchant_master` ON `registered_merchant_master`.`id` = `merchant_stores`.`merchant_id`
        JOIN `users` ON `users`.`user_id` = `transaction_details`.`consumer_id`
        JOIN `status_master` ON `transaction_details`.`status_id` = `status_master`.`id`
        JOIN `return_reason_masters` ON `transaction_details`.`return_reason` = `return_reason_masters`.`id`
        LEFT JOIN `timezone_masters` ON `timezone_masters`.`id` = `transaction_details`.`timezone_id`
        WHERE `users`.`bank_link_type` = 1 AND `return_reason_masters`.`reason_code` = 'R01' AND `transaction_details`.`is_represented` = 0 AND `transaction_details`.`transaction_ref_no` IS NULL AND `status_master`.`code` = '" . RETURNED . "' AND `transaction_details`.`local_transaction_date` <= DATE_SUB(CURDATE(), INTERVAL 60 DAY) AND transaction_details.represent_count IN (0,1)
        order by transaction_details.local_transaction_date;");

        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Total " . count($transactions) . " number of transactions will be represented.");
        $this->info("Total " . count($transactions) . " number of transactions will be represented.");

        if (sizeof($transactions) != 0) {
            $cnt = 0;
            foreach ($transactions as $transaction) {
                // Storing into history table for each transaction
                $history = new ReturnRepresentHistory();
                $history->consumer_id = $transaction->consumer_id;
                $history->transaction_id = $transaction->id;
                $history->amount = $transaction->amount + $transaction->tip_amount;
                $history->is_manual = 0;
                try {
                    if ($transaction->canpay_represent == 1 && $transaction->new_banking == 0) {
                        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Representing transaction to acheck21 for transaction amount: " . $transaction->amount . " with id: " . $transaction->id);
                        if (env('ACHECK_POSTING')) {
                            $response = $this->acheck->representTransaction($transaction->acheck_doc_id);
                            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is enabled. Posting in ACHECK21.");
                        } else {
                            $response = 204;
                            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is disabled. Continue with a static 204 response.");
                        }

                        if ($response == 204) {
                            $this->_createTransaction($transaction, $transaction->acheck_doc_id, $transaction->id);
                            // Update the parent transaction
                            $new_represent_count = $transaction->represent_count + 1;
                            DB::statement("UPDATE transaction_details SET is_represented = 1, represent_count = " . $new_represent_count . " WHERE id = '" . $transaction->id . "'");
                            //adding details to store into history table
                            $history->outcome = "Success. Old transaction represented into acheck21. Executed from Direct Link R01 representment scheduler.";
                            $history->reason_representable = 1;
                            $history->status_id = $success;
                            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction represented for transaction amount: " . $transaction->amount . " with id: " . $transaction->id);
                            $cnt++;
                        } else {
                            //adding details to store into history table
                            $history->outcome = $response;
                            $history->reason_representable = 1;
                            $history->status_id = $failed;
                            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck21 could not represent the transaction for transaction amount: " . $transaction->amount . " with id: " . $transaction->id);
                        }
                    }
                    $history->save();
                } catch (\Exception$e) {
                    //adding details to store into history table
                    $history->outcome = $e;
                    $history->status_id = $failed;
                    Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "There was a problem trying to represent transaction with id: " . $transaction->id, [EXCEPTION => $e]);
                    $history->save();
                    continue;
                }
            }
            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Total " . $cnt . " R01 Transactions represented successfully.");
            $this->info("Transactions represented successfully.");
        } else {
            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No R01 transactions found to represent.");
            $this->info("No transactions found to represent.");
        }
    }
    /**
     * This function calls the api that posts consumer end transactions to acheck21
     * once the response returned from acheck21 it shores a new row to transaction_details table
     */
    private function _createTransaction($transaction, $doc_id, $account_id = false)
    {
        //create a new transaction
        $transaction_details = new TransactionDetails();
        $transaction_details->transaction_number = $transaction->transaction_number;
        $transaction_details->transaction_ref_no = $transaction->id;
        $transaction_details->user_id = $transaction->user_id;
        $transaction_details->consumer_id = $transaction->consumer_id;
        $transaction_details->terminal_id = $transaction->terminal_id;
        $transaction_details->account_id = $account_id;
        $transaction_details->transaction_time = Carbon::now();
        $transaction_details->local_transaction_time = Carbon::now($transaction->timezone_name);
        $transaction_details->local_transaction_year = date("Y", strtotime($transaction_details->local_transaction_time));
        $transaction_details->local_transaction_month = date("m", strtotime($transaction_details->local_transaction_time));
        $transaction_details->local_transaction_date = date("Y-m-d", strtotime($transaction_details->local_transaction_time));
        $transaction_details->timezone_id = $transaction->timezone_id;
        $transaction_details->consumer_bank_posting_amount = $transaction->consumer_bank_posting_amount;
        $transaction_details->amount = $transaction->amount;
        $transaction_details->tip_amount = $transaction->tip_amount;
        $transaction_details->tip_type = $transaction->tip_type;
        $transaction_details->tip_add_time = $transaction->tip_add_time;
        $transaction_details->used_qr_id = $transaction->used_qr_id;
        $transaction_details->status_id = getStatus(PROCESSED_FOR_ACHECK21);
        $transaction_details->transaction_type_id = $transaction->transaction_type_id;
        $transaction_details->transaction_place = ACHECK21;
        $transaction_details->acheck_document_id = $doc_id;
        $transaction_details->is_represented = 1;
        $transaction_details->save();
        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction details was stored successfully.");

        //Add Document ID in Acheck21DocumentIdHistory table and update Previous Ignore Flags
        $this->_addDocumentIdHistory($transaction_details);
    }

    private function _addDocumentIdHistory($transaction)
    {
        //Update the ignore flags for the previous flags
        Acheck21DocumentIdHistory::where(['transaction_ref_no' => $transaction->transaction_ref_no])->update(['ignore_flag' => 1]);

        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Ignore Flag updated for Transation Ref No. : " . $transaction->transaction_ref_no);

        //Add new Document ID in Acheck21DocumentIdHistory table
        $document_history = new Acheck21DocumentIdHistory();
        $document_history->transaction_id = $transaction->id;
        $document_history->transaction_ref_no = $transaction->transaction_ref_no;
        $document_history->amount = $transaction->amount + $transaction->tip_amount;
        $document_history->acheck_document_id = $transaction->acheck_document_id;
        $document_history->save();

        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Document ID added for Transation ID : " . $transaction->id);
    }
}
