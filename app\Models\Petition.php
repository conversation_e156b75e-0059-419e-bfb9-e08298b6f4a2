<?php
namespace App\Models;

use Illuminate\Auth\Authenticatable;
use Illuminate\Contracts\Auth\Access\Authorizable as AuthorizableContract;
use Illuminate\Contracts\Auth\Authenticatable as AuthenticatableContract;
use Illuminate\Database\Eloquent\Model;
use <PERSON><PERSON>\Lumen\Auth\Authorizable;

class Petition extends Model implements AuthenticatableContract, AuthorizableContract
{
    use Authenticatable, Authorizable;

    /**
     * __construct
     * This function will insert an alphanumeric 32 character unique id as primary key in the table whenever a data get inserted
     * @param  mixed $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        $this->id = generateUUID();

        parent::__construct($attributes);
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'petition_number',
        'consumer_id',
        'store_name',
        'store_short_name',
        'street_address',
        'city',
        'state',
        'zipcode',
        'lat',
        'long',
        'logo_url',
        'primary_contact_person_firstname',
        'primary_contact_person_lastname',
        'primary_contact_person_email',
        'primary_contact_person_title',
        'secondary_contact_person_firstname',
        'secondary_contact_person_lastname',
        'secondary_contact_person_email',
        'secondary_contact_person_title',
        'status_id',
        'onboarded_date',
        'merchant_store_id',
    ];
    public $timestamps   = true;
    public $incrementing = false;
    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */

    /**
     * Get the details of the petition.
     *
     * This function establishes a one-to-many relationship between the Petition model
     * and the PetitionDetail model, linking them via the 'petition_id' foreign key.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */

    public function petitionDetails()
    {
        return $this->hasMany(PetitionDetail::class, 'petition_id', 'id')->whereNull('deleted_at');
    }

    public function petitionRewards()
    {
        return $this->hasMany(CrewWheelSpinResult::class, 'petition_id', 'id');
    }

}
