<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("CREATE TABLE IF NOT EXISTS `short_urls` (
            `id` varchar(40) NOT NULL,
            `code` VARCHAR(40) NOT NULL,
            `original_url` VARCHAR(256) NOT NULL,
            `created_at` DATETIME NOT NULL,
            `updated_at` DATETIME NOT NULL,
            PRIMARY KEY (`id`),
            UNIQUE KEY `unique_code` (`code`)  -- Ensure the code is unique
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8");
        
    }

};
