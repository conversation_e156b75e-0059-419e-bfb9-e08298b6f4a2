<?php

namespace App\Console\Commands;

use App\Http\Factories\EmailExecutor\EmailExecutorFactory;
use App\Http\Factories\Transaction\TransactionFactory;
use App\Models\TransactionDetails;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ScheduleTransaction extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'schedule:transaction {--transaction_date=}';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command schedules the Merchant and CanPay end transactions to Acheck21';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->transaction = new TransactionFactory();
        $this->emailexecutor = new EmailExecutorFactory();
        $this->pending = getStatus(PENDING);
        $this->returned = getStatus(RETURNED);
        $this->processed_for_acheck = getStatus(PROCESSED_FOR_ACHECK21);
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

        // Check wheather new ach process is enabled or disabled
        $checkNewAchEnabled = getSettingsValue('enable_new_ach_process_for_all_merchant', 0);
        if ($checkNewAchEnabled == 1) {
            $this->info("New ACH process is enabled. NOT posting Consumer transaction data to Acheck21.");
            Log::channel('transaction-scheduler')->warning(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "New ACH process is enabled. NOT posting Consumer transaction data to Acheck21.");
            return false;
        }

        Log::channel('transaction-scheduler')->debug(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Merchant transaction scheduler started running at " . Carbon::now() . " UTC");
        // Get the last date when merchant or canpay transaction amount posted in acheck21
        $lastDateOfCanpayPosting = TransactionDetails::where('isCanpay', 1)->orderBy('local_transaction_time', 'DESC')->first();
        if ($this->option('transaction_date')) {
            $transaction_date = $this->option('transaction_date');
        } else {
            if (!empty($lastDateOfCanpayPosting)) { // If not empty then get the actual transaction date as the scheduler runs on the next day
                $transaction_date = Carbon::parse($lastDateOfCanpayPosting->created_at)->subDays(1)->toDateString();
            } else { // If empty then it is the first time the scheduler is running
                $transaction_date = Carbon::now()->timezone(ACHECK21_TIMEZONE_FOR_WEBHOOK)->subDays(1)->toDateString();
            }
        }

        // Check if there is any V2 transaction on that day
        $checkTransactionExists = TransactionDetails::join('status_master', 'status_master.id', '=', 'transaction_details.status_id')->where('transaction_details.is_v1', 0)->whereRaw("transaction_details.scheduled_posting_date = ?", [$transaction_date])->whereIn('status_master.code', array(PENDING, SUCCESS))->count();

        // Check if the date's transaction already poseted in ACHECK for Merchants and Canpay
        $posting_date = ($this->option('transaction_date')) ? Carbon::parse($this->option('transaction_date'))->addDays(1)->toDateString() : Carbon::parse($transaction_date)->addDays(1)->toDateString();
        $checkDuplicateSchedulerCall = TransactionDetails::join('status_master', 'status_master.id', '=', 'transaction_details.status_id')->whereRaw("transaction_details.local_transaction_date = ?", [$posting_date])->where('transaction_details.isCanpay', 1)->where('status_master.code', PROCESSED_FOR_ACHECK21)->count();
        if ($checkDuplicateSchedulerCall == 0) { // If Canpay posting row not available for that day then proceed with the scheduler
            // Check if there is any V2 transaction on that day
            $checkTransactionExists = TransactionDetails::join('status_master', 'status_master.id', '=', 'transaction_details.status_id')->where('transaction_details.is_v1', 0)->whereRaw("transaction_details.scheduled_posting_date = ?", [$transaction_date])->whereIn('status_master.code', array(PENDING, SUCCESS))->count();

            if ($checkTransactionExists > 0) { // If Transaction Exists then proceed for next Step
                Log::channel('transaction-scheduler')->debug(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Checking webhook status for date: " . $transaction_date . "...");
                // Check if webhook has been called from ACHECK21 for the transaction date
                $checkForAcheckWebhook = getWebhookDataforSpecificDate($transaction_date);
                if (!empty($checkForAcheckWebhook) || empty($lastDateOfCanpayPosting)) { // If previous day's transaction got updated or if this is the first time the scheduler is running then proceed with the process
                    Log::channel('transaction-scheduler')->info(addslashes(__METHOD__) . "(" . LINE . __LINE__ . ") - " . "Merchant transaction scheduler started running at " . Carbon::now() . " UTC for Transaction date " . $transaction_date);
                    $this->info("Merchant transaction scheduler started running at " . Carbon::now() . " UTC for Transaction date " . $transaction_date);
                    $this->_createMerchantCreditTransaction($transaction_date);
                    Log::channel('transaction-scheduler')->info(addslashes(__METHOD__) . "(" . LINE . __LINE__ . ") - " . "Merchant credit transaction finished at " . Carbon::now() . " UTC for Transaction date " . $transaction_date);
                    $this->info("Merchant credit transaction finished at " . Carbon::now() . " UTC for Transaction date " . $transaction_date);
                    $this->_createMerchantDebitTransaction($transaction_date);
                    Log::channel('transaction-scheduler')->info(addslashes(__METHOD__) . "(" . LINE . __LINE__ . ") - " . "Merchant debit transaction finished at " . Carbon::now() . " UTC for Transaction date " . $transaction_date);
                    $this->info("Merchant debit transaction finished at " . Carbon::now() . " UTC for Transaction date " . $transaction_date);
                    $this->_createCanPayTransaction($transaction_date);
                    Log::channel('transaction-scheduler')->info(addslashes(__METHOD__) . "(" . LINE . __LINE__ . ") - " . "CanPay credit transaction finished at " . Carbon::now() . " UTC for Transaction date " . $transaction_date);
                    $this->info("CanPay credit transaction finished at " . Carbon::now() . " UTC for Transaction date " . $transaction_date);
                    $this->_createCanPayReturnTransaction();
                    Log::channel('transaction-scheduler')->info(addslashes(__METHOD__) . "(" . LINE . __LINE__ . ") - " . "CanPay Return Recovery transaction finished at " . Carbon::now() . " UTC.");
                    $this->info("CanPay Return Recovery transaction finished at " . Carbon::now() . " UTC.");
                    Log::channel('transaction-scheduler')->info(addslashes(__METHOD__) . "(" . LINE . __LINE__ . ") - " . "Reward Point scheduler started running at " . Carbon::now() . " UTC for Transaction date " . $transaction_date);
                    $this->info("Reward Point transaction scheduler started running at " . Carbon::now() . " UTC for Transaction date " . $transaction_date);
                    $this->_createRewardAmountDebitTransaction($transaction_date);
                    Log::channel('transaction-scheduler')->info(addslashes(__METHOD__) . "(" . LINE . __LINE__ . ") - " . "Reward Point transaction finished at " . Carbon::now() . " UTC for Transaction date " . $transaction_date);
                    Log::channel('transaction-scheduler')->info(addslashes(__METHOD__) . "(" . LINE . __LINE__ . ") - " . "Cashback Point credit scheduler started running at " . Carbon::now() . " UTC for Transaction date " . $transaction_date);
                    $this->_createCanpayCashbackCreditTransaction($transaction_date);
                    Log::channel('transaction-scheduler')->info(addslashes(__METHOD__) . "(" . LINE . __LINE__ . ") - " . "Cashback Point credit transaction scheduler finished at " . Carbon::now() . " UTC for Transaction date " . $transaction_date);
                    $this->_sendDailyTransactionEmail(); // Daily Transaction Email
                    Log::channel('transaction-scheduler')->info(addslashes(__METHOD__) . "(" . LINE . __LINE__ . ") - " . "Merchant transaction scheduler finished running at " . Carbon::now() . " UTC for Transaction date " . $transaction_date);
                    $this->info("Merchant transaction scheduler finished running at " . Carbon::now() . " UTC for Transaction date " . $transaction_date);
                } else {
                    Log::channel('transaction-scheduler')->info(addslashes(__METHOD__) . "(" . LINE . __LINE__ . ") - " . "Merchant transaction scheduler skipped running at " . Carbon::now() . " UTC as Webhook was not called by ACHECK21 yet for " . $transaction_date);
                }
            } else { // If Transaction deosn't exists then Skip the scheduler
                Log::channel('transaction-scheduler')->info(addslashes(__METHOD__) . "(" . LINE . __LINE__ . ") - " . "Merchant transaction scheduler skipped running at " . Carbon::now() . " UTC as there was no V2 transaction found on " . $transaction_date);
            }
        } else { // If Transaction deosn't exists then Skip the scheduler
            Log::channel('transaction-scheduler')->info(addslashes(__METHOD__) . "(" . LINE . __LINE__ . ") - " . "Merchant transaction scheduler skipped running at " . Carbon::now() . " UTC as there was no V2 transaction found on " . $transaction_date);
        }
    }

    /**
     * Create merchant end transaction with all the accumulated amount
     */
    private function _createMerchantCreditTransaction($transaction_date)
    {
        $transaction_sql = "SELECT COUNT(*) AS count, SUM(td.amount) AS sum, SUM(td.tip_amount) AS tip_sum, tz.timezone_name, ms.merchant_id as registered_merchant_id, ms.store_id, td.*
        FROM transaction_details td
        JOIN terminal_master tm ON tm.id = td.terminal_id
        JOIN merchant_stores ms ON tm.merchant_store_id = ms.id
        JOIN registered_merchant_master rmm ON rmm.id = ms.merchant_id
        JOIN timezone_masters tz ON tz.id = td.timezone_id
        LEFT JOIN transaction_details td1 FORCE INDEX(idx_trans_ref_no) ON td.id = td1.transaction_ref_no AND td1.status_id = ?
        WHERE td.transaction_ref_no IS NULL AND td.merchant_id IS NULL AND IFNULL(td.consumer_id, '') != '' AND td.`status_id` = ? AND td.is_v1 = 0 AND td.scheduled_posting_date = ? AND td.merchant_id IS NULL AND td1.id IS NULL AND rmm.is_enabled_new_ach_process = 0
        GROUP BY ms.merchant_id";
        $transactions = DB::select($transaction_sql, [$this->returned, $this->pending, $transaction_date]);

        DB::beginTransaction();
        foreach ($transactions as $transaction) {
            //create the transaction through acheck21
            Log::channel('transaction-scheduler')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Posting merchant credit transaction to acheck21 for merchant id: " . $transaction->registered_merchant_id);
            try {
                $total_amount = ($transaction->sum + $transaction->tip_sum);
                $params['amount'] = -$total_amount;
                $params['merchant_id'] = $transaction->registered_merchant_id;
                $params['acheck_account_id'] = ENV('CANPAY_SETTLEMENT_ID');
                // sending store id and transaction date time as timestamp to create the check number for acheck21 posting
                $params['store_id'] = $transaction->store_id;
                $params['transaction_time'] = strtotime($transaction->local_transaction_time);
                //calling the factory function to create merchant credit transaction into acheck21
                if (env('ACHECK_POSTING')) {
                    $response = $this->transaction->createMerchantDepositTransaction($params);
                    $response_decoded = json_decode($response, true);
                    Log::channel('transaction-scheduler')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is enabled. Posting in ACHECK21.");
                } else {
                    $response_decoded['documentId'] = rand(********, ********);
                    Log::channel('transaction-scheduler')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is disabled. Continue with a random number.");
                }
                //create a new transaction
                $saveTransactionArray = [
                    'transaction' => $transaction,
                    'transaction_date' => $transaction_date,
                    'amount' => $total_amount,
                    'entry_type' => 'Cr',
                    'acheck_document_id' => $response_decoded['documentId'],
                    'is_canpay' => 0,
                    'return_offset' => 0,
                ];
                $this->_saveTransaction($saveTransactionArray);
                DB::commit();
                Log::channel('transaction-scheduler')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction details was stored successfully for merchant id: " . $transaction->registered_merchant_id);
                $this->info("Transaction details was stored successfully for merchant id: " . $transaction->registered_merchant_id);
                //store transaction details into transaction table
            } catch (\Exception $e) {
                Log::channel('transaction-scheduler')->error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured during Transaction for merchant id: " . $transaction->registered_merchant_id, [EXCEPTION => $e]);
                continue;
            }
        }
    }

    /**
     * Create merchant end Debit transaction with all the commission amount
     */
    private function _createMerchantDebitTransaction($transaction_date)
    {
        $transaction_sql = "SELECT COUNT(*) AS count, SUM(td.amount) AS sum, SUM(td.tip_amount) AS tip_sum, SUM(td.cashback_amount_earned) as total_cashback_amount, rmm.`merchant_name`,rmm.`volume_value_type`,rmm.`volume_value`,rmm.`per_transaction_value_type`,rmm.`per_transaction_value`, tz.`timezone_name`, ms.`store_id` as `store_id`, rmm.id as registered_merchant_id, rmm.split_cashback_posting, td.*
        FROM transaction_details td
        JOIN terminal_master tm ON tm.id = td.terminal_id
        JOIN merchant_stores ms ON tm.merchant_store_id = ms.id
        JOIN registered_merchant_master rmm ON rmm.id = ms.merchant_id
        JOIN timezone_masters tz ON tz.id = td.timezone_id
        LEFT JOIN transaction_details td1 FORCE INDEX(idx_trans_ref_no) ON td.id = td1.transaction_ref_no AND td1.status_id = ?
        WHERE td.transaction_ref_no IS NULL AND td.merchant_id IS NULL AND IFNULL(td.consumer_id, '') != '' AND td.`status_id` = ? AND td.is_v1 = 0 AND td.scheduled_posting_date = ? AND td.merchant_id IS NULL AND td1.id IS NULL AND rmm.is_enabled_new_ach_process = 0
        GROUP BY ms.merchant_id";
        $transactions = DB::select($transaction_sql, [$this->returned, $this->pending, $transaction_date]);

        foreach ($transactions as $transaction) {
            Log::channel('transaction-scheduler')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Posting merchant debit transaction to acheck21 for merchant id: " . $transaction->registered_merchant_id);
            try {
                //create the transaction through acheck21
                //calculate the commission amount that needs to be debited from the merchant account
                //volumn calculation
                $total_amount = ($transaction->sum + $transaction->tip_sum);

                if ($transaction->volume_value_type == PERCENTAGE) {
                    $fee_percentage = ($transaction->volume_value / 100);
                    $volume_amount = $total_amount * $fee_percentage;
                } else {
                    $volume_amount = $total_amount * $transaction->volume_value;
                }
                $rounded_up_volume_amount = round_up($volume_amount, 2);
                //per transaction calculation
                if ($transaction->per_transaction_value_type == PERCENTAGE) {
                    $fee_percentage = ($transaction->per_transaction_value / 100);
                    $per_transaction_amount = $transaction->count * $fee_percentage;
                } else {
                    $per_transaction_amount = $transaction->count * $transaction->per_transaction_value;
                }
                $per_transaction_amount = round($per_transaction_amount, 2);
                // If split_cashback_posting is set to 1 then post canpay fees and cashack points separately. Else post it together. But we will insert 2 rows in both the cases to keep the records in our side.
                $params['amount'] = $transaction->split_cashback_posting == 1 ? $per_transaction_amount + $rounded_up_volume_amount : $per_transaction_amount + $rounded_up_volume_amount + $transaction->total_cashback_amount;
                $params['merchant_id'] = $transaction->registered_merchant_id;
                $params['acheck_account_id'] = ENV('CANPAY_FEES_ID');
                // sending store id and transaction date time as timestamp to create the check number for acheck21 posting
                $params['store_id'] = $transaction->store_id;
                $params['transaction_time'] = strtotime($transaction->local_transaction_time);
                //calling the factory function to create merchant debit transaction into acheck21
                if (env('ACHECK_POSTING')) {
                    $response = $this->transaction->createMerchantFeeTransaction($params);
                    $response_decoded = json_decode($response, true);
                    Log::channel('transaction-scheduler')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is enabled. Posting in ACHECK21.");
                } else {
                    $response_decoded['documentId'] = rand(********, ********);
                    Log::channel('transaction-scheduler')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is disabled. Continue with a random number.");
                }
                // If split_cashback_posting is set to 1 then again post the cashback amount to ACHECK21
                if ($transaction->split_cashback_posting == 1 && $transaction->total_cashback_amount > 0) {
                    $params['amount'] = $transaction->total_cashback_amount;
                    if (env('ACHECK_POSTING')) {
                        $response = $this->transaction->createMerchantFeeTransaction($params);
                        $response_decoded_cashback = json_decode($response, true);
                        Log::channel('transaction-scheduler')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is enabled. Posting in ACHECK21.");
                    } else {
                        $response_decoded_cashback['documentId'] = rand(********, ********);
                        Log::channel('transaction-scheduler')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is disabled. Continue with a random number.");
                    }
                }
                // Save both debit data in database
                // create a new transaction for canpay fee
                $saveDebitTransactionArray = [
                    'transaction' => $transaction,
                    'transaction_date' => $transaction_date,
                    'amount' => $per_transaction_amount + $rounded_up_volume_amount,
                    'entry_type' => 'Dr',
                    'acheck_document_id' => $response_decoded['documentId'],
                    'is_canpay' => 0,
                    'return_offset' => 0,
                ];
                $this->_saveTransaction($saveDebitTransactionArray);
                // create a new transaction for cashback points
                if ($transaction->total_cashback_amount > 0) {
                    $saveCashbackTransactionArray = [
                        'transaction' => $transaction,
                        'transaction_date' => $transaction_date,
                        'amount' => $transaction->total_cashback_amount,
                        'entry_type' => 'Dr',
                        'acheck_document_id' => $transaction->split_cashback_posting == 1 ? $response_decoded_cashback['documentId'] : $response_decoded['documentId'],
                        'is_canpay' => 0,
                        'return_offset' => 0,
                        'is_cashback' => 1,
                    ];
                    $this->_saveTransaction($saveCashbackTransactionArray);
                }
                DB::commit();
                Log::channel('transaction-scheduler')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction details was stored successfully for merchant id: " . $transaction->registered_merchant_id);
                $this->info("Transaction details was stored successfully for merchant id: " . $transaction->registered_merchant_id);
                //store transaction details into transaction table
            } catch (\Exception $e) {
                Log::channel('transaction-scheduler')->error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured during Transaction for merchant id: " . $transaction->registered_merchant_id, [EXCEPTION => $e]);
                continue;
            }
        }
    }
    /**
     * Creates CanPay end transactions along with the accumulated commission amount
     */
    private function _createCanPayTransaction($transaction_date)
    {
        $transaction_sql = "SELECT COUNT(*) AS count, SUM(td.amount) AS sum, SUM(td.tip_amount) AS tip_sum, tz.`timezone_name`, ms.`merchant_id` as `registered_merchant_id`,td.*,rmm.volume_value_type, rmm.volume_value, rmm.per_transaction_value_type, rmm.per_transaction_value
        FROM transaction_details td
        JOIN terminal_master tm ON tm.id = td.terminal_id
        JOIN merchant_stores ms ON tm.merchant_store_id = ms.id
        JOIN registered_merchant_master rmm ON rmm.id = ms.merchant_id
        JOIN timezone_masters tz ON tz.id = td.timezone_id
        LEFT JOIN transaction_details td1 FORCE INDEX(idx_trans_ref_no) ON td.id = td1.transaction_ref_no AND td1.status_id = ?
        WHERE td.transaction_ref_no IS NULL AND td.merchant_id IS NULL AND IFNULL(td.consumer_id, '') != '' AND td.`status_id` = ? AND td.is_v1 = 0 AND td.scheduled_posting_date = ? AND td.merchant_id IS NULL AND td1.id IS NULL AND rmm.is_enabled_new_ach_process = 0
        GROUP BY ms.merchant_id";
        $transactions = DB::select($transaction_sql, [$this->returned, $this->pending, $transaction_date]);

        $total_fee = 0;
        DB::beginTransaction();
        try {
            foreach ($transactions as $transaction) {
                $total_amount = ($transaction->sum + $transaction->tip_sum);
                //calculate the commission amount that needs to be debited from the merchant account
                //volumn calculation
                if ($transaction->volume_value_type == PERCENTAGE) {
                    $fee_percentage = ($transaction->volume_value / 100);
                    $volume_amount = $total_amount * $fee_percentage;
                } else {
                    $volume_amount = $total_amount * $transaction->volume_value;
                }
                $rounded_up_volume_amount = round_up($volume_amount, 2);
                //per transaction calculation
                if ($transaction->per_transaction_value_type == PERCENTAGE) {
                    $fee_percentage = ($transaction->per_transaction_value / 100);
                    $per_transaction_amount = $transaction->count * $fee_percentage;
                } else {
                    $per_transaction_amount = $transaction->count * $transaction->per_transaction_value;
                }
                $per_transaction_amount = round($per_transaction_amount, 2);
                $total_fee = $total_fee + $per_transaction_amount + $rounded_up_volume_amount;
            }
            //create the transaction through acheck21
            Log::channel('transaction-scheduler')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Posting merchant transaction to acheck21 for CanPay Commission");
            $this->info("Posting merchant transaction to acheck21 for CanPay Commission");
            $params['amount'] = -$total_fee;
            $params['acheck_account_id'] = ENV('CANPAY_DEPOSIT');
            //calling the factory function to create canpay credit transaction into acheck21
            if (env('ACHECK_POSTING')) {
                $response = $this->transaction->createCanPayTransaction($params);
                $response_decoded = json_decode($response, true);
                Log::channel('transaction-scheduler')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is enabled. Posting in ACHECK21.");
            } else {
                $response_decoded['documentId'] = rand(********, ********);
                Log::channel('transaction-scheduler')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is disabled. Continue with a random number.");
            }
            //create a new transaction
            $saveTransactionArray = [
                'transaction' => $transaction,
                'transaction_date' => $transaction_date,
                'amount' => $total_fee,
                'entry_type' => 'Cr',
                'acheck_document_id' => $response_decoded['documentId'],
                'is_canpay' => 1,
                'return_offset' => 0,
            ];
            $this->_saveTransaction($saveTransactionArray);
            DB::commit();
            Log::channel('transaction-scheduler')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction details was stored successfully for CanPay Commission");
            $this->info("Transaction details was stored successfully for CanPay Commission");
        } catch (\Exception $e) {
            Log::channel('transaction-scheduler')->error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured while Canpay Credit Transaction", [EXCEPTION => $e]);
        }
    }
    /**
     * Creates CanPay Recovery Return transaction for the day
     */
    private function _createCanPayReturnTransaction()
    {
        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Posting CanPay Return Recovery transaction to acheck21 for representment");
        $transaction_sum = TransactionDetails::join('status_master', 'transaction_details.status_id', '=', 'status_master.id')->select(DB::raw('sum(transaction_details.consumer_bank_posting_amount) as sum'), DB::raw('sum(transaction_details.tip_amount) as tip_sum'))->where('transaction_details.is_represented', 1)->where('transaction_details.is_v1', 0)->where('transaction_details.transaction_ref_no', null)->where('status_master.code', RETURNED)->first(); // sum query
        if ($transaction_sum->sum != null) {
            $amount = ($transaction_sum->sum);
            $params['amount'] = -$amount;
            $params['acheck_account_id'] = env('CANPAY_RETURN_RECOVERY');
            //calling the factory function to create consumer transaction into acheck21
            if (env('ACHECK_POSTING')) {
                $response = $this->transaction->createCanPayReturnTransaction($params);
                $response_decoded = json_decode($response, true);
                Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is enabled. Posting in ACHECK21.");
            } else {
                $response_decoded['documentId'] = rand(********, ********);
                Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is disabled. Continue with a random number.");
            }
            if (isset($response_decoded['documentId'])) {
                //create a new transaction
                $saveTransactionArray = [
                    'amount' => $transaction_sum->sum,
                    'entry_type' => 'Cr',
                    'acheck_document_id' => $response_decoded['documentId'],
                    'is_canpay' => 0,
                    'return_offset' => 1,
                ];
                $this->_saveTransaction($saveTransactionArray);

                //update the status of all the represented returned transactions from returned to pending
                $transactions = TransactionDetails::where('is_represented', 1)->where('is_v1', 0)->where('transaction_ref_no', null)->where('status_id', $this->returned)->get();

                foreach ($transactions as $transaction) {
                    $transaction->status_id = $this->pending;
                    $transaction->save();
                }
                Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "CanPay transaction details was stored successfully for amount: " . $params['amount']);
                $this->info("CanPay transaction details was stored successfully.");
            } else {
                Log::channel('return-transaction')->error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "There was some problem trying to post transaction into Acheck21.");
                $this->info("There was some problem trying to post transaction into Acheck21.");
            }
        } else {
            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No transactions found.");
            $this->info("No transactions found.");
        }
    }

    private function _createRewardAmountDebitTransaction($transaction_date)
    {
        Log::channel('transaction-scheduler')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Posting Canpay Reward Amount Used to acheck21");

        $transaction_sql = "SELECT COALESCE(SUM(td.reward_point_used),0) AS reward_point, COALESCE(SUM(td.reward_amount_used),0) AS reward_amount, td.scheduled_posting_date
        FROM transaction_details td
        JOIN terminal_master tm ON tm.id = td.terminal_id
        JOIN merchant_stores ms ON tm.merchant_store_id = ms.id
        JOIN registered_merchant_master rmm ON rmm.id = ms.merchant_id
        LEFT JOIN transaction_details AS td1 ON td.id = td1.transaction_ref_no AND td1.status_id = ? WHERE td.scheduled_posting_date = ? AND td.transaction_ref_no IS NULL AND td.consumer_id!='' AND td.status_id = ? AND td.isCanpay = 0 AND rmm.is_enabled_new_ach_process = 0";
        $transactions = DB::select($transaction_sql, [$this->processed_for_acheck, $transaction_date, $this->pending]);

        if ($transactions[0]->reward_amount > 0) {
            $amount = $transactions[0]->reward_amount;
            $params['amount'] = $amount;
            $params['acheck_account_id'] = env('CANPAY_REWARD_POINT');
            //calling the factory function to create consumer transaction into acheck21
            if (env('ACHECK_POSTING')) {
                $response = $this->transaction->createCanPayRewardTransaction($params);
                $response_decoded = json_decode($response, true);
                Log::channel('transaction-scheduler')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is enabled. Posting in ACHECK21.");
            } else {
                $response_decoded['documentId'] = rand(********, ********);
                Log::channel('transaction-scheduler')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is disabled. Continue with a random number.");
            }
            if (isset($response_decoded['documentId'])) {
                //create a new transaction
                $saveTransactionArray = [
                    'transaction' => $transactions[0],
                    'transaction_date' => $transaction_date,
                    'reward_point_used' => $transactions[0]->reward_point,
                    'amount' => $transactions[0]->reward_amount,
                    'tip_amount' => 0,
                    'entry_type' => 'Dr',
                    'acheck_document_id' => $response_decoded['documentId'],
                    'reward_acheck_posting' => 1,
                ];
                $this->_saveTransaction($saveTransactionArray);

                Log::channel('transaction-scheduler')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "CanPay Reward amount details was stored successfully for amount: " . $params['amount']);
                $this->info("CanPay Reward Amount details was stored successfully.");
            } else {
                Log::channel('transaction-scheduler')->error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "There was some problem trying to post reward point transactions into Acheck21.");
                $this->info("There was some problem trying to post reward point transactions into Acheck21.");
            }
        } else {
            Log::channel('transaction-scheduler')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No transactions found with Reward Amount Used.");
            $this->info("No transactions found with Reward Amount Used.");
        }
    }

    /**
     * _createCanpayCashbackCreditTransaction
     * This function will post all the cashback amount to canpay
     * @param  mixed $transaction_date
     * @return void
     */
    private function _createCanpayCashbackCreditTransaction($transaction_date)
    {
        Log::channel('transaction-scheduler')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Posting Canpay cashback amount credit to acheck21");

        $transaction_sql = "SELECT COALESCE(SUM(td.cashback_amount_earned),0) AS total_cashback_amount_earned, td.scheduled_posting_date FROM transaction_details td LEFT JOIN transaction_details AS td1 ON td.id = td1.transaction_ref_no AND td1.status_id = ? WHERE td.scheduled_posting_date = ? AND td.transaction_ref_no IS NULL AND td.consumer_id != '' AND td.status_id = ? AND td.isCanpay = 0";
        $transactions = DB::select($transaction_sql, [$this->processed_for_acheck, $transaction_date, $this->pending]);

        if ($transactions[0]->total_cashback_amount_earned > 0) {
            $amount = $transactions[0]->total_cashback_amount_earned;
            $params['amount'] = $amount;
            $params['acheck_account_id'] = env('CANPAY_CASHBACK_POINT');
            //calling the factory function to create consumer transaction into acheck21
            if (env('ACHECK_POSTING')) {
                $response = $this->transaction->createCanPayCashbackTransaction($params);
                $response_decoded = json_decode($response, true);
                Log::channel('transaction-scheduler')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is enabled. Posting in ACHECK21.");
            } else {
                $response_decoded['documentId'] = rand(********, ********);
                Log::channel('transaction-scheduler')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is disabled. Continue with a random number.");
            }
            if (isset($response_decoded['documentId'])) {
                //create a new transaction
                $saveTransactionArray = [
                    'transaction' => $transactions[0],
                    'transaction_date' => $transaction_date,
                    'amount' => $transactions[0]->total_cashback_amount_earned,
                    'tip_amount' => 0,
                    'entry_type' => 'Cr',
                    'acheck_document_id' => $response_decoded['documentId'],
                    'is_cashback' => 1,
                ];
                $this->_saveTransaction($saveTransactionArray);

                Log::channel('transaction-scheduler')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "CanPay Cashback amount details was stored successfully for amount: " . $params['amount']);
                $this->info("CanPay Reward Amount details was stored successfully.");
            } else {
                Log::channel('transaction-scheduler')->error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "There was some problem trying to post canpay credit cashback amount transactions into Acheck21.");
                $this->info("There was some problem trying to post canpay credit cashback amount transactions into Acheck21.");
            }
        } else {
            Log::channel('transaction-scheduler')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No transactions found with Reward Amount Used.");
            $this->info("No transactions found with Reward Amount Used.");
        }
    }

    /**
     * _saveTransaction
     * This fucntion will save data in transaction_details table based on tha params
     * @param  mixed $data
     * @return void
     */
    private function _saveTransaction($params)
    {
        // Creating a new transaction
        $transaction_details = new TransactionDetails();
        $transaction_details->transaction_number = generateTransactionId();
        $transaction_details->transaction_ref_no = isset($params['return_offset']) && isset($params['is_canpay']) && $params['return_offset'] == 0 && $params['is_canpay'] == 0 ? $params['transaction']->registered_merchant_id : null; // This should be null. But we are saving the merchant_id here as this minor change needs a huge change for all major transaction related queries in the whole application
        $transaction_details->merchant_id = isset($params['return_offset']) && isset($params['is_canpay']) && $params['return_offset'] == 0 && $params['is_canpay'] == 0 ? $params['transaction']->registered_merchant_id : null;
        if (isset($params['return_offset']) && $params['return_offset'] == 1) {
            $transaction_details->transaction_time = Carbon::now();
            $transaction_details->local_transaction_time = Carbon::now();
            $transaction_details->is_represented = 1;
        } else if (Carbon::parse($params['transaction_date'])->addDays(1)->lt(Carbon::now())) {
            $transaction_details->transaction_time = Carbon::parse($params['transaction_date'])->addDays(1)->format('Y-m-d H:i:s');
            $transaction_details->local_transaction_time = Carbon::parse($params['transaction_date'])->addDays(1)->format('Y-m-d H:i:s');
        } else {
            $transaction_details->transaction_time = Carbon::now();
            $transaction_details->local_transaction_time = Carbon::now($params['transaction']->timezone_name);
        }
        $transaction_details->local_transaction_year = date("Y", strtotime($transaction_details->local_transaction_time));
        $transaction_details->local_transaction_month = date("m", strtotime($transaction_details->local_transaction_time));
        $transaction_details->local_transaction_date = date("Y-m-d", strtotime($transaction_details->local_transaction_time));
        $transaction_details->timezone_id = isset($params['return_offset']) && $params['return_offset'] == 0 && $params['is_canpay'] == 0 ? $params['transaction']->timezone_id : null;
        $transaction_details->scheduled_posting_date = isset($params['transaction']) ? $params['transaction']->scheduled_posting_date : null;
        $transaction_details->amount = $params['amount'];
        $transaction_details->tip_amount = isset($params['tip_amount']) ? $params['tip_amount'] : 0;
        $transaction_details->entry_type = $params['entry_type'];
        $transaction_details->status_id = $this->processed_for_acheck;
        $transaction_details->transaction_place = ACHECK21;
        $transaction_details->acheck_document_id = $params['acheck_document_id'];
        if (isset($params['is_canpay']) && $params['is_canpay'] == 1) {
            $transaction_details->isCanpay = 1;
        }
        if (isset($params['is_cashback']) && $params['is_cashback'] == 1) {
            $transaction_details->is_cashback = 1;
        }
        if (array_key_exists("reward_acheck_posting", $params) && $params['reward_acheck_posting'] == 1) {
            $transaction_details->reward_point_used = $params['reward_point_used'];
            $transaction_details->reward_amount_used = $params['amount'];
            $transaction_details->reward_acheck_posting = 1;
        }
        $transaction_details->save();
    }

    /**
     * Function that sends mail Daily to the email set in the .env File
     */
    private function _sendDailyTransactionEmail()
    {
        $this->info("Daily Transaction mail sending started...");
        $report_date_merchant = Carbon::now()->toDateString();
        $report_date = Carbon::parse($report_date_merchant)->subDays(1)->toDateString();
        $last_time_of_canpay_posting = TransactionDetails::where('isCanpay', 1)->orderBy('created_at', 'DESC')->first();
        $return_represent_start_time = Carbon::parse($last_time_of_canpay_posting->created_at)->subDays(1);
        $return_represent_end_time = $last_time_of_canpay_posting->created_at;
        $canpay_offset_post_date = date("Y-m-d", strtotime($last_time_of_canpay_posting->created_at)) . ' 07:00:00';
        $date = Carbon::createFromFormat('Y-m-d H:i:s', $canpay_offset_post_date, 'America/New_York');
        $represented_date = date('Y-m-d', strtotime(Carbon::parse($last_time_of_canpay_posting->created_at)->subDays(1)));

        $transaction_report = "SELECT 'Total Consumer Transaction' AS head, COUNT(*) as total_count, COALESCE(SUM(td.consumer_bank_posting_amount),0) as total_value FROM transaction_details td STRAIGHT_JOIN transaction_details AS td1 ON td.id = td1.transaction_ref_no WHERE td.scheduled_posting_date = ? AND td.transaction_ref_no IS NULL AND td.consumer_id!='' AND td.status_id = ? AND td.isCanpay = 0 AND td1.status_id = ?
        UNION
        SELECT 'Total Reward Amount Used' AS head, COUNT(*) as total_count, COALESCE(SUM(amount),0) AS total_value FROM transaction_details WHERE reward_acheck_posting = 1 AND scheduled_posting_date = ?
        UNION
        SELECT 'Total Merchant Transaction' AS head, COUNT(*) as total_count,COALESCE(SUM(amount),0) AS total_value  FROM transaction_details WHERE entry_type = '" . CREDIT . "' AND merchant_id IS NOT NULL AND scheduled_posting_date = ?
        UNION
        SELECT 'CanPay Fee(Merchant Debit)' AS head, COUNT(*) as total_count,COALESCE(SUM(amount),0) AS total_value FROM transaction_details WHERE entry_type = '" . DEBIT . "' AND merchant_id IS NOT NULL AND scheduled_posting_date = ? AND is_cashback = 0
        UNION
        SELECT 'CanPay Fee' AS head, COUNT(*) as total_count,COALESCE(SUM(amount),0) AS total_value FROM transaction_details WHERE isCanpay = 1 AND scheduled_posting_date = ?";

        $transaction_report_array = DB::select($transaction_report, [$report_date, $this->pending, $this->processed_for_acheck, $report_date, $report_date, $report_date, $report_date]);

        $total_represented = "SELECT COALESCE(SUM(consumer_bank_posting_amount),0) AS represented_amount
        FROM transaction_details AS td
        LEFT JOIN return_manual_post_logs rmpl ON td.transaction_ref_no = rmpl.primary_identifier_value AND rmpl.return_posted = '1'
        WHERE entry_type = '" . DEBIT . "' AND is_represented = 1 AND transaction_ref_no IS NOT NULL AND td.status_id = ?
        AND td.created_at >= ? AND td.created_at <= ? AND rmpl.id IS null";
        $total_represented_array = DB::connection(MYSQL_RO)->select($total_represented, [$this->processed_for_acheck, $return_represent_start_time, $return_represent_end_time]);

        $return_transaction_amounts = "SELECT COALESCE(SUM(if((td1.return_from_primary_account = 1 AND td1.transaction_returned != td1.transaction_represented) OR (td1.represent_count >= 3), td.consumer_bank_posting_amount, 0)),0) AS new_transaction
        FROM transaction_details AS td
        INNER JOIN transaction_details AS td1 ON td.transaction_ref_no = td1.id
        INNER JOIN return_reason_masters rrm ON td1.return_reason = rrm.id
        INNER JOIN users AS us ON td.consumer_id = us.user_id
        WHERE td.entry_type = '" . DEBIT . "' AND td.is_represented = 1 AND td.transaction_ref_no IS NOT NULL AND td.status_id = ? AND td.local_transaction_date = ? ";
        $return_transaction_amounts_array = DB::connection(MYSQL_RO)->select($return_transaction_amounts, [$this->processed_for_acheck, $represented_date]);

        $canpay_offset = "SELECT COALESCE(SUM(amount+tip_amount),0) AS amount FROM transaction_details WHERE entry_type = '" . CREDIT . "' AND merchant_id IS NULL AND created_at >= ?  AND created_at <= ? and is_represented = 1";
        $canpay_offset_array = DB::select($canpay_offset, [$return_represent_end_time, Carbon::parse($return_represent_end_time)->addHour()]);

        // Reward Points Earned
        $reward_points_earned = "SELECT COALESCE(SUM(IF(user_reward_usage_history.entry_type =  '" . CREDIT . "',user_reward_usage_history.reward_amount_left,0)),0) -  COALESCE(SUM(IF(user_reward_usage_history.entry_type = '" . DEBIT . "',user_reward_usage_history.reward_amount_left,0)),0) AS reward_amount, COALESCE(ROUND(SUM(IF(user_reward_usage_history.entry_type = '" . CREDIT . "',user_reward_usage_history.reward_point_left,0)),0),0)  - COALESCE(ROUND(SUM(IF(user_reward_usage_history.entry_type = '" . DEBIT . "',user_reward_usage_history.reward_point_left,0)),0),0) AS reward_point FROM " . env('DB_DATABASE_REWARD_WHEEL') . ".user_reward_usage_history WHERE created_at >= ? AND created_at <= ?";
        $reward_earned_array = DB::select($reward_points_earned, [$return_represent_start_time, $return_represent_end_time]);

        // Cashback points earned
        $cashback_point_earned_sql = "SELECT COALESCE(SUM(IF(merchant_id IS NOT NULL && entry_type = 'Dr' && is_cashback = 1, amount, 0)),0) AS merchant_cashback_debit, COALESCE(SUM(IF(entry_type = 'Cr' && is_cashback = 1 && merchant_id IS NULL, amount, 0)),0) AS canpay_cashback_credit FROM transaction_details WHERE status_id = ? AND created_at >= ? AND created_at <= ?";
        $cashback_point_earned = DB::select($cashback_point_earned_sql, [$this->processed_for_acheck, $return_represent_start_time, $return_represent_end_time]);

        $transaction_returned = "SELECT
        REPLACE(CONCAT(COALESCE(
        REPLACE(us.first_name, ' ',''), ''),' ', COALESCE(
        REPLACE(us.last_name, ' ',''), '')),'  ',' ') AS consumer_name, COALESCE(td.consumer_bank_posting_amount,0) AS amount, td.local_transaction_time AS return_represent_time, rrm.reason_code, td1.represent_count
        FROM `transaction_details` AS `td`
        STRAIGHT_JOIN  `transaction_details` AS `td1` ON `td`.`transaction_ref_no` = `td1`.`id`
        INNER JOIN `return_reason_masters` AS `rrm` ON `td1`.`return_reason` = `rrm`.`id`
        INNER JOIN `users` AS `us` ON `td`.`consumer_id` = `us`.`user_id`
        WHERE `td`.`entry_type` = 'Dr' AND `td`.`is_represented` = 1 AND td.transaction_ref_no IS NOT NULL AND `td`.`status_id` = ? AND td.created_at >= ? AND td.created_at <= ?";
        $transaction_return_array = DB::connection(MYSQL_RO)->select($transaction_returned, [$this->processed_for_acheck, $return_represent_start_time, $return_represent_end_time]);

        $totalCredits = (isset($transaction_report_array[2]) ? $transaction_report_array[2]->total_value : 0) + (isset($transaction_report_array[4]) ? $transaction_report_array[4]->total_value : 0) + (isset($total_represented_array[0]) ? $total_represented_array[0]->represented_amount : 0);
        $total_consumer_and_reward_amount = $transaction_report_array[0]->total_value + $transaction_report_array[1]->total_value;
        $transaction_details = [
            'transaction_date' => date("m-d-Y", strtotime($report_date_merchant)),
            'total_consumer_transaction_count' => isset($transaction_report_array[0]) ? round($transaction_report_array[0]->total_count, 0) : 0,
            'total_consumer_transaction_value' => isset($transaction_report_array[0]) ? "$" . number_format($transaction_report_array[0]->total_value, 2) : "$0.00",
            'total_reward_amount' => isset($transaction_report_array[1]) ? "$" . number_format($transaction_report_array[1]->total_value, 2) : "$0.00",
            'total_consumer_and_reward_amount' => "$" . number_format($total_consumer_and_reward_amount, 2),
            'total_merchant_transaction' => isset($transaction_report_array[2]) ? "$" . number_format($transaction_report_array[2]->total_value, 2) : "$0.00",
            'total_canpay_fee_merchant_debit' => isset($transaction_report_array[3]) ? "$" . number_format($transaction_report_array[3]->total_value, 2) : "$0.00",
            'total_canpay_fee' => isset($transaction_report_array[4]) ? "$" . number_format($transaction_report_array[4]->total_value, 2) : "$0.00",
            'total_represented_value' => "$" . number_format($total_represented_array[0]->represented_amount, 2),
            'new_transaction_value' => "$" . number_format($return_transaction_amounts_array[0]->new_transaction, 2),
            'representment_value' => "$" . number_format($total_represented_array[0]->represented_amount - $return_transaction_amounts_array[0]->new_transaction, 2),
            'canpay_offset' => "$" . number_format($canpay_offset_array[0]->amount, 2),
            'totalCredits' => "$" . number_format($totalCredits, 2),
            'transaction_returns' => json_decode(json_encode($transaction_return_array), true),
            'reward_points_earned' => $reward_earned_array[0]->reward_point,
            'reward_amount_earned' => "$" . number_format($reward_earned_array[0]->reward_amount, 2),
            'total_cashback_merchant_debit' => "$" . number_format($cashback_point_earned[0]->merchant_cashback_debit, 2),
            'total_cashback_canpay_credit' => "$" . number_format($cashback_point_earned[0]->canpay_cashback_credit, 2),
        ];

        $this->emailexecutor->dailyTransactionDetailsEmail($transaction_details);
    }
}
