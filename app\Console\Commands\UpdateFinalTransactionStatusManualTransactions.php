<?php
namespace App\Console\Commands;

use App\Models\TransactionDetails;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class UpdateFinalTransactionStatusManualTransactions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:manualtransaction';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command updates the final status for all the manual transactions that has been successfully settled.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->_updateTransactionStatus();
    }
    /**
     * This function updates the final status for all the transactions that has been successfully settled
     */
    private function _updateTransactionStatus()
    {
        Log::channel('update-final-status-manual-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Fetching all the manual transactions from " . env('UPDATE_STATUS_AFTER_REPRESENTMENT') . " days earlier...");
        $this->info("Fetching transactions...");
        $pending = getStatus(PENDING);
        $success = getStatus(SUCCESS);
        $processed_for_acheck = getStatus(PROCESSED_FOR_ACHECK21);
        //fetch all the transactions before mentioned day that has been posted for representment and update the status to success (Only Manual Transactions)
        $sub_query_days_interval = env('UPDATE_STATUS_AFTER_REPRESENTMENT') - 1;
        $transactions = TransactionDetails::on(MYSQL_RO)->join('user_bank_account_info', 'transaction_details.account_id', '=', 'user_bank_account_info.id')
            ->join('users', 'users.user_id', '=', 'transaction_details.consumer_id')
            ->join('timezone_masters', 'timezone_masters.id', '=', 'transaction_details.timezone_id')
            ->leftJoin('transaction_release_dates', function ($join) { // Will exclude the records in transaction_release_dates table
                $join->on('transaction_release_dates.transaction_id', '=', 'transaction_details.id')
                    ->where('transaction_release_dates.invalid', '=', 0);
            })
            ->select('transaction_details.*', 'timezone_masters.timezone_name', 'users.algo_user_type')
            ->where('transaction_details.transaction_ref_no', null)
            ->selectRaw('if(user_bank_account_info.account_id IS NOT NULL,1,0) AS bank_link_type')
            ->where('transaction_details.isCanpay', 0)->where('transaction_details.is_v1', 0)
            ->where('transaction_details.status_id', $pending)
            ->whereNull('user_bank_account_info.account_id') // Condition to fetch only Manual Transactions
            ->whereNull('transaction_release_dates.id')
            ->whereRaw('transaction_details.scheduled_posting_date <= (DATE_SUB(CURDATE(), INTERVAL ' . env('UPDATE_STATUS_AFTER_REPRESENTMENT') . ' DAY))')
            ->whereNotIn('transaction_details.id', function ($query) use ($processed_for_acheck, $sub_query_days_interval) {
                $query->select('transaction_details.transaction_ref_no')
                    ->from(with(new TransactionDetails)->getTable())
                    ->where('transaction_details.status_id', $processed_for_acheck)
                    ->where('transaction_details.isCanpay', 0)
                    ->where('transaction_details.merchant_id', null)
                    ->where('transaction_details.transaction_ref_no', '!=', null)
                    ->whereRaw('transaction_details.local_transaction_date BETWEEN (DATE_SUB(CURDATE(), INTERVAL ' . $sub_query_days_interval . ' DAY)) AND CURDATE()')->get();
            })->get();
        $today = date('Y-m-d');
        if (sizeof($transactions) != 0) {
            foreach ($transactions as $transaction) {
                Log::channel('update-final-status-manual-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction ID : " . $transaction->id . " checking started for Manual Bank link conditions");
                $releaseDate = getTransactionReleaseDate($transaction->scheduled_posting_date, $transaction->id);
                $releasedays = env('MANUAL_BANK_LINK_RELEASE_DAYS');
                if ($releaseDate && $releaseDate <= $today) {
                    // update the parent transaction
                    TransactionDetails::where('id', $transaction->id)->update(array('status_id' => $success));
                    // Settle Points associated with the Transaction
                    updateRewardStatus($transaction, UPDATE_FINAL_STATUS_MANUAL_TRANSACTION);
                    // created new success row
                    createSuccessTransaction($transaction, UPDATE_FINAL_STATUS_MANUAL_TRANSACTION);
                    // Check and update the user's algo_user_type
                    checkAndUpdateAlgoUserType($transaction);
                    Log::channel('update-final-status-manual-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction ID : " . $transaction->id . "  updated and released in " . $releasedays . " working days successfully.");
                    $this->info("Transaction ID: " . $transaction->id . " updated successfully.");
                } else {
                    // Insert into Transaction Release Dates table
                    $data['transaction_id'] = $transaction->id;
                    $data['release_date'] = $releaseDate;
                    $data['bank_link_type'] = 0;
                    insertIntoTransactionReleaseDates($data, UPDATE_FINAL_STATUS_MANUAL_TRANSACTION);
                    $this->info("Transaction ID: " . $transaction->id . " inserted into Transaction Release Dates table.");
                }
            }
        } else {
            Log::channel('update-final-status-manual-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No Manual Link transactions found to update.");
            $this->info("No Manual Link transactions found to update.");
        }
    }

}
