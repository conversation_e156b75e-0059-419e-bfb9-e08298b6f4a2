<?php
namespace App\Console\Commands;

use App\Http\Clients\Acheck21HttpClient;
use App\Http\Factories\Transaction\TransactionFactory;
use App\Models\TransactionDetails;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class RepresentTransactionSet extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'represent:transactionset {--transaction_id=} {--account_id=}';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command represents returned transaction through acheck21 which are approved by consumer.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->transaction = new TransactionFactory();
        $this->acheck = new Acheck21HttpClient();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->info("Representing return transactions...");
        $transaction_id = $this->option('transaction_id');
        $processed_for_acheck = getStatus(PROCESSED_FOR_ACHECK21);
        $transaction = TransactionDetails::find($transaction_id);

        //post the consumer debit transaction
        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Posting consumer transaction to acheck21 for transaction amount: " . $transaction->consumer_bank_posting_amount . " with id: " . $transaction->id);
        $params['amount'] = $transaction->consumer_bank_posting_amount;
        $params['consumer_id'] = $transaction->consumer_id;
        $params['acheck_account_id'] = $transaction->acheck_account_id;
        // send over the current active bank account id
        $params['account_id'] = $this->option('account_id') != '' ? $this->option('account_id') : $transaction->account_id;

        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Posting consumer transaction to acheck21 with Account ID: " . $params['account_id']);

        if (env('ACHECK_POSTING')) {
            //calling the factory function to create consumer transaction into acheck21
            $response = $this->transaction->createConsumerReturnTransaction($params);
            $response_decoded = json_decode($response, true);
            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is enabled. Posting in ACHECK21.");
        } else {
            $response_decoded['documentId'] = rand(********, ********);
            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is disabled. Continue with a random number.");
        }

        // delete last entered processed for acheck row
        TransactionDetails::where('transaction_ref_no', $transaction_id)->where('status_id', $processed_for_acheck)->orderby('created_at', 'desc')->limit(1)->delete();

        $this->_createTransaction($transaction, $response_decoded['documentId'], $params['account_id']);

        $transaction->account_id = $transaction->account_id;
        // update the parent transaction
        $transaction->is_represented = 1;
        $transaction->represent_count = $transaction->represent_count + 1;
        $transaction->transaction_represented = $params['account_id'];
        $transaction->save();
        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction represented(created new) for transaction amount: " . $transaction->consumer_bank_posting_amount . " with id: " . $transaction->id);
    }
    private function _createTransaction($transaction, $doc_id, $account_id)
    {
        //create a new transaction
        $transaction_details = new TransactionDetails();
        $transaction_details->transaction_number = $transaction->transaction_number;
        $transaction_details->transaction_ref_no = $transaction->id;
        $transaction_details->user_id = $transaction->user_id;
        $transaction_details->consumer_id = $transaction->consumer_id;
        $transaction_details->terminal_id = $transaction->terminal_id;
        $transaction_details->account_id = $account_id;
        $transaction_details->transaction_time = Carbon::now();
        $transaction_details->local_transaction_time = Carbon::now($transaction->timezone_name);
        $transaction_details->local_transaction_year = date("Y", strtotime($transaction_details->local_transaction_time));
        $transaction_details->local_transaction_month = date("m", strtotime($transaction_details->local_transaction_time));
        $transaction_details->local_transaction_date = date("Y-m-d", strtotime($transaction_details->local_transaction_time));
        $transaction_details->timezone_id = $transaction->timezone_id;
        $transaction_details->consumer_bank_posting_amount = $transaction->consumer_bank_posting_amount;
        $transaction_details->reward_amount_used = $transaction->reward_amount_used;
        $transaction_details->reward_point_used = $transaction->reward_point_used;
        $transaction_details->return_representment_reward_deduction_amount = $transaction->return_representment_reward_deduction_amount;
        $transaction_details->amount = $transaction->amount;
        $transaction_details->tip_amount = $transaction->tip_amount;
        $transaction_details->tip_type = $transaction->tip_type;
        $transaction_details->tip_add_time = $transaction->tip_add_time;
        $transaction_details->used_qr_id = $transaction->used_qr_id;
        $transaction_details->status_id = getStatus(PROCESSED_FOR_ACHECK21);
        $transaction_details->transaction_type_id = $transaction->transaction_type_id;
        $transaction_details->transaction_place = ACHECK21;
        $transaction_details->acheck_document_id = $doc_id;
        $transaction_details->is_represented = 1;
        $transaction_details->save();
        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction details was stored successfully.");
    }
}
