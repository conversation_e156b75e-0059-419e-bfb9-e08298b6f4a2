<?php

namespace App\Console\Commands;

use App\Http\Clients\Acheck21HttpClient;
use App\Http\Factories\EmailExecutor\EmailExecutorFactory;
use App\Http\Factories\Transaction\TransactionFactory;
use App\Models\Acheck21DocumentIdHistory;
use App\Models\BankAccountInfo;
use App\Models\ConsumerAccountBalance;
use App\Models\ReturnRepresentHistory;
use App\Models\ReturnTransactionRepaymentSchedule;
use App\Models\TransactionDetails;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class RepresentTransaction extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'represent:transaction';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command represents returned transaction through acheck21 which are approved by consumer.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->transaction = new TransactionFactory();
        $this->acheck = new Acheck21HttpClient();
        $this->emailexecutor = new EmailExecutorFactory();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // Check wheather the cron is enabled or disabled
        $checkCronEnabled = getSettingsValue('enable_acheck21_return_representment_process', 0);
        if ($checkCronEnabled == 0) {
            $this->info("NOT representing returns data to Acheck21. It is DISABLED in settings.");
            Log::channel('return-transaction')->warning(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "NOT representing returns data to Acheck21. It is DISABLED in settings.");
            // Send a email to the desired person stating that the cron job is disabled in settings
            $email_params = [
                'cron_job_name' => 'Transaction Representment',
            ];
            $this->emailexecutor->cronJobDisbaledAlert($email_params);
            return false;
        }
        $this->info("Representing return transactions...");
        // Get the Transaction date based on EST time
        if (Carbon::now()->timezone(ACHECK21_TIMEZONE_FOR_WEBHOOK)->lt(Carbon::now()->timezone(ACHECK21_TIMEZONE_FOR_WEBHOOK)->toDateString() . " 07:00:00")) {
            $transaction_date = Carbon::now()->timezone(ACHECK21_TIMEZONE_FOR_WEBHOOK)->subDays(1)->toDateString();
        } else {
            $transaction_date = Carbon::now()->timezone(ACHECK21_TIMEZONE_FOR_WEBHOOK)->toDateString();
        }
        Log::channel('return-transaction')->debug(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Checking webhook status for date: " . $transaction_date . "...");

        // Check if webhook has been called from ACHECK21 for the transaction date
        $checkForAcheckWebhook = getWebhookDataforSpecificDate($transaction_date);
        if (!empty($checkForAcheckWebhook) && Carbon::now()->gte(Carbon::parse($checkForAcheckWebhook->created_at)->addHour())) {
            $success = getStatus(SUCCESS);
            $failed = getStatus(FAILED);
            //Repay Scheduled Transactions
            $this->_repayScheduledReturnTransactions();
            // represent R01 transactions for finicity linked consumers
            $this->_representR01Transaction($success, $failed);
            $this->_representTransaction($success, $failed);
            // represent transactions for manually linked consumer which are waiting for webhook call
            $this->_representManualLinkedTransactions($success, $failed);
        } else {
            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Couldn't post new transactions to acheck21. Past transactions did not get Processed To Bank.");
            $this->info("Couldn't post new transactions to acheck21. Past transactions did not get Processed To Bank.");
        }
    }

    private function _representR01Transaction($success, $failed)
    {
        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "=========== REPESENTING REPRESENTABLE RETURN TRANSACTIONS FOR FINICITY LINKED BANK ACCOUNTS===============");
        //fetch all the approved by consumer returned transactions
        $transactions = TransactionDetails::join('terminal_master', 'terminal_master.id', '=', 'transaction_details.terminal_id')->join('merchant_stores', 'terminal_master.merchant_store_id', '=', 'merchant_stores.id')->join('registered_merchant_master', 'registered_merchant_master.id', '=', 'merchant_stores.merchant_id')->join('users', 'users.user_id', '=', 'transaction_details.consumer_id')->join('status_master', 'transaction_details.status_id', '=', 'status_master.id')->join('return_reason_masters', 'transaction_details.return_reason', '=', 'return_reason_masters.id')->leftJoin('timezone_masters', 'timezone_masters.id', '=', 'transaction_details.timezone_id')->select('transaction_details.*', 'registered_merchant_master.acheck_account_id', 'return_reason_masters.reason_code as reason_code', 'timezone_masters.timezone_name', 'users.bank_link_type as bank_link_type', DB::raw('transaction_details.consumer_bank_posting_amount as sum'), DB::raw('transaction_details.tip_amount as tip_sum'))->where('users.bank_link_type', 1)->where('transaction_details.is_represented', 0)->where('transaction_details.consumer_approval', 1)->where('transaction_details.approved_to_represent', 1)->where('transaction_details.represent_block', 0)->where('status_master.code', RETURNED)->where('return_reason_masters.canpay_represent', 1)->where('return_reason_masters.bank_login', 0)->whereNull('transaction_details.transaction_ref_no')->get();
        if (sizeof($transactions) != 0) {
            foreach ($transactions as $transaction) {
                $total_amount = ($transaction->sum);
                // storing into history table for each transaction
                $history = new ReturnRepresentHistory();
                $history->consumer_id = $transaction->consumer_id;
                $history->transaction_id = $transaction->id;
                $history->amount = $transaction->amount + $transaction->tip_amount;
                $history->is_manual = 0;
                try {
                    if ($transaction->return_from_primary_account == 1) {
                        // check account balance
                        $bank_account = ConsumerAccountBalance::on(MYSQL_RO)->where('error', 0)->where('account_id', $transaction->transaction_represented)->orderBy("created_at", 'DESC')->first();
                    } else {
                        $bank_account = ConsumerAccountBalance::on(MYSQL_RO)->where('error', 0)->where('account_id', $transaction->account_id)->orderBy("created_at", 'DESC')->first();
                    }
                    if (isset($bank_account->balance)) {
                        // check if account balance is sufficient for this transaction
                        if ($bank_account->balance >= $total_amount) {
                            // if transaction is nonrepresentable post new transaction
                            if ($transaction->return_from_primary_account == 1) {
                                //post the consumer debit transaction
                                Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Posting consumer transaction to acheck21 for transaction amount: " . $total_amount . " with id: " . $transaction->id);
                                $params['amount'] = $total_amount;
                                $params['consumer_id'] = $transaction->consumer_id;
                                $params['acheck_account_id'] = $transaction->acheck_account_id;
                                // send over the current active bank account id
                                $params['account_id'] = $bank_account->account_id;
                                if (env('ACHECK_POSTING')) {
                                    //calling the factory function to create consumer transaction into acheck21
                                    $response = $this->transaction->createConsumerReturnTransaction($params);
                                    $response_decoded = json_decode($response, true);
                                    Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is enabled. Posting in ACHECK21.");
                                } else {
                                    $response_decoded['documentId'] = rand(********, ********);
                                    Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is disabled. Continue with a random number.");
                                }
                                $this->_createTransaction($transaction, $response_decoded['documentId'], $bank_account->account_id);

                                $transaction->account_id = $bank_account->account_id;
                                // update the parent transaction
                                $transaction->is_represented = 1;
                                $transaction->represent_count = $transaction->represent_count + 1;
                                $transaction->save();
                                //adding details to store into history table
                                $history->outcome = "Success. New Transaction posted into acheck21. Executed from daily finicity scheduler.";
                                $history->reason_representable = 0;
                                $history->status_id = $success;
                                Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction represented(created new) for transaction amount: " . $total_amount . " with id: " . $transaction->id);
                            } else { //represent the transaction
                                Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Representing transaction to acheck21 for transaction amount: " . $total_amount . " with id: " . $transaction->id);
                                $reference_transaction = TransactionDetails::join('status_master', 'transaction_details.status_id', '=', 'status_master.id')->where('transaction_details.transaction_ref_no', $transaction->id)->where('status_master.code', PROCESSED_FOR_ACHECK21)->orderBy("transaction_details.created_at", 'ASC')->first();
                                if (env('ACHECK_POSTING')) {
                                    $response = $this->acheck->representTransaction($reference_transaction->acheck_document_id);
                                    Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is enabled. Posting in ACHECK21.");
                                } else {
                                    $response = 204;
                                    Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is disabled. Continue with static 204 response.");
                                }

                                if ($response == 204) {
                                    $this->_createTransaction($transaction, $reference_transaction->acheck_document_id, $transaction->account_id);
                                    // update the parent transaction
                                    $transaction->represent_block = 1;
                                    $transaction->is_represented = 1;
                                    $transaction->transaction_represented = $transaction->account_id;
                                    $transaction->represent_count = $transaction->represent_count + 1;
                                    $transaction->save();
                                    //adding details to store into history table
                                    $history->outcome = "Success. Old transaction represented into acheck21. Executed from daily scheduler.";
                                    $history->reason_representable = 1;
                                    $history->status_id = $success;
                                    Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction represented for transaction amount: " . $total_amount . " with id: " . $transaction->id);
                                } else {
                                    //adding details to store into history table
                                    $history->outcome = $response;
                                    $history->reason_representable = 1;
                                    $history->status_id = $failed;
                                    Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck21 could not represent the transaction for transaction amount: " . $total_amount . " with id: " . $transaction->id);
                                }
                            }
                        } else {
                            //adding details to store into history table
                            $history->outcome = "Consumer transaction amount: " . $total_amount . " but account balance is: " . $bank_account->balance;
                            $history->status_id = $failed;
                            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer transaction amount: " . $total_amount . " but account balance is: " . $bank_account->balance);
                        }
                    } else {
                        //adding details to store into history table
                        $history->outcome = "Consumer transaction amount: " . $total_amount . " but no account balance found";
                        $history->status_id = $failed;
                        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer transaction amount: " . $total_amount . " but no account balance found with transaction id: " . $transaction->id . " for consumer id: " . $transaction->consumer_id);
                    }
                    $history->save();
                } catch (\Exception $e) {
                    //adding details to store into history table
                    $history->outcome = $e;
                    $history->status_id = $failed;
                    Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "There was a problem trying to represent transaction with id: " . $transaction->id, [EXCEPTION => $e]);
                    $history->save();
                    continue;
                }
            }
            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transactions represented successfully.");
            $this->info("Transactions represented successfully.");
        } else {
            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No transactions found to represent.");
            $this->info("No transactions found to represent.");
        }
    }
    /**
     * This function fetches bank balance for all consumers who has added bank from finicity and keeps record in the database
     */
    private function _representTransaction($success, $failed)
    {
        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "=========== REPESENTING CONSUMER APPROVED RETURN TRANSACTIONS FOR FINICITY LINKED BANK ACCOUNTS===============");
        //fetch all the approved by consumer returned transactions
        $transactions = TransactionDetails::join('terminal_master', 'terminal_master.id', '=', 'transaction_details.terminal_id')->join('merchant_stores', 'terminal_master.merchant_store_id', '=', 'merchant_stores.id')->join('registered_merchant_master', 'registered_merchant_master.id', '=', 'merchant_stores.merchant_id')->join('users', 'users.user_id', '=', 'transaction_details.consumer_id')->join('status_master', 'transaction_details.status_id', '=', 'status_master.id')->join('return_reason_masters', 'transaction_details.return_reason', '=', 'return_reason_masters.id')->leftJoin('timezone_masters', 'timezone_masters.id', '=', 'transaction_details.timezone_id')->select('transaction_details.*', 'registered_merchant_master.acheck_account_id', 'return_reason_masters.canpay_represent as canpay_represent', 'timezone_masters.timezone_name', 'return_reason_masters.new_banking as new_banking', 'users.bank_link_type as bank_link_type', DB::raw('transaction_details.consumer_bank_posting_amount as sum'), DB::raw('transaction_details.tip_amount as tip_sum'))->where('users.bank_link_type', 1)->where('transaction_details.is_represented', 0)->where('transaction_details.consumer_approval', 1)->where('transaction_details.represent_block', 1)->where('status_master.code', RETURNED)->whereNull('transaction_details.transaction_ref_no')->get();
        if (sizeof($transactions) != 0) {
            foreach ($transactions as $transaction) {
                $total_amount = $transaction->sum;
                // storing into history table for each transaction
                $history = new ReturnRepresentHistory();
                $history->consumer_id = $transaction->consumer_id;
                $history->transaction_id = $transaction->id;
                $history->amount = $total_amount;
                $history->is_manual = 0;
                try {
                    if ($transaction->return_from_primary_account == 1) {
                        $bank_account = BankAccountInfo::on(MYSQL_RO)->where('id', $transaction->transaction_represented)->first();
                    } else {
                        $bank_account = BankAccountInfo::on(MYSQL_RO)->where('id', $transaction->account_id)->first();
                    }
                    // if transaction should be posted as new transaction
                    if ($transaction->return_from_primary_account == 1) {
                        //post the consumer debit transaction
                        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Posting consumer transaction to acheck21 for transaction amount: " . $total_amount . " with id: " . $transaction->id);
                        $params['amount'] = $total_amount;
                        $params['consumer_id'] = $transaction->consumer_id;
                        $params['acheck_account_id'] = $transaction->acheck_account_id;
                        // send over the current active bank account id
                        $params['account_id'] = $bank_account->id;

                        if (env('ACHECK_POSTING')) {
                            //calling the factory function to create consumer transaction into acheck21
                            $response = $this->transaction->createConsumerReturnTransaction($params);
                            $response_decoded = json_decode($response, true);
                            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is enabled. Posting in ACHECK21.");
                        } else {
                            $response_decoded['documentId'] = rand(********, ********);
                            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is disabled. Continue with a random number.");
                        }
                        $this->_createTransaction($transaction, $response_decoded['documentId'], $bank_account->id);

                        $transaction->account_id = $bank_account->id;
                        // update the parent transaction
                        $transaction->is_represented = 1;
                        $transaction->represent_count = $transaction->represent_count + 1;
                        $transaction->save();
                        //adding details to store into history table
                        $history->outcome = "Success. New Transaction posted into acheck21. Executed from daily finicity scheduler.";
                        $history->reason_representable = 0;
                        $history->status_id = $success;
                        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction represented(created new) for transaction amount: " . $total_amount . " with id: " . $transaction->id);
                    } else { //represent the transaction
                        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Representing transaction to acheck21 for transaction amount: " . $total_amount . " with id: " . $transaction->id);
                        $reference_transaction = TransactionDetails::join('status_master', 'transaction_details.status_id', '=', 'status_master.id')->where('transaction_details.transaction_ref_no', $transaction->id)->where('status_master.code', PROCESSED_FOR_ACHECK21)->orderBy("transaction_details.created_at", 'ASC')->first();

                        if (env('ACHECK_POSTING')) {
                            $response = $this->acheck->representTransaction($reference_transaction->acheck_document_id);
                            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is enabled. Posting in ACHECK21.");
                        } else {
                            $response = 204;
                            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is disabled. Continue with static 204 response.");
                        }
                        if ($response == 204) {
                            $this->_createTransaction($transaction, $reference_transaction->acheck_document_id, $transaction->account_id);
                            // update the parent transaction
                            $transaction->is_represented = 1;
                            $transaction->represent_count = $transaction->represent_count + 1;
                            $transaction->save();
                            //adding details to store into history table
                            $history->outcome = "Success. Old transaction represented into acheck21. Executed from daily scheduler.";
                            $history->reason_representable = 1;
                            $history->status_id = $success;
                            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction represented for transaction amount: " . $total_amount . " with id: " . $transaction->id);
                        } else {
                            //adding details to store into history table
                            $history->outcome = $response;
                            $history->reason_representable = 1;
                            $history->status_id = $failed;
                            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck21 could not represent the transaction for transaction amount: " . $transaction->consumer_bank_posting_amount . " with id: " . $transaction->id);
                        }
                    }
                    $history->save();
                } catch (\Exception $e) {
                    //adding details to store into history table
                    $history->outcome = $e;
                    $history->status_id = $failed;
                    Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "There was a problem trying to represent transaction with id: " . $transaction->id, [EXCEPTION => $e]);
                    $history->save();
                    continue;
                }
            }
            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transactions represented successfully.");
            $this->info("Transactions represented successfully.");
        } else {
            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No transactions found to represent.");
            $this->info("No transactions found to represent.");
        }
    }
    /**
     * This function represents manually linked consumer trasnactions who have opted to pay now but waiting for webhook call
     */
    private function _representManualLinkedTransactions($success, $failed)
    {
        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "=========== REPESENTING PAY NOW RETURN TRANSACTIONS FOR MANUALLY LINKED BANK ACCOUNTS===============");
        //fetch all the approved by consumer returned transactions
        $transactions = TransactionDetails::join('terminal_master', 'terminal_master.id', '=', 'transaction_details.terminal_id')->join('merchant_stores', 'terminal_master.merchant_store_id', '=', 'merchant_stores.id')->join('registered_merchant_master', 'registered_merchant_master.id', '=', 'merchant_stores.merchant_id')->join('status_master', 'transaction_details.status_id', '=', 'status_master.id')->join('return_reason_masters', 'transaction_details.return_reason', '=', 'return_reason_masters.id')->leftJoin('timezone_masters', 'timezone_masters.id', '=', 'transaction_details.timezone_id')->join('users', 'users.user_id', '=', 'transaction_details.consumer_id')->select('transaction_details.*', 'return_reason_masters.canpay_represent as canpay_represent', 'return_reason_masters.new_banking as new_banking', 'timezone_masters.timezone_name', 'registered_merchant_master.acheck_account_id')->where('transaction_details.is_represented', 0)->where('users.bank_link_type', 0)->where('transaction_details.represent_block', 1)->where('status_master.code', RETURNED)->get();
        if (sizeof($transactions) != 0) {
            foreach ($transactions as $transaction) {
                $total_amount = $transaction->consumer_bank_posting_amount;
                // storing into history table for each transaction
                $history = new ReturnRepresentHistory();
                $history->consumer_id = $transaction->consumer_id;
                $history->transaction_id = $transaction->id;
                $history->amount = $transaction->amount + $transaction->tip_amount;
                $history->is_manual = 1;
                try {
                    // if transaction is nonrepresentable post new transaction
                    if ($transaction->return_from_primary_account == 1) {
                        //post the consumer debit transaction
                        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Posting consumer transaction to acheck21 for transaction amount: " . $total_amount . " with id: " . $transaction->id);
                        $bank_account_info = BankAccountInfo::on(MYSQL_RO)->where('id', $transaction->transaction_represented)->first();
                        $history->reason_representable = 0;
                        if (isset($bank_account_info->id)) {
                            $params['amount'] = $total_amount;
                            $params['consumer_id'] = $transaction->consumer_id;
                            $params['acheck_account_id'] = $transaction->acheck_account_id;
                            // send over the current active bank account id
                            $params['account_id'] = $bank_account_info->id;

                            if (env('ACHECK_POSTING')) {
                                //calling the factory function to create consumer transaction into acheck21
                                $response = $this->transaction->createConsumerReturnTransaction($params);
                                $response_decoded = json_decode($response, true);
                                Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is enabled. Posting in ACHECK21.");
                            } else {
                                $response_decoded['documentId'] = rand(********, ********);
                                Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is disabled. Continue with a random number.");
                            }

                            // update transaction account id with new linked account id
                            $this->_createTransaction($transaction, $response_decoded['documentId'], $bank_account_info->id);
                            $transaction->account_id = $bank_account_info->id;
                            // update the parent transaction
                            $transaction->is_represented = 1;
                            $transaction->represent_count = $transaction->represent_count + 1;
                            $transaction->save();
                            //adding details to store into history table
                            $history->outcome = "Success. New Transaction posted into acheck21. Executed from daily pay now scheduler.";
                            $history->status_id = $success;
                            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction represented(created new) for transaction amount: " . $total_amount . " with id: " . $transaction->id);
                        } else {
                            //adding details to store into history table
                            $history->outcome = "Consumer transaction amount: " . $total_amount . " but no active bank account found with consumer id: " . $transaction->consumer_id . " for transaction id: " . $transaction->id;
                            $history->status_id = $success;
                            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer transaction amount: " . $total_amount . " but no active bank account found with consumer id: " . $transaction->consumer_id . " for transaction id: " . $transaction->id);
                        }
                    } else { //represent the transaction
                        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Representing transaction to acheck21 for transaction amount: " . $total_amount . " with id: " . $transaction->id);
                        $reference_transaction = TransactionDetails::join('status_master', 'transaction_details.status_id', '=', 'status_master.id')->where('transaction_details.transaction_ref_no', $transaction->id)->where('status_master.code', PROCESSED_FOR_ACHECK21)->orderBy("transaction_details.created_at", 'ASC')->first();

                        if (env('ACHECK_POSTING')) {
                            $response = $this->acheck->representTransaction($reference_transaction->acheck_document_id);
                            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is enabled. Posting in ACHECK21.");
                        } else {
                            $response = 204;
                            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is disabled. Continue with static 204 response.");
                        }
                        if ($response == 204) {
                            $this->_createTransaction($transaction, $reference_transaction->acheck_document_id, $transaction->id);
                            // update the parent transaction
                            $transaction->is_represented = 1;
                            $transaction->represent_count = $transaction->represent_count + 1;
                            $transaction->save();
                            //adding details to store into history table
                            $history->outcome = "Success. Old transaction represented into acheck21. Executed from daily pay now scheduler.";
                            $history->reason_representable = 1;
                            $history->status_id = $success;
                            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction represented for transaction amount: " . $total_amount . " with id: " . $transaction->id);
                        } else {
                            //adding details to store into history table
                            $history->outcome = $response;
                            $history->reason_representable = 1;
                            $history->status_id = $failed;
                            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck21 could not represent the transaction for transaction amount: " . $total_amount . " with id: " . $transaction->id);
                        }
                    }
                    $history->save();
                } catch (\Exception $e) {
                    //adding details to store into history table
                    $history->outcome = $e;
                    $history->status_id = $failed;
                    Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "There was a problem trying to represent transaction with id: " . $transaction->id, [EXCEPTION => $e]);
                    $history->save();
                    continue;
                }
            }
            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transactions represented successfully.");
            $this->info("Transactions represented successfully.");
        } else {
            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No pay now return transactions found to represent.");
            $this->info("No transactions found to represent.");
        }
    }
    private function _createTransaction($transaction, $doc_id, $account_id)
    {
        //create a new transaction
        $transaction_details = new TransactionDetails();
        $transaction_details->transaction_number = $transaction->transaction_number;
        $transaction_details->transaction_ref_no = $transaction->id;
        $transaction_details->user_id = $transaction->user_id;
        $transaction_details->consumer_id = $transaction->consumer_id;
        $transaction_details->terminal_id = $transaction->terminal_id;
        $transaction_details->account_id = $account_id;
        $transaction_details->transaction_time = Carbon::now();
        $transaction_details->local_transaction_time = Carbon::now($transaction->timezone_name);
        $transaction_details->local_transaction_year = date("Y", strtotime($transaction_details->local_transaction_time));
        $transaction_details->local_transaction_month = date("m", strtotime($transaction_details->local_transaction_time));
        $transaction_details->local_transaction_date = date("Y-m-d", strtotime($transaction_details->local_transaction_time));
        $transaction_details->timezone_id = $transaction->timezone_id;
        $transaction_details->consumer_bank_posting_amount = $transaction->consumer_bank_posting_amount;
        $transaction_details->reward_amount_used = $transaction->reward_amount_used;
        $transaction_details->reward_point_used = $transaction->reward_point_used;
        $transaction_details->return_representment_reward_deduction_amount = $transaction->return_representment_reward_deduction_amount;
        $transaction_details->amount = $transaction->amount;
        $transaction_details->tip_amount = $transaction->tip_amount;
        $transaction_details->tip_type = $transaction->tip_type;
        $transaction_details->tip_add_time = $transaction->tip_add_time;
        $transaction_details->used_qr_id = $transaction->used_qr_id;
        $transaction_details->status_id = getStatus(PROCESSED_FOR_ACHECK21);
        $transaction_details->transaction_type_id = $transaction->transaction_type_id;
        $transaction_details->transaction_place = ACHECK21;
        $transaction_details->acheck_document_id = $doc_id;
        $transaction_details->is_represented = 1;
        $transaction_details->save();
        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction details was stored successfully.");

        //Add Document ID in Acheck21DocumentIdHistory table and update Previous Ignore Flags
        $this->_addDocumentIdHistory($transaction_details);
    }

    private function _addDocumentIdHistory($transaction)
    {
        //Update the ignore flags for the previous flags
        Acheck21DocumentIdHistory::where(['transaction_ref_no' => $transaction->transaction_ref_no])->update(['ignore_flag' => 1]);

        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Ignore Flag updated for Transation Ref No. : " . $transaction->transaction_ref_no);

        //Add new Document ID in Acheck21DocumentIdHistory table
        $document_history = new Acheck21DocumentIdHistory();
        $document_history->transaction_id = $transaction->id;
        $document_history->transaction_ref_no = $transaction->transaction_ref_no;
        $document_history->amount = $transaction->amount + $transaction->tip_amount;
        $document_history->acheck_document_id = $transaction->acheck_document_id;
        $document_history->save();

        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Document ID added for Transation ID : " . $transaction->id);
    }

    private function _repayScheduledReturnTransactions()
    {
        $bankPostingDate = date('Y-m-d');
        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "=========== Fetch the Transactions for Repayment for Date : " . $bankPostingDate);

        // fetch all the approved by consumer returned transactions
        $repaymentReturnSchedules = ReturnTransactionRepaymentSchedule::where(['repayment_bank_processing_date' => $bankPostingDate, 'repayment_initiated' => 0])->get();

        foreach ($repaymentReturnSchedules as $schedules) {
            //Update the Transaction for Repayment
            $transaction = TransactionDetails::where("id", $schedules->transaction_id)->first();
            $transaction->represent_block = 1;
            $transaction->approved_to_represent = 1;
            $transaction->consumer_approval = 1;
            $transaction->repayment_scheduled = 0;
            $transaction->save();

            // update the Return Transaction Repayment Schedule that return is Initiated
            $repaymentSchedule = ReturnTransactionRepaymentSchedule::where(['id' => $schedules->id])->first();
            $repaymentSchedule->repayment_initiated = 1;
            $repaymentSchedule->save();

            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "=========== Repayment Initiated for Transaction ID  : " . $schedules->transaction_id);
        }

        return true;
    }
}
