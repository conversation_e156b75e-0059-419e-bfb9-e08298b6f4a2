<?php

namespace App\Http\Factories\Mx;

use App\Http\Clients\MxHttpClient;
use App\Http\Factories\Firebase\FirebaseFactory;
use App\Http\Factories\PurchasePower\PurchasePowerFactory;
use App\Models\BankAccountInfo;
use App\Models\MxBank;
use App\Models\MxCheckBalanceCallHistory;
use App\Models\MxIdentifyMemberCallHistory;
use App\Models\User;
use App\Models\UserBankAccountInfo;
use App\Models\UserValidationCredentials;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

/**
 *
 * @package App\Http\Factories\IdValidator
 */
class MxFactory implements MxInterface
{

    private $mxClient = null;

    public function __construct()
    {
        $this->mxClient = new MxHttpClient();
        $this->purchasePower = new PurchasePowerFactory();
        $this->firebase = new FirebaseFactory();
    }

    /**
     * This function will create consumer in MX
     *
     * @return mixed
     */
    public function createConsumer($params)
    {
        try {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Process started for creation consumer in MX...");
            $response = json_decode($this->mxClient->createMxUser($params), true);
            return ['code' => SUCCESS, 'message' => null, 'data' => $response];
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while creating consumer in MX.", [EXCEPTION => $e]);
            return ['code' => FAIL, 'message' => trans('message.consuer_creation_fail'), 'data' => null];
        }
    }

    /**
     * This function will create a widget URL in MX based on the bank code
     *
     * @return mixed
     */
    public function generateWidgetUrl($params)
    {
        try {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Process started for creation of widget URL in MX...");
            // Check if the connection repair is required or a new connection needs to be created
            if (isset($params['repair_connection']) && $params['repair_connection'] == 1) {
                $response = json_decode($this->mxClient->createConnectionRepairWidgetURL($params), true);
            } else {
                $response = json_decode($this->mxClient->createWidgetURL($params), true);
            }
            return ['code' => SUCCESS, 'message' => null, 'data' => $response];
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while creating widget URL in MX.", [EXCEPTION => $e]);
            return ['code' => FAIL, 'message' => trans('message.widget_url_creation_failed'), 'data' => null];
        }
    }

    /**
     * This function will fetch the user list from MX
     *
     * @return mixed
     */
    public function getUserList($params)
    {
        try {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Process started for fetching consumer list in MX...");
            $response = json_decode($this->mxClient->getUserList($params), true);
            return $response;
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while fetching consumer list in MX.", [EXCEPTION => $e]);
            return null;
        }
    }

    /**
     * This function will delete the user from MX
     *
     * @return mixed
     */
    public function deleteUser($params)
    {
        try {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Process started for deletion of consumer in MX...");
            $response = $this->mxClient->deleteUser($params);
            return $response;
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while deletion of consumer in MX.", [EXCEPTION => $e]);
            return null;
        }
    }

    /**
     * This function will fetch the bank accounts of a specific user from MX
     *
     * @return mixed
     */
    public function getUserAccounts($data)
    {
        try {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Process started for fetching accounts for a consumer in MX...");
            $data['page'] = 1;
            $accounts = [];
            while (true) {
                $apiResponse = json_decode($this->mxClient->getUserAccounts($data), true);
                $accounts = array_merge($accounts, $apiResponse['accounts']);
                if ($apiResponse['pagination']['current_page'] < $apiResponse['pagination']['total_pages']) {
                    $data['page'] = $data['page'] + 1;
                } else {
                    break; // All entries are loaded, exit the loop
                }
            }
            $response = [];
            $higest_balance = 0;
            $checking = false;
            $primary_account = '';
            $blocked_routing_nos = getBlockedRoutingNos(); // Fetch the blocked Routing Numbers
            $inc = 0;

            // Params to call Member Identtity API. This APi needs to be called for once before calling the Account Owner API. So we are calling this here after Account Fetch API is called
            $params['user_guid'] = $data['user_guid'];
            $params['member_guid'] = $data['member_guid'];
            foreach ($accounts as $account) {
                if ($account['member_guid'] === $data['member_guid']) { // Check the account user has just linked
                    // Check if the account is checking or savings. Otherwise reject it.
                    if (strtolower($account['type']) == CHECKING || strtolower($account['type']) == SAVINGS) {
                        if (isset($account['availableBalance'])) {
                            $balance = $account['available_balance'] > $account['balance'] ? $account['available_balance'] : $account['balance'];
                        } else {
                            $balance = $account['balance'];
                        }
                        //check whether accountType and accountNumberDisplay exists in DB
                        $bank_data = [
                            'realAccountNumberLast4' => $account['account_number'] ?? null,
                            'type' => $account['type'],
                        ];
                        $existing_bank_account = checkBankLastFourDigitExists($data, $bank_data);
                        if ($existing_bank_account) {
                            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Bank account exists for realAccountNumberLast4: " . $bank_data['realAccountNumberLast4'] . " and Account type: " . $bank_data['type']);
                            $routing_no = $existing_bank_account->routing_no;
                            $account_no = $existing_bank_account->account_no;
                        } else {
                            // Get the account number details for the account
                            $data['account_guid'] = $account['guid'];
                            $account_details = json_decode($this->mxClient->getUserAccountsNumber($data), true)['account_numbers'][0] ?? '';
                            $routing_no = $account_details['routing_number'] ?? '';
                            $account_no = $account_details['account_number'] ?? '';
                        }
                        // Check if the ACH details are vailable or not. If not then skip the account.
                        if ($routing_no != '' && $account_no != '') {
                            // Check if the routing number exists in the Financial institution table for bank id
                            checkFinancialInstitutionRoutingNoExists($data['bank_id'], $routing_no);
                            $routing_no_exists = fedRoutingNumberExistsCheck($data, $routing_no);
                            if ($routing_no_exists) {
                                // Prepare the response here to match with the other banking solutions' response from factory
                                $response[] = [
                                    'id' => $account['guid'],
                                    'accountNumberDisplay' => $bank_data['realAccountNumberLast4'] ? substr($bank_data['realAccountNumberLast4'], -4) : '',
                                    'type' => strtolower($account['type']),
                                    'balance' => $balance,
                                    'balanceDate' => '',
                                    'institutionId' => $account['institution_code'],
                                    'realAccountNumber' => $account_no,
                                    'routingNumber' => $routing_no,
                                    'mx_consumer_id' => $data['user_guid'],
                                    'mx_member_guid' => $data['member_guid'],
                                ];
                                if ($balance >= $higest_balance && strtolower($account['type']) === CHECKING && !key_value_pair_exists($blocked_routing_nos, $routing_no, strtolower($account['type']))) {
                                    $higest_balance = $balance;
                                    $primary_account = $inc;
                                    $checking = true;
                                }
                                if (!$checking) {
                                    if ($balance >= $higest_balance && !key_value_pair_exists($blocked_routing_nos, $routing_no, strtolower($account['type']))) {
                                        $higest_balance = $balance;
                                        $primary_account = $inc;
                                    }
                                }
                                $inc++;
                                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Account details for Account with ID: " . $account['guid'] . " fetched and stored successfully in the database.");
                            } else {
                                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Account details for Account with ID: " . $account['guid'] . " skipped due to routing No and Bank name not matched in our FinancialInstitutionRoutingNumber DB. Routing No: " . $routing_no . " , Bank Name: " . $data['bank_name'] . ".");
                            }
                        } else {
                            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Account details for Account with ID: " . $account['guid'] . " skipped due to non availability of account no. and routing no.");
                        }
                    } else {
                        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Account details for Account with ID: " . $account['guid'] . " skipped due to Account Type: " . $account['type']);
                    }
                } else {
                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Account details for Account with ID: " . $account['guid'] . " skipped due to Old Linked account as member id did not matched.");
                }
            }
            // Set the primary account flag
            if (is_int($primary_account)) {
                $response[$primary_account]['primary'] = 1;
            }

            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "MX response for Account details: " . json_encode($response));

            // sort the response w.r.t balance in ascending order
            usort($response, function ($a, $b) {
                return $a['balance'] <=> $b['balance'];
            });
            Log::info("Response after sorting...");
            Log::info($response);
            return ['code' => SUCCESS, 'message' => null, 'data' => $response];
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while fetching accounts for a consumer list in MX.", [EXCEPTION => $e]);
            return ['code' => FAIL, 'message' => $e->getMessage(), 'data' => null];
        }
    }

    /**
     * getMxConsumer
     * This function will return the mc consumer id for existing user or create a new one if not found
     * @param  mixed $params
     * @return void
     */
    public function getMxConsumer($params)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Process started for fetching consumer id for a consumer in MX...");

        $result = UserValidationCredentials::where('phone', $params['phoneNo'])->orderBy('created_at', 'DESC')->first();

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "User validation credentials :" . $result);

        if (empty($result)) {
            $response['message'] = trans('message.required_identity_vaidation');
            return json_encode($response, true);
        }

        if (empty($result['mx_consumer_id'])) {
            // If consumer does not exist, create a new one
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No existing MX consumer found. Creating a new one...");

            $data = json_decode($this->mxClient->createMxUser($params), true);
            $mx_consumer_id = $data['user']['guid'];
            $mx_canpay_user_id = $data['user']['id'];

            // Store the id into the database
            $result->mx_consumer_id = $mx_consumer_id;
            $result->mx_canpay_user_id = $mx_canpay_user_id;
            $result->save();
        } else {
            // Use existing MX consumer id
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Using existing MX consumer id: " . $result['mx_consumer_id']);
            $mx_consumer_id = $result['mx_consumer_id'];
        }

        return $mx_consumer_id;
    }

    /**
     * getConsumerAccountBalance
     * This function will return a consumer account details based on the account id
     * @param  mixed $account_id
     * @return void
     */
    public function getConsumerAccountBalance($account_id, $consumer_id, $for_scheduler = false)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Process started for fetching a specific account details for a consumer in MX...");
        /// checking if testing mode is set then return the custom bank balance
        if (env('TESTING_MODE')) {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "=====================TESTING MODE ENABLED=======================");
            $balance = env('CUSTOM_BANK_BALANCE');
            $originalResponse = "Testing Mode. Not calling MX API.";
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Returning custom balance :" . $balance);
            return $for_scheduler != '' ? ["availableBalanceAmount" => $balance, "balance" => $balance, "effectiveBalance" => $balance, "banking_solution_response" => $originalResponse] : $balance;
        }
        try {
            $account_info = UserBankAccountInfo::where('account_id', $account_id)->first();
            $params['user_guid'] = $account_info->mx_consumer_id;
            $params['account_guid'] = $account_id;
            $getAccountDetails = $this->mxClient->getAccountDetails($params);
            $account_data = json_decode($getAccountDetails, true);
            if (isset($account_data['account'])) {
                $account_details = $account_data['account'];
                if (isset($account_details['available_balance'])) {
                    $available_balance = $account_details['available_balance'];
                    if ($account_details['available_balance'] > $account_details['balance']) {
                        $balance = $account_details['available_balance'];
                    } else {
                        $balance = $account_details['balance'];
                    }
                } else {
                    $balance = $account_details['balance'];
                    $available_balance = $balance;
                }
                return $for_scheduler != '' ? ["availableBalanceAmount" => $available_balance, "balance" => $account_details['balance'], "effectiveBalance" => $balance, "banking_solution_response" => $getAccountDetails] : $balance;
            } else {
                Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No account information foumd for user:  " . $consumer_id . ".");
                return $for_scheduler != '' ? ["availableBalanceAmount" => 0, "balance" => 0, "error" => 1, "effectiveBalance" => 0, "banking_solution_response" => "_ERR_No Account Details Found."] : false;
            }
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured while trying to fetch bank account information for user:  " . $consumer_id . ".", [EXCEPTION => $e]);
            return $for_scheduler != '' ? ["availableBalanceAmount" => 0, "balance" => 0, "error" => 1, "effectiveBalance" => 0, "banking_solution_response" => "_ERR_" . $e->getMessage()] : false;
        }
    }

    /**
     * This api refreshes balance and purchase power for a consumer
     */
    public function getConsumerRefreshBalance($active_bank, $user_id, $source)
    {
        // Calculate highest transaction bonus for the user
        try {
            $highestTransactionBonus = calculateHighestTransactionBonus($user_id);
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Highest transaction bonus calculated: " . $highestTransactionBonus . " for consumer ID: " . $user_id);
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Error calculating highest transaction bonus for consumer ID: " . $user_id . ". Error: " . $e->getMessage());
            $highestTransactionBonus = null;
        }
        $active_status = getStatus(BANK_ACTIVE);
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Fetching Balance for MX... ");
        $balanceNPurchasePower = $this->getConsumerAccountBalance($active_bank->account_id, $user_id, 1);
        if (!isset($balanceNPurchasePower['error'])) {
            $checkParellelAlgoEnabled = getSettingsValue('prallel_pp_algo', 0); // Check wheather the parrellel purchase power algo is enabled or disabled
            $checkNewAlgoEnabled = getSettingsValue('enable_new_pp_algo', 0); // Check wheather the new purchase power algo is enabled or disabled
            $user_details = User::where('user_id', $user_id)->first();
            // Prepare data for purchase power calculation
            $purchasePowerCalculationData = [
                'account_id' => $active_bank->id,
                'account_no' => $active_bank->account_no,
                'balance' => $balanceNPurchasePower["effectiveBalance"],
                'updated_at' => $active_bank->updated_at,
                'parellel_pp_enabled' => $checkParellelAlgoEnabled,
                'enable_new_pp_algo' => $checkNewAlgoEnabled,
                'consumer_registration_date' => $user_details->existing_user == 0 ? $user_details->created_at : $user_details->migrated_at,
                'total_account_count' => 1,
                'current_account' => 1,
                'user_details' => $user_details,
                'highest_transaction_bonus' => $highestTransactionBonus,
            ];
            $purchase_power_data = $this->purchasePower->calculatePurchasePower($purchasePowerCalculationData);
            $purchase_power = $purchase_power_data['purchase_power'];
            $data['user_id'] = $user_id;
            $data['account_id'] = $active_bank->id;
            $data['balance'] = $balanceNPurchasePower["effectiveBalance"];
            $data['response_raw_balance'] = $balanceNPurchasePower['balance'];
            $data['response_available_balance'] = $balanceNPurchasePower['availableBalanceAmount'];
            $data['purchase_power'] = $purchase_power;
            $data['purchase_power_source'] = $user_details->purchase_power_source;
            $data['source'] = $source;
            $one_time_refresh = $source == ONE_TIME_BALANCE_FETCH ? 1 : 0;
            $data['one_time_refresh'] = $one_time_refresh;
            $data['banking_solution_response'] = $balanceNPurchasePower['banking_solution_response'];
            addCustomerAccounts($data);

            if ($active_status == $active_bank->status) {
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Calculated purchase power for User ID: " . $user_id . " is : $" . $purchase_power);
                User::where(['user_id' => $user_id, 'disable_automatic_purchase_power' => 0])->update(array('purchase_power' => $purchase_power, 'refresh_balance_called' => 1));
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Consumer purchase power updated into database successfully.");
            }
            $response = [
                'code' => SUCCESS,
                'message' => trans('message.balance_refresh'),
                'data' => null,
            ];
        } else {
            $response = [
                'code' => SUCCESS,
                'message' => null,
                'data' => 0,
            ];
        }
        return $response;
    }

    /**
     * getInstitutionList
     * This function will fetch the institution list from MX
     * @return void
     */
    public function getInstitutionList($params)
    {
        try {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Process started for fetching institution list in MX...");
            $response = json_decode($this->mxClient->getInstitutionList($params), true);
            return $response;
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while fetching institution list in MX.", [EXCEPTION => $e]);
            return null;
        }
    }

    /**
     * getAccountOwnerInfo
     * This function will fetch the account owner information from akoya
     * @param  mixed $params
     * @return void
     */
    public function getAccountOwnerInfo($params)
    {
        try {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Fetch process started for account owner information from MX... ");

            $response = json_decode($this->mxClient->getAccountOwners($params), true);
            Log::info($response);
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Account owner information fetched successfully MX.");
            return $response;
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while fetching owner information in MX.", [EXCEPTION => $e]);
            return null;
        }
    }

    /**
     * identifyMember
     * This function will call identify member api which is required for calling the account owner api
     * @param  mixed $params
     * @return void
     */
    public function identifyMember($params)
    {
        try {
            if ($params->mx_user_action_needed == 1 || $params->mx_non_actionable_status_detected == 1) {
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Account is in a problematic state. Exiting the identify member process...");
                return ['status' => FAIL, 'message' => 'User action needed'];
            }

            $data = [
                'user_guid' => $params->mx_consumer_id,
                'member_guid' => $params->mx_member_guid,
            ];

            $timeLimit = Carbon::now()->subMinutes(10); // Get the time 10 minutes ago
            if (!isset($params->skip_identify_member_call) && $params->skip_identify_member_call != 1) {
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Checking for recent identify member call in last 10 minutes...");

                // Check the MxIdentifyMemberCallHistory table for any records in the last 10 minutes
                $recentCall = MxIdentifyMemberCallHistory::where('consumer_id', $params->user_id)
                    ->where('mx_user_id', $params->mx_consumer_id)
                    ->where('mx_member_id', $params->mx_member_guid)
                    ->where('created_at', '>=', $timeLimit)
                    ->first();

                if ($recentCall) { // Return an error if an identify member call was made within the last 10 minutes
                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Recent identify member call found for admin within the last 10 minutes.");
                    return ['status' => FAIL, 'message' => 'An identify member request was made within the last 10 minutes. Please try in few minutes.'];
                }

                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Identify Member API call started...");
                $identifyMemberData = json_decode($this->mxClient->identifyMember($data), true);
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Decoded response from identify member API for source: " . $params->source . ": ", $identifyMemberData);

                if (!isset($identifyMemberData['member'])) {
                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - No member data found in the response.");
                    return ['status' => FAIL, 'message' => 'No member data found'];
                }

                $memberStatus = $identifyMemberData['member'];

                $mx_identify_call_history = MxIdentifyMemberCallHistory::create([
                    'source' => $params->source,
                    'consumer_id' => $params->user_id,
                    'mx_user_id' => $params->mx_consumer_id,
                    'mx_member_id' => $params->mx_member_guid,
                ]);
            } else {
                $mx_identify_call_history = MxIdentifyMemberCallHistory::find($params->record_id); // Get the record from the table
            }

            if (isset($params->check_member_status) && $params->check_member_status == 1) {
                $memberStatus = $this->_checkMemberStatus($params, $data, $mx_identify_call_history);
            }

            if (handleProblemStatuses($memberStatus, $params->user_id, $params, $params->source)) {
                return ['status' => FAIL, 'message' => 'Problem status found'];
            }

            if ($memberStatus['connection_status'] == MX_CONNECTED && !$memberStatus['is_being_aggregated']) {
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Member status check call completed successfully. Proceeding to call the account owner API...");
                $this->_processAccountOwners($data, $mx_identify_call_history, $params->starttime);
                return ['status' => 'success', 'message' => 'Account owner details processed successfully'];
            }

            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Identify Member process completed but account owner details were not processed.");
            return ['status' => 'success', 'message' => 'Account owner details processed successfully'];
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Exception while fetching Identify Member in MX.", ['exception' => $e]);
            return ['status' => FAIL, 'message' => 'Exception occurred: ' . $e->getMessage()];
        }
    }

    /**
     * _checkMemberStatus
     * This function will check the member status
     * @param  mixed $params
     * @param  mixed $data
     * @param  mixed $mx_identify_call_history
     * @return void
     */
    private function _checkMemberStatus($params, $data, $mx_identify_call_history)
    {
        $memberStatusData = json_decode($this->mxClient->getMemberStatus($data), true);

        if (!isset($memberStatusData['member'])) {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - No member status data found.");
            return false;
        }

        $memberStatus = $memberStatusData['member'];

        // Handle problem statuses
        if (handleProblemStatuses($memberStatus, $params->user_id, $params, $params->source)) {
            return false;
        }

        return $memberStatus;
    }

    /**
     * Process account owners and update related tables
     *
     * @param array $data
     * @param object $mx_identify_call_history
     * @param float $startTime
     * @return void
     */
    private function _processAccountOwners($data, $mx_identify_call_history, $startTime)
    {
        if (env('TESTING_MODE') && env('MX_ACCOUNT_ONWER_NULL_RESPONSE')) {
            $response = ["account_owners" => [], "pagination" => ["current_page" => 1, "per_page" => 25, "total_entries" => 0, "total_pages" => 1]];
        } else {
            $response = $this->getAccountOwnerInfo($data);
        }

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Response received: " . json_encode($response));

        $responseReceivedAt = Carbon::now();

        if (!isset($response['account_owners'])) {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - No account owners found in the response.");
            return false;
        }

        if (empty($response['account_owners'])) {
            $accounts = UserBankAccountInfo::where('mx_member_guid', $data['member_guid'])->get();
            if (!empty($accounts)) {
                foreach ($accounts as $account) {
                    $this->_addAccountOwnerInfo($account, $response['account_owners'], $data);
                }
            }
        }

        foreach ($response['account_owners'] as $ownerInfo) {
            $account = UserBankAccountInfo::where('account_id', $ownerInfo['account_guid'])->first();
            if (!empty($account)) {
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Account found for account id: " . $ownerInfo['account_guid']);
                $this->_addAccountOwnerInfo($account, $ownerInfo, $data);
            } else {
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Account not found for account id: " . $ownerInfo['account_guid']);
            }
        }
        // Update the mx_identify_member_api_call_history table
        $history = MxIdentifyMemberCallHistory::find($mx_identify_call_history->id);
        $history->response_received_at = $responseReceivedAt;
        $history->completed_at = Carbon::now();
        $history->execution_time = microtime(true) - $startTime;
        $history->save();
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Account Owner API call completed successfully.");
    }

    /**
     * Adds account owner information to the database.
     *
     * @param UserBankAccountInfo $account The account object.
     * @param array $ownerInfo The owner information array.
     * @param array $data The data array containing additional information.
     * @return void
     */
    private function _addAccountOwnerInfo($account, $ownerInfo, $data)
    {
        $data['account_id'] = $account->id;
        $data['user_id'] = $account->user_id;
        $data['owner_name'] = $ownerInfo['owner_name'] ?? null;
        $data['owner_address'] = implode(', ', array_filter([
            $ownerInfo['address'] ?? null,
            $ownerInfo['city'] ?? null,
            $ownerInfo['state'] ?? null,
            $ownerInfo['postal_code'] ?? null,
            $ownerInfo['country'] ?? null,
        ]));
        $data['as_of_date'] = date('Y-m-d');
        $data['raw_response'] = $account->account_id ? $ownerInfo : null;
        addBankAccountOwnerInfo($data);
    }
    /**
     * getMemberData
     * This function will call the member data API
     * @param  mixed $params
     * @return void
     */
    public function getMemberData($params)
    {
        try {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Get Member Data API call started... ");
            $params['user_guid'] = $params->mx_consumer_id;
            $params['member_guid'] = $params->mx_member_guid;
            $response = json_decode($this->mxClient->getMemberData($params), true);
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Member data API call completed successfully.");
            return $response;
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while call Member Data API in MX.", [EXCEPTION => $e]);
        }
    }

    /**
     * This function will fetch the Member Guid for a MX Consumer
     *
     * @return mixed
     */
    public function getMemberGuid($data)
    {
        try {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Process started for fetching accounts for a consumer in MX...");
            $data['page'] = 1;
            $accounts = [];
            $data['user_guid'] = $data['mx_consumer_id'];
            while (true) {
                $apiResponse = json_decode($this->mxClient->getUserAccounts($data), true);
                $accounts = array_merge($accounts, $apiResponse['accounts']);
                if ($apiResponse['pagination']['current_page'] < $apiResponse['pagination']['total_pages']) {
                    $data['page'] = $data['page'] + 1;
                } else {
                    break; // All entries are loaded, exit the loop
                }
            }
            foreach ($accounts as $account) {
                return $account['member_guid'];
            }
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while fetching accounts for a consumer list in MX.", [EXCEPTION => $e]);
            return ['code' => FAIL, 'message' => trans('message.fetch_user_account_exception'), 'data' => null];
        }
    }

    /**
     * This function will fetch the Member Guid for a MX Consumer
     *
     * @return mixed
     */
    public function getMembers($data)
    {
        try {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Process started for fetching members for Consumer ID: " . $data['consumer_id'] . " in MX...");
            $data['page'] = 1;
            $merbers = [];
            while (true) {
                $apiResponse = json_decode($this->mxClient->getMembers($data), true);
                $merbers = array_merge($merbers, $apiResponse['members']);
                if ($apiResponse['pagination']['current_page'] < $apiResponse['pagination']['total_pages']) {
                    $data['page'] = $data['page'] + 1;
                } else {
                    break; // All entries are loaded, exit the loop
                }
            }
            if (count($merbers) > 0) {
                // Filter the array to keep only the arrays with "connection_status" set to "CONNECTED"
                $connectedArrays = array_filter($merbers, function ($item) {
                    return $item['connection_status'] === 'CONNECTED' && $item['is_being_aggregated'] == false;
                });
                // Get the last array from the filtered result
                $lastConnectedArray = end($connectedArrays);
                if (empty($lastConnectedArray)) {
                    Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Bank account update Failed due to no Connected Member return from MX for User ID:  " . $data['consumer_id']);
                    return ['code' => FAIL, 'message' => trans('message.fetch_user_members_exception'), 'data' => null];
                } else {
                    return ['code' => SUCCESS, 'message' => trans('message.connected_merber_guid_found'), 'data' => $lastConnectedArray['guid']];
                }
            } else {
                Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Bank account update Failed due to no Member return from MX for User ID:  " . $data['consumer_id']);
                return ['code' => FAIL, 'message' => trans('message.fetch_user_members_exception'), 'data' => null];
            }
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while fetching members for a consumer list in MX for Consumer ID: " . $data['consumer_id'], [EXCEPTION => $e]);
            return ['code' => FAIL, 'message' => trans('message.fetch_user_members_exception'), 'data' => null];
        }
    }

    public function getBalance($params)
    {
        try {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Process started for fetching balance for a specific account in MX...");
            $response = json_decode($this->mxClient->getBalance($params), true);
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Process completed for fetching balance for a specific account in MX.");
            return $response;
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while fetching balance for a specific account in MX.", [EXCEPTION => $e]);
            return null;
        }
    }

    /**
     * Checks the balance of a bank account and updates the account details.
     *
     * @param object $bank_details Bank account details
     * @param int $user_id User ID
     * @param string $source Source of the balance check
     * @param bool $skip_check_balance Flag to skip balance check
     * @param mixed $check_balance_data Check balance data
     * @throws \Exception If an error occurs during balance check
     * @return array Balance check result
     */
    public function checkBalance($bank_details, $user_id, $source, $skip_check_balance = false, $check_balance_data = false)
    {
        // Check if the bank is oauth bank or non oauth bank. If non oauth bank, check if the last balance check was between the configurable hour. If happened the skip the balance check.
        $bankTypeCheck = $this->_checkBankType($bank_details);
        if ($bankTypeCheck['skip_check']) {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Skipping balance check for non-oauth bank as it may cause the consumer to land in a challenged state for Consumer ID: " . $user_id);
            if ($source != ADMIN_BALANCE_FETCH && $source != ADMIN_REFRESH_BALANCE) {
                $code = MX_CHECK_BALANCE_RESTRICTION;
                $message = "Balance update is temporarily unavailable due to a banking issue. Please try refreshing your balance after some time. We apologize for the inconvenience and appreciate your understanding.";
            } else {
                $code = FAIL;
                $message = "Balance fetch is temporarily restricted for this non-OAuth bank to prevent potential issues that could place the consumer in a challenged state. Please try again after " . $bankTypeCheck['remaining_time'] . ".";
            }
            return ['code' => $code, 'message' => $message, 'data' => null];
        }

        $startTime = microtime(true);
        $bank_active = getStatus(BANK_ACTIVE);
        $params = [
            'user_guid' => $bank_details->mx_consumer_id,
            'member_guid' => $bank_details->mx_member_guid,
            'account_guid' => $bank_details->account_id,
        ];

        // Check if the account is in problematic state that requires manual intervention of the user
        if ($bank_details->mx_user_action_needed == 1 || $bank_details->mx_non_actionable_status_detected == 1) {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Account is in problematic state. Exiting the check balance process...");
            if ($source != ADMIN_BALANCE_FETCH && $source != ADMIN_REFRESH_BALANCE) {
                return ['code' => MX_CHECK_BALANCE_RESTRICTION, 'message' => trans('message.refresh_balance_call_failed_as_action_needed_for_consumer'), 'data' => null];
            }else{
                return ['code' => FAIL, 'message' => trans('message.refresh_balance_call_failed_as_action_needed'), 'data' => null];
            }
        }

        try {
            if (!$skip_check_balance) {
                if ($source != MX_ACCOUNT_FIX) { // We don't need to call the paid check balance api as MX already fetched the latest balance during connection fix
                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Calling the check balance API with the following params: ", $params);
                    $membercheckBalance = $this->mxClient->membercheckBalance($params);
                    $membercheckBalanceData = json_decode($membercheckBalance, true);
                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Decoded response from member checkBalance for source: " . $source . ": ", $membercheckBalanceData);
                } else {
                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Skipping balance check api call as it is already done during connection fix for Consumer ID: " . $user_id);
                }

                // Insert into MX check balance history table
                $check_balance_data = insertBalanceCheckRecord($bank_details, $user_id, $source);
            }

            if ($source == SCHEDULED_BALANCE_FETCH && !$skip_check_balance) {
                return null; // Return null in case of scheduled balance fetch as the rest of the part will be skipped for now and will be executed later from a separate cron job
            }
            $periodically_status_check = $source != MX_ACCOUNT_FIX ? true : false;
            $account_details_check = $source != MX_ACCOUNT_FIX ? false : true;
            $inc = 0; // Initialize iteration count

            if (!$skip_check_balance && $source != MX_ACCOUNT_FIX) {
                // Check if member data exists in the response
                if (isset($membercheckBalanceData['member'])) {
                    $memberBalanceStatus = $membercheckBalanceData['member'];

                    // Check if the member status is in the problem statuses
                    if (handleProblemStatuses($memberBalanceStatus, $user_id, $bank_details, $source)) {
                        return ['code' => FAIL, 'message' => trans('message.bank_account_marked_as_action_needed_in_mx'), 'data' => ['active_account_delinked' => 1]];
                    }

                    // Check if the member is connected and not being aggregated
                    if ($memberBalanceStatus['connection_status'] == MX_CONNECTED && $memberBalanceStatus['is_being_aggregated'] == false) {
                        $periodically_status_check = false;
                        $account_details_check = true;
                    }
                }
            }

            // If periodically status check is needed
            if ($periodically_status_check) {
                while (true) {
                    Log::info(__METHOD__ . "(" . __LINE__ . ") - Periodically member status check iteration: " . $inc);

                    if (!$skip_check_balance) {
                        sleep(env('MX_PERIODICALLY_STATUS_CHECK_WAIT_TIME')); // Wait for a specified time before the next check
                    }

                    // Get the latest member status
                    $memberStatus = $this->mxClient->getMemberStatus($params);
                    $memberStatusData = json_decode($memberStatus, true);

                    // Check if member data exists in the response
                    if (isset($memberStatusData['member'])) {
                        $memberStatus = $memberStatusData['member'];

                        // Check if the member status is in the problem statuses
                        if (handleProblemStatuses($memberStatus, $user_id, $bank_details, $source)) {
                            return ['code' => FAIL, 'message' => trans('message.bank_account_marked_as_action_needed_in_mx'), 'data' => ['active_account_delinked' => 1]]; // Return to break the loop
                        }

                        // Check if the member is connected and not being aggregated
                        if ($memberStatus['connection_status'] == MX_CONNECTED && $memberStatus['is_being_aggregated'] == false) {
                            $account_details_check = true;
                            Log::info(__METHOD__ . "(" . __LINE__ . ") - Account details fetch conditions are met for user: " . $user_id . ". on " . $inc . " iteration.");
                            break; // Exit the loop if conditions are met
                        }
                    }

                    // Break the loop if the maximum number of iterations is reached
                    if ($inc == env('MX_PERIODICALLY_STATUS_CHECK_LOOP_LIMIT') || $skip_check_balance) {
                        Log::error(__METHOD__ . "(" . __LINE__ . ") - Max iterations limit reached to fetch member status for Consumer: " . $user_id . ". Exiting...");
                        break;
                    }
                    $inc++;
                }
            }

            // Check if account details fetch conditions are met
            if ($account_details_check) {
                if ($source != SCHEDULED_BALANCE_FETCH && $source != MX_ACCOUNT_FIX && $source != MX_MEMBER_STATUS_CHECK_SCHEDULER) {
                    $getAccountDetails = $this->mxClient->getAccountDetails($params);
                    $account_data = json_decode($getAccountDetails, true);
                    $response_received_at = Carbon::now();

                    if (isset($account_data['account'])) {
                        $this->_processAccountDetails($account_data['account'], $bank_details, $user_id, $source, $check_balance_data, $response_received_at, $startTime, $bank_active, $inc);
                        return ['code' => SUCCESS, 'message' => trans('message.refresh_balance_call_success'), 'data' => null];
                    } else {
                        Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - No account information found for Consumer: " . $user_id . ".");
                        return ['code' => FAIL, 'message' => trans('message.refresh_balance_call_failed'), 'data' => null];
                    }
                } else { // Get the balance for all accounts related to the member
                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Fetching account details for all accounts related to the member: " . $params['member_guid'] . " during " . $source . ". ");
                    $getMemberAccounts = $this->mxClient->getMemberAccounts($params);
                    $member_data = json_decode($getMemberAccounts, true);
                    $response_received_at = Carbon::now();

                    if (isset($member_data['accounts']) && count($member_data['accounts']) > 0) {
                        foreach ($member_data['accounts'] as $account) {
                            $this->_processAccountDetails($account, $bank_details, $user_id, $source, $check_balance_data, $response_received_at, $startTime, $bank_active, $inc);
                        }
                        return ['code' => SUCCESS, 'message' => trans('message.refresh_balance_call_success'), 'data' => null];
                    } else {
                        Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - No member account information found for Consumer: " . $user_id . ".");
                        return ['code' => FAIL, 'message' => trans('message.refresh_balance_call_failed'), 'data' => null];
                    }
                }
            } else {
                Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Max wait time exceeded to fetch account details for Consumer: " . $user_id . ".");
                return ['code' => FAIL, 'message' => trans('message.refresh_balance_call_failed'), 'data' => null];
            }

            if (isset($membercheckBalanceData['member']['connection_status'])) {
                // Check if the member status is in the problem statuses
                handleProblemStatuses($membercheckBalanceData['member'], $user_id, $active_bank, $source);
            } else {
                Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Member status check skipped as the response from membercheckBalance does not contain connection_status: ", $membercheckBalanceData);
            }

            return $membercheckBalanceData;
        } catch (\Exception $e) {
            // Log any exception that occurs
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Exception while fetching balance for member account in MX: ", [EXCEPTION => $e]);
            return ['code' => FAIL, 'message' => trans('message.refresh_balance_call_failed'), 'data' => null];
        }
    }

    /**
     * Process account details and update related tables
     *
     * @param array $account_details
     * @param object $bank_details
     * @param string $user_id
     * @param string $source
     * @param object $check_balance_data
     * @param \Carbon\Carbon $response_received_at
     * @param float $startTime
     * @param string $bank_active
     * @param int $inc
     * @return void
     */
    private function _processAccountDetails($account_details, $bank_details, $user_id, $source, $check_balance_data, $response_received_at, $startTime, $bank_active, $inc)
    {
        // Calculate highest transaction bonus for the user
        try {
            $highestTransactionBonus = calculateHighestTransactionBonus($user_id);
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Highest transaction bonus calculated: " . $highestTransactionBonus . " for consumer ID: " . $user_id);
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Error calculating highest transaction bonus for consumer ID: " . $user_id . ". Error: " . $e->getMessage());
            $highestTransactionBonus = null;
        }
        // Check if the account exists in the database for this consumer
        $checkAccountExists = UserBankAccountInfo::where(['user_id' => $user_id, 'account_id' => $account_details['guid']])->first();
        if (!empty($checkAccountExists)) {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Account information found for Consumer: " . $user_id . " and Account: " . $account_details['guid'] . ".");
            if (env('TESTING_MODE')) {
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "=====================TESTING MODE ENABLED=======================");
                $balance = env('CUSTOM_BANK_BALANCE');
                $account_details['available_balance'] = env('CUSTOM_BANK_BALANCE');
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Using custom balance :" . $balance);
            } else {
                $balance = $account_details['available_balance'] ?? $account_details['balance'];
                if (isset($account_details['available_balance']) && $account_details['available_balance'] > $account_details['balance']) {
                    $balance = $account_details['available_balance'];
                }
            }

            $checkParellelAlgoEnabled = getSettingsValue('prallel_pp_algo', 0); // Check whether the parallel purchase power algo is enabled or disabled
            $checkNewAlgoEnabled = getSettingsValue('enable_new_pp_algo', 0); // Check whether the new purchase power algo is enabled or disabled
            $user_details = User::where('user_id', $user_id)->first();

            // Prepare data for purchase power calculation
            $purchasePowerCalculationData = [
                'account_id' => $checkAccountExists->id,
                'account_no' => $checkAccountExists->account_no,
                'balance' => $balance,
                'updated_at' => $checkAccountExists->updated_at,
                'parellel_pp_enabled' => $checkParellelAlgoEnabled,
                'enable_new_pp_algo' => $checkNewAlgoEnabled,
                'consumer_registration_date' => $user_details->existing_user == 0 ? $user_details->created_at : $user_details->migrated_at,
                'total_account_count' => 1,
                'current_account' => 1,
                'user_details' => $user_details,
                'highest_transaction_bonus' => $highestTransactionBonus,
            ];

            $purchase_power_data = $this->purchasePower->calculatePurchasePower($purchasePowerCalculationData);
            $purchase_power = $purchase_power_data['purchase_power'];

            // Prepare data for consumer_account_balances table
            $data = [
                'user_id' => $user_id,
                'account_id' => $checkAccountExists->id,
                'balance' => $balance,
                'response_raw_balance' => $account_details['balance'],
                'response_available_balance' => $account_details['available_balance'],
                'purchase_power' => $purchase_power,
                'purchase_power_source' => $user_details->purchase_power_source,
                'source' => $source,
                'one_time_refresh' => $source == ONE_TIME_BALANCE_FETCH ? 1 : 0,
                'banking_solution_response' => json_encode($account_details),
            ];

            addCustomerAccounts($data); // Save the data in consumer_account_balances table

            $refresh_balance_called = $source == CONSUMER_LOGIN ? 1 : 0;

            // Save the data in mx_check_balance_call_history table
            $check_balance_data->member_status_check_count = $inc + 1; // Added 1 as it starts from 0
            $check_balance_data->response_received_at = $response_received_at;
            $check_balance_data->completed_at = Carbon::now();
            $check_balance_data->balance = $balance;
            $check_balance_data->purchase_power = $purchase_power;
            $check_balance_data->execution_time = microtime(true) - $startTime;
            $check_balance_data->save();

            // Update purchase power in user table if the current account is active
            if ($bank_active == $checkAccountExists->status) {
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Calculated purchase power for User ID: " . $user_id . " is : $" . $purchase_power);
                User::where(['user_id' => $user_id, 'disable_automatic_purchase_power' => 0])->update(['purchase_power' => $purchase_power, 'refresh_balance_called' => $refresh_balance_called]);
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Consumer purchase power updated into database successfully.");
            }
        } else {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - No bank account found for user ID: " . $user_id . " with Account ID:" . $account_details['guid']);
        }
    }

    /**
     * Checks if the given bank type supports OAuth, and if it does then
     * return an array with 'skip_check' as false and no remaining time.
     * If the bank does not support OAuth, then check if the last balance fetch
     * was within the configurable time interval. If it was then return an array
     * with 'skip_check' as true and the remaining time, otherwise return false.
     *
     * @param $bank_details
     * @return array
     */
    private function _checkBankType($bank_details)
    {
        $checkBankType = MxBank::where('code', $bank_details->institution_id)->first();

        // If the bank supports OAuth, return an array with 'skip_check' as false and no remaining time
        if (!empty($checkBankType) && $checkBankType->supports_oauth == 1) {
            return ['skip_check' => false, 'remaining_time' => 0]; // Return false as the bank supports OAuth
        }

        // Check if the last balance fetch was within the configurable time interval as the bank is non-oauth
        $timeInterval = Carbon::now()->subMinutes(env('TIME_INTERVAL_FOR_NON_OAUTH_BANK_BALANCE_FETCH'));

        $checkLastBalanceFetch = MxCheckBalanceCallHistory::where('consumer_id', $bank_details->user_id)
            ->where('mx_member_id', $bank_details->mx_member_guid)
            ->where('created_at', '>=', $timeInterval)
            ->whereNotNull('completed_at')
            ->orderBy('id', 'desc')
            ->first();

        if (!empty($checkLastBalanceFetch)) {
            $lastFetchTime = $checkLastBalanceFetch->created_at;
            $nextAllowedFetchTime = $lastFetchTime->addMinutes(env('TIME_INTERVAL_FOR_NON_OAUTH_BANK_BALANCE_FETCH'));

            if ($nextAllowedFetchTime->isFuture()) {
                $remainingTime = $nextAllowedFetchTime->diffForHumans(); // Get the remaining time in human readable format
                return ['skip_check' => true, 'remaining_time' => $remainingTime]; // Return the remaining time
            }
            return ['skip_check' => true, 'remaining_time' => 0]; // Return false if the last balance check was between the configurable hour.
        } else {
            return ['skip_check' => false, 'remaining_time' => 0]; // Return false if the last balance check was not between the configurable hour.
        }
    }
}
