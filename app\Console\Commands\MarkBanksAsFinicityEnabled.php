<?php
namespace App\Console\Commands;

use App\Models\BankingSolutionMaster;
use App\Models\FinancialInstitutionMaster;
use App\Models\FinancialInstitutionRoutingNumber;
use App\Models\UserBankAccountInfo;
use Illuminate\Console\Command;

class MarkBanksAsFinicityEnabled extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'markbanks:finicityenabled';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will mark the banks in fed_routing_no_masters table based on the routing numbers';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $banking_solution_id = BankingSolutionMaster::where('banking_solution_name', FINICITY)->first();
        $all_routing_numbers = UserBankAccountInfo::whereNotNull('finicity_id')->where('banking_solution_id', $banking_solution_id->id)->distinct()->pluck('routing_no');
        // Update the banks as finicity enabled based on the routing number
        foreach ($all_routing_numbers as $routing_number) {
            $institution_routing = FinancialInstitutionRoutingNumber::where('routing_no', $routing_number)->orderBy('created_at', 'desc')->first();
            if ($institution_routing) {
                FinancialInstitutionMaster::where('id', $$institution_routing->financial_institution_id)->update(['is_finicity' => 1]);
            }
        }

    }
}
