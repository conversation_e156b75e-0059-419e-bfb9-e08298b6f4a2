<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FixEmailDomain extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:email-domains';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix email addresses ending with .con and .comm to .com';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->info('--- Starting Email Domain Correction ---');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . 'Email domain correction job started.');

        try {
            // Fetch users with incorrect email endings
            $users = DB::table('users')
                ->join('user_roles', 'users.role_id', '=', 'user_roles.role_id')
                ->where('user_roles.role_name', CONSUMER)
                ->where(function ($query) {
                    $query->where('email', 'like', '%.con')
                        ->orWhere('email', 'like', '%.comm');
                })
                ->get();

            if ($users->isEmpty()) {
                $this->warn('No emails found ending with .con or .comm.');
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . 'No emails found to correct.');
                return;
            }

            $correctedCount = 0;
            $failedCount = 0;

            foreach ($users as $user) {
                try {
                    $correctedEmail = str_replace(['.con', '.comm'], '.com', $user->email);

                    DB::table('users')
                        ->where('user_id', $user->user_id)
                        ->update(['email' => $correctedEmail]);

                    $this->info("Updated: {$user->email} -> {$correctedEmail}");
                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Email updated successfully", [
                        'user_id'   => $user->user_id,
                        'old_email' => $user->email,
                        'new_email' => $correctedEmail,
                    ]);

                    $correctedCount++;
                } catch (\Exception $e) {
                    $this->error("Failed to update email for user ID {$user->user_id}: " . $e->getMessage());
                    Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Failed to update email", [
                        'user_id'   => $user->user_id,
                        'email'     => $user->email,
                        'error'     => $e->getMessage(),
                        'trace'     => $e->getTraceAsString(),
                    ]);
                    $failedCount++;
                }
            }

            $this->info('--- Email Domain Correction Completed ---');
            $this->info("Total emails corrected: {$correctedCount}");
            $this->info("Total emails failed: {$failedCount}");
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Total emails corrected: {$correctedCount}, failed: {$failedCount}");
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . 'Email domain correction job completed.');
        } catch (\Exception $e) {
            $this->error('Error occurred during email correction process: ' . $e->getMessage());
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . 'Error in email domain correction job', [
                'error_message' => $e->getMessage(),
                'trace'         => $e->getTraceAsString(),
            ]);
        }
    }
}
