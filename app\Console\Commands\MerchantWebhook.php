<?php
namespace App\Console\Commands;

use App\Http\Factories\Webhook\WebhookFactory;
use App\Models\StatusMaster;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class MerchantWebhook extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'merchant:webhook';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command call a webhook for all ecommerce integrators.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->merchantWebhook = new WebhookFactory();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        
        //get Awaiting Consumer Approval status details
        $awaiting_consumer_approval_status = StatusMaster::where('code', AWAITINGCONSUMERAPPROVAL)->first();
        //get auto approved status details
        $declined_status = StatusMaster::where('code', DECLINED)->first();
        //get consumer approved status details
        $approved_by_consumer_status = StatusMaster::where('code', APPROVED_BY_CONSUMER)->first();

        $column_name = ENV('API_ENVIRONMENT') . '_webhook_url';
        // Fetch all transactions that are not passed through webhook
        $sql = "SELECT transaction_id, api_url, tot_amount, tip_amount, transaction_number, intent_id, passthrough_param, status
        FROM (SELECT td.id transaction_id, IFNULL(sum(if(twd.transaction_id IS NOT NULL,1,0)),0) total_cnt, IFNULL(sum(if(twd.webhook_status=1,1,0)),0) statused_cnt, rmm." . $column_name . " api_url, td.amount tot_amount, td.tip_amount tip_amount, td.intent_id intent_id, td.transaction_number transaction_number, emi.passthrough_param passthrough_param, (CASE WHEN (sm.code = " . SUCCESS . " || sm.code = " . PENDING . " ||sm.code = " . RETURNED . ") THEN 'Success' 
        WHEN (sm.code = " . VOIDED . ") THEN 
            (CASE WHEN ((parent_transaction.change_request = 1 AND parent_transaction.consumer_approval_for_change_request = '".$awaiting_consumer_approval_status->id."') || (td.change_request = 1 AND td.consumer_approval_for_change_request = '".$awaiting_consumer_approval_status->id."'))THEN '".$awaiting_consumer_approval_status->status."' 
                WHEN ((parent_transaction.change_request = 1 AND parent_transaction.consumer_approval_for_change_request = '".$declined_status->id."') || (td.change_request = 1 AND td.consumer_approval_for_change_request = '".$declined_status->id."')) THEN '".$declined_status->status."' 
                WHEN ((parent_transaction.change_request = 1 AND parent_transaction.consumer_approval_for_change_request = '".$approved_by_consumer_status->id."') || (td.change_request = 1 AND td.consumer_approval_for_change_request = '".$approved_by_consumer_status->id."')) THEN '".$approved_by_consumer_status->status."' 
            ELSE 'Voided' END ) 
        ELSE 'Failed' END ) as status
        FROM transaction_details td
        LEFT JOIN transaction_details parent_transaction ON td.change_request_transaction_ref_no = parent_transaction.id
        INNER JOIN terminal_master tm ON td.terminal_id = tm.id
        INNER JOIN merchant_stores ms ON tm.merchant_store_id = ms.id
        INNER JOIN registered_merchant_master rmm ON ms.merchant_id = rmm.id
        INNER JOIN ecommerce_merchant_intents emi ON td.intent_id = emi.intent_id
        INNER JOIN status_master sm ON td.status_id = sm.id
        LEFT JOIN transaction_webhook_details twd ON td.id = twd.transaction_id
        WHERE td.transaction_ref_no IS NULL AND td.is_ecommerce = 1 AND td.intent_id IS NOT NULL AND td.webhook_called = 0 AND ( td.updated_amount IS NULL OR (td.updated_amount IS NOT NULL AND td.change_request_transaction_ref_no IS NULL))
        AND rmm." . $column_name . " IS NOT NULL 
        group by td.id
        ) in_tbl WHERE (in_tbl.total_cnt = 0 or (total_cnt < 3 and statused_cnt <= 0));";

        $allTransactions = DB::connection(MYSQL_RO)->select($sql);
        if (!empty($allTransactions)) {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Webhook call process started for " . count($allTransactions) . " transactions...");
            foreach ($allTransactions as $transaction) {   
                $this->merchantWebhook->sendToWebHook($transaction);
            }
        } else {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No transactions found for webhook call.");
        }
    }

}
