<?php

namespace App\Console\Commands;

use App\Http\Factories\IdValidatorPlaid\IdValidatorPlaidFactory;
use App\Models\CognitoAssessmentZipExceptionHistory;
use App\Models\StatusMaster;
use App\Models\User;
use App\Models\ValidationLog;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class ScheduleMonthlyCognitoIdentitySearch extends Command
{
    /**
     * The name and signature of the console command.
     * command will be executed every month of 30th day or end of the month
     *
     * @var string
     */
    protected $signature = 'schedule:monthlycognitoidentitysearch  {--start_limit=} {--end_limit=} {--custom_limit=}';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will schedule existing users for plaid identity search with assesment';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->idvalidator = new IdValidatorPlaidFactory();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Log::channel('schedule-plaid-identity-search-monthly')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Schedule monthly identity searches of existing users started...");
        // plaid monthly limit
        $cognitoMonthlyLimit = ENV('COGNITO_MONTHLY_THRESHOLD');
        // plaid daily limit
        $cognitoDailyLimit = ENV('COGNITO_DAILY_THRESHOLD');
        // plaid limit for identity searches of maximum user when scheduler runs
        $maxCognitoLimit = ENV('MAX_COGNITO_THRESHOLD');
        Log::channel('schedule-plaid-identity-search-monthly')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Cognito monthly limit: " . $cognitoMonthlyLimit . ", daily limit: " . $cognitoDailyLimit . " and maximum limit: " . $maxCognitoLimit);
        // count plaid identity search api call in current month
        $first_day_of_the_current_month = Carbon::today()->startOfMonth();
        $last_day_of_the_current_month = $first_day_of_the_current_month->copy()->endOfMonth();
        Log::channel('schedule-plaid-identity-search-monthly')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Current month first and last date: " . $first_day_of_the_current_month . " and " . $last_day_of_the_current_month);
        // count current month plaid limit used
        $count_cognito_identity_search_limit_used = ValidationLog::where('type', 'plaid')->where('api', '/idv_classic/identity_searches')->whereBetween('created_at', [$first_day_of_the_current_month, $last_day_of_the_current_month])->count();
        Log::channel('schedule-plaid-identity-search-monthly')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Cognito limit used in current month is: " . $count_cognito_identity_search_limit_used);
        // get total days in current month
        $currentDate = Carbon::now();
        $totalDaysOfCurrentMonth = Carbon::parse($currentDate->format('Y-m-d'))->daysInMonth;
        Log::channel('schedule-plaid-identity-search-monthly')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Current month total days:" . $totalDaysOfCurrentMonth);
        // allocate limit for rest of days in current month
        $allocateCognitoLimit = $totalDaysOfCurrentMonth == 31 ? $cognitoDailyLimit * 2 : $cognitoDailyLimit;
        Log::channel('schedule-plaid-identity-search-monthly')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Cognito limit buffer for remaining days of current month is: " . $allocateCognitoLimit);
        $totalCognitoLimitOfCurrentMonth = $count_cognito_identity_search_limit_used + $allocateCognitoLimit;
        Log::channel('schedule-plaid-identity-search-monthly')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Total plaid limit reserved for the current month: " . $totalCognitoLimitOfCurrentMonth);
        // check if plaid monthly limit is greater than total plaid limit of current month
        if ($cognitoMonthlyLimit > $totalCognitoLimitOfCurrentMonth) {
            // current month remaining plaid limit
            $remainingCognitoLimitOfCurrentMonth = $cognitoMonthlyLimit - $totalCognitoLimitOfCurrentMonth;
            Log::channel('schedule-plaid-identity-search-monthly')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Current month remaining plaid limit: " . $remainingCognitoLimitOfCurrentMonth);
            if ($remainingCognitoLimitOfCurrentMonth > $maxCognitoLimit) {
                $remainingCognitoLimitOfCurrentMonth = $maxCognitoLimit;
            }
            Log::channel('schedule-plaid-identity-search-monthly')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Final remaining plaid limit: " . $remainingCognitoLimitOfCurrentMonth);
            // get limited users who has not done plaid identity search yet
            if ($this->option('end_limit')) {
                Log::channel('schedule-plaid-identity-search-monthly')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Manual limit given. skip:" . $this->option('start_limit') . " .Take: " . $this->option('end_limit'));
                $sql = "SELECT validation_log_table.id, users.* FROM users LEFT JOIN validation_log_table ON validation_log_table.phone = users.phone AND validation_log_table.api = '/idv_classic/identity_searches' AND validation_log_table.type = 'plaid' LEFT JOIN status_master ON users.status = status_master.id WHERE status_master.code IN ('" . USER_ACTIVE . "', '" . USER_ACTIVE2 . "') AND users.password IS NOT NULL AND users.existing_user = 1 AND validation_log_table.id IS NULL ORDER BY users.migrated_at LIMIT ?, ? ";
                $searchArray = [$this->option('start_limit'), $this->option('end_limit')];
                $users = DB::select($sql, $searchArray);
            } else {
                // get minimum users
                $sql = "SELECT validation_log_table.id, users.* FROM users LEFT JOIN validation_log_table ON validation_log_table.phone = users.phone AND validation_log_table.api = '/idv_classic/identity_searches' AND validation_log_table.type = 'cognito' LEFT JOIN status_master ON users.status = status_master.id WHERE status_master.code IN ('" . USER_ACTIVE . "', '" . USER_ACTIVE2 . "') AND users.password IS NOT NULL AND users.existing_user = 1 AND validation_log_table.id IS NULL ORDER BY users.migrated_at LIMIT ?";
                if ($this->option('custom_limit')) {
                    $remainingCognitoLimitOfCurrentMonth = $this->option('custom_limit');
                    Log::channel('schedule-plaid-identity-search-monthly')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Custom cognito limit: " . $remainingCognitoLimitOfCurrentMonth);
                }
                $searchArray = [$remainingCognitoLimitOfCurrentMonth];
                $users = DB::select($sql, $searchArray);
            }
            Log::channel('schedule-plaid-identity-search-monthly')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Identity search processing for total number of user: " . count($users));
            // count skipped user
            $count_skipped_user = 0;
            // plaid search identity
            foreach ($users as $key => $user) {
                $params['street'] = $user->street_address;
                $params['subdivision'] = $user->state;
                $params['postal_code'] = $user->zipcode;
                $params['phoneNo'] = $user->phone;
                $params['firstName'] = $user->first_name;
                $params['middleName'] = $user->middle_name;
                $params['lastName'] = $user->last_name;
                $params['email'] = $user->email;
                $params['dateOfBirth'] = $user->date_of_birth;
                $params['address'] = $user->street_address;
                $params['city'] = $user->city;
                $params['state'] = $user->state;
                $params['zip'] = $user->zipcode;
                $params['user_id'] = $user->user_id;
                $params['session_id'] = $user->user_id;
                $params['source'] = V1_MONTHLY_SCHEDULER;
                // check if zip code matches
                if (preg_match(VALIDATION_US_ZIPCODE, $user->zipcode)) {
                    try {
                        //call to plaid id validation
                        $response = $this->idvalidator->validateIdentityofExistingUser($params);
                        Log::channel('schedule-plaid-identity-search-monthly')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Identity search processed for user Id: " . $user->user_id);
                        $params['response'] = $response;
                        // get the assessment done for the response returned after creating identity search
                        $res = $this->_getAssessment($params);
                        Log::channel('schedule-plaid-identity-search-monthly')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Assessment processed for user Id: " . $user->user_id);
                    } catch (\Exception $e) {
                        ++$count_skipped_user;
                        Log::channel('schedule-plaid-identity-search-monthly')->error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured during plaid identity search for user Id: " . $user->user_id, [EXCEPTION => $e]);
                        continue;
                    }
                } else {
                    ++$count_skipped_user;
                    //store zip exception history
                    CognitoAssessmentZipExceptionHistory::create([
                        'user_id' => $user->user_id,
                        'zip' => $user->zipcode,
                        'source' => 'schedule plaid search identity for v1 users',
                    ]);
                    Log::channel('schedule-plaid-identity-search-monthly')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Invalid zipcode: " . $user->zipcode . ". Identity search process skipped for user Id: " . $user->user_id . " and stored in database.");
                }
            }
            // total count of processed users
            $count_processed_user = count($users) - $count_skipped_user;
            Log::channel('schedule-plaid-identity-search-monthly')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Total number of skipped users: " . $count_skipped_user);
            Log::channel('schedule-plaid-identity-search-monthly')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Cognito identity search completed for total number of users: " . $count_processed_user);
        }
    }

    /**
     * Getting identity assessment done for the last Identity search without ssn
     *
     * @return mixed
     */
    private function _getAssessment($params)
    {
        $params['response'] = json_decode($params['response'], true);
        //handle error if any
        if (isset($params['response']['errors'])) {
            //format the error from plaid
            $message = formatCognitoResponse($params['response']['errors'][0]['detail']);
            return renderResponse(FAIL, $message, null);
        }
        //picking out the search id
        $params['search_id'] = $params['response']['data']['id'];
        $params['type'] = (!isset($params['ssn'])) ? ASSESSMENT : ASSESSMENT_WITH_SSN;
        $response = $this->idvalidator->getIdentityAssessmentforExistingUser($params);
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . 'Identity search with id validation successfully done for user id:' . $params['user_id']);
    }
}
