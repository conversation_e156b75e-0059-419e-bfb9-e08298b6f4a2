<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CrewTipHistory extends Model
{
    /**
     * Constructor: Automatically assigns a UUID to the model's primary key.
     *
     * @param array $attributes
     */
    public function __construct(array $attributes = [])
    {
        $this->id = generateUUID();

        parent::__construct($attributes);
    }

    /**
     * Table name associated with the model.
     *
     * @var string
     */
    protected $table = 'crew_tip_history';

    /**
     * Primary key type is string, not auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'id',
        'petition_id',
        'reward_id',
        'tip_from',
        'tip_to',
        'tip_reward_amount',
        'tip_reward_point',
        'is_deleted',
        'created_at',
        'updated_at',
    ];

}
