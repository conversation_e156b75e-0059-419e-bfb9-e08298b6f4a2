<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("ALTER TABLE `petitions`
            ADD COLUMN IF NOT EXISTS `rejection_reason` VARCHAR(100) NULL DEFAULT NULL AFTER `status_id`,
            ADD COLUMN IF NOT EXISTS `rejection_comments` TEXT NULL DEFAULT NULL AFTER `rejection_reason`
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {

    }
};
