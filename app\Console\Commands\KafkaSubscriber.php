<?php

namespace App\Console\Commands;

use App\Http\Factories\EmailExecutor\EmailExecutorFactory;
use App\Models\CepAlarm;
use App\Models\UserBankAccountOwnerInfoMatchDetail;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class KafkaSubscriber extends Command
{
    protected $signature = 'kafka:subscriber';
    protected $description = 'This command will continuously listen to a specific Kafka topic to subscribe to new data.';
    protected $emailexecutor;

    public function __construct(EmailExecutorFactory $emailexecutor)
    {
        parent::__construct();
        $this->emailexecutor = $emailexecutor;
    }

    public function handle()
    {
        Log::channel("kafka-listener")->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Kafka subscriber started...");
        $this->info("Kafka subscriber started...");

        // // Kafka configuration setup
        // $conf = $this->configureKafka();

        // // Kafka consumer initialization
        // $consumer = new \RdKafka\KafkaConsumer($conf);
        // $consumer->subscribe([ENV('KAFKA_TOPIC_FOR_ALARM_NOTIFICATION')]);

        // // Kafka message processing loop
        // while (true) {
        //     $message = $consumer->consume(30 * 1000);
        //     switch ($message->err) {
        //         case RD_KAFKA_RESP_ERR_NO_ERROR:
        //             $this->processPayload($message->payload);
        //             break;
        //         case RD_KAFKA_RESP_ERR__PARTITION_EOF:
        //             Log::channel("kafka-listener")->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No new messages. Continue waiting for more messages...");
        //             $this->info("No new messages. Continue waiting for more messages...");
        //             break;
        //         case RD_KAFKA_RESP_ERR__TIMED_OUT:
        //             Log::channel("kafka-listener")->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Kafka subscriber timed out.");
        //             $this->info("Kafka subscriber timed out.");
        //             break;
        //         default:
        //             throw new \Exception($message->errstr(), $message->err);
        //     }
        // }
    }

    private function configureKafka()
    {
        // Kafka configuration setup
        // $conf = new \RdKafka\Conf();
        // $conf->set('bootstrap.servers', ENV('KAFKA_BOOTSTRAP_SERVER'));
        // $conf->set('security.protocol', ENV('KAFKA_SECURITY_PROTOCOL'));
        // $conf->set('sasl.mechanism', 'PLAIN');
        // $conf->set('sasl.username', ENV('KAFKA_USERNAME'));
        // $conf->set('sasl.password', ENV('KAFKA_PASSWORD'));
        // $conf->set('group.id', 'group');
        // $conf->set('auto.offset.reset', 'earliest');

        // $topicConf = new \RdKafka\TopicConf();
        // $conf->setDefaultTopicConf($topicConf);

        // return $conf;
    }

    private function processPayload($payload)
    {
        $payload = json_decode($payload);

        if (!isset($payload->code) || !isset($payload->name)) {
            Log::channel("kafka-listener")->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Payload mandatory keys not found.");
            $this->info("Payload mandatory keys not found.");
            return;
        }

        if (isset($payload->send_alarm) && $payload->send_alarm == 1) {
            $this->processAlarm($payload);
        }

        $this->insertAccountOwnerMatchInfoData($payload);
    }

    private function processAlarm($payload)
    {
        Log::channel("kafka-listener")->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Alert received. Inserting into table and Sending mail...");
        $this->info("Alert received. Inserting into table and Sending mail...");

        // Insert the data in the cep_alarms table
        $cep_alarms = new CepAlarm();
        $cep_alarms->alarm_id = $payload->alarmID;
        $cep_alarms->name = $payload->name;
        $cep_alarms->code = $payload->code;
        $cep_alarms->description = $payload->desc;
        $cep_alarms->evidences = json_encode($payload->evidences);
        $cep_alarms->metaInfo = $payload->metaInfo;
        $cep_alarms->window_start_time = $payload->windowStartTime;
        $cep_alarms->window_end_time = $payload->windowEndTime;
        $cep_alarms->save();

        // Send mail
        $email_params = [
            'kafka_subject' => $payload->code . ' - ' . $payload->name,
            'alarm_name' => ucwords(str_replace('-', ' ', $payload->name)),
            'evidence' => property_exists($payload, 'evidences') ? json_encode($payload->evidences) : "",
        ];
        $this->emailexecutor->emailForAlarmNotification($email_params);
    }

    private function insertAccountOwnerMatchInfoData($payload)
    {
        if ($payload->code == "ACCOUNT-INFO-MATCH-00001") {
            if ($this->isValidEvidences($payload->evidences)) {
                Log::channel("kafka-listener")->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Data received for Account owner info match. Inserting data in database...");
                $this->saveAccountOwnerMatchInfoData($payload->evidences);
            } else {
                Log::channel("kafka-listener")->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Evidences are not properly configured. Exiting...");
            }
        }
    }

    private function isValidEvidences($evidences)
    {
        return isset($evidences->id)
            && isset($evidences->name_match_percentage)
            && isset($evidences->address_match_percentage);
    }

    private function saveAccountOwnerMatchInfoData($evidences)
    {
        $user_bank_account_owner_info_match_details = new UserBankAccountOwnerInfoMatchDetail();
        $user_bank_account_owner_info_match_details->user_bank_account_owner_info_id = $evidences->id;
        $user_bank_account_owner_info_match_details->name_match_percentage = $evidences->name_match_percentage;
        $user_bank_account_owner_info_match_details->address_match_percentage = $evidences->address_match_percentage;
        $user_bank_account_owner_info_match_details->save();
    }
}
