<?php

use App\Models\StatusMaster;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Log;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update the status for code '217' from 'Provisioned' to 'Paused - Store In Discussion with CanPay'
        $statusRecord = StatusMaster::where('code', '217')->first();

        if ($statusRecord) {
            $statusRecord->update(['status' => 'Paused - Store In Discussion with CanPay']);
            Log::info('Status updated successfully for code 217: Provisioned -> Paused - Store In Discussion with CanPay');
        } else {
            Log::warning('Status record with code 217 not found during migration up()');
        }
    }
};
