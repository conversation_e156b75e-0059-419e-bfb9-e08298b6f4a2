<?php
namespace App\Console\Commands;

use App\Http\Clients\Acheck21HttpClient;
use App\Http\Factories\EmailExecutor\EmailExecutorFactory;
use App\Models\Acheck21DocumentIdHistory;
use App\Models\BankAccountInfo;
use App\Models\BankDetailsForProbableReturn;
use App\Models\ReturnReasonMaster;
use App\Models\TimezoneMaster;
use App\Models\TransactionDetails;
use App\Models\UnknownReturnReason;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ScheduleCheckReturns extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'schedule:checkreturns';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command makes call to ACHECK21 to fetch the returned transactions.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->client = new Acheck21HttpClient();
        $this->emailexecutor = new EmailExecutorFactory();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->checkConsumerTransactionReturns();
    }
    /**
     * This function fetches all the return transactions and updates the database
     */
    private function checkConsumerTransactionReturns()
    {
        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Fetching return transactions from ACHECK21...");
        $this->info("Fetching return transactions...");
        try {
            // setting up the parameters to make a call to ACHECK21 for returns
            $params['start_date'] = Carbon::now()->subDays(env('RETURN_TRANSACTION_DAY_RANGE'));
            $params['end_date'] = Carbon::now();

            if(env('ACHECK_POSTING')){
                $response = $this->client->getReturns($params);
                $response_decoded = json_decode($response, true);
                Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is enabled. Posting in ACHECK21.");
            }else{
                $response_decoded['documentId'] = rand(10000000,99999999);
                $response_decoded['recordsTotal'] = rand(10000000,99999999);
                $response_decoded['returns'] = [];
                Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is disabled. Continue with a random number.");
            }
            
            // check if acheck21 returned total record count as not 0
            if ($response_decoded['recordsTotal'] != 0) {
                Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . $response_decoded['recordsTotal'] . " return transactions found from ACHECK21. Updating database..");
                $this->info($response_decoded['recordsTotal'] . " return transactions found from ACHECK21. Updating database..");
                $returned = getStatus(RETURNED);
                $records = 0;
                foreach ($response_decoded['returns'] as $returns) {
                    try {
                        // get the return reason using reason code
                        Log::channel('return-transaction')->info("Return Code: " . $returns['returnCode'] . " with Doc ID: " . $returns['documentId']);
                        $return_reason = ReturnReasonMaster::where('reason_code', $returns['returnCode'])->first();
                        if (empty($return_reason)) {
                            $return_reason = ReturnReasonMaster::where('reason_code', 'D100')->first();
                            Log::channel('return-transaction')->info(__METHOD__ . "(" . __LINE__ . ") - Unknown return Code: " . $returns['returnCode'] . " received for Document ID: " . $returns['documentId'] . ". Hence continuing with the deafult D100 return code.");
                        }
                        //get the record with the document id 
                        $transaction_sql = "select * from
                        transaction_details td force index(idx_acheck_doc_id)
                        straight_join transaction_details td1 on td1.id = td.transaction_ref_no
                        where td.acheck_document_id = '" . $returns['documentId'] . "'
                        limit 1;";
                        $transaction = DB::select($transaction_sql);
                        $return_amount = number_format((float) $returns['amount'], 2, '.', '');
                        if (!empty($transaction)) {
                            //Fetch the Latest Document ID for the Transaction
                            $acheckDocumentId = Acheck21DocumentIdHistory::where(['transaction_ref_no' => $transaction[0]->id, 'ignore_flag' => 0])->first();
                            if (!empty($acheckDocumentId)) {
                                $return_date = date('Y-m-d', strtotime($returns['returnedOn']));
                                if ($transaction[0]->returned_on != '') {
                                    $dateDiff = Carbon::parse($return_date)->diffInDays(Carbon::parse($transaction[0]->returned_on)->toDateString());
                                } else {
                                    $dateDiff = 1;
                                }
                                // check if return details already updated in the database
                                if ($dateDiff > 0 && ($transaction[0]->consumer_bank_posting_amount) == $return_amount) {
                                    Log::channel('return-transaction')->info(__METHOD__ . "(" . __LINE__ . ") - Return recieved for transaction details: " . json_encode($transaction));
                                    // update the status of the parent row as "Returned"
                                    // updating the approve flag to one to indicate that transaction does not need consumer approval
                                    $approval_to_represent = $return_reason->canpay_represent == 1 && $transaction[0]->represent_count < 2 ? 1 : 0;
                                    TransactionDetails::where('id', $transaction[0]->id)->update(array('is_represented' => 0, 'status_id' => $returned, 'return_reason' => $return_reason->id, 'returned_on' => Carbon::parse($returns['returnedOn']), 'approved_to_represent' => $approval_to_represent, 'consumer_approval' => $approval_to_represent, 'represent_block' => 0, 'approved_by_admin' => 0, 'transaction_returned' => $transaction[0]->account_id));

                                    //create a new row with the returned status
                                    //create a new transaction
                                    $transaction_details = new TransactionDetails();
                                    $transaction_details->transaction_number = $transaction[0]->transaction_number;
                                    $transaction_details->transaction_ref_no = $transaction[0]->id;
                                    $transaction_details->user_id = $transaction[0]->user_id;
                                    $transaction_details->consumer_id = $transaction[0]->consumer_id;
                                    $transaction_details->terminal_id = $transaction[0]->terminal_id;
                                    $transaction_details->transaction_time = Carbon::now();
                                    //get timezone name
                                    $timezone = TimezoneMaster::find($transaction[0]->timezone_id);
                                    $transaction_details->local_transaction_time = Carbon::now($timezone->timezone_name);
                                    $transaction_details->local_transaction_year = date("Y", strtotime($transaction_details->local_transaction_time));
                                    $transaction_details->local_transaction_month = date("m", strtotime($transaction_details->local_transaction_time));
                                    $transaction_details->local_transaction_date = date("Y-m-d", strtotime($transaction_details->local_transaction_time));
                                    $transaction_details->timezone_id = $transaction[0]->timezone_id;
                                    $transaction_details->consumer_bank_posting_amount = $transaction[0]->consumer_bank_posting_amount;
                                    $transaction_details->reward_amount_used = $transaction[0]->reward_amount_used;
                                    $transaction_details->reward_point_used = $transaction[0]->reward_point_used;
                                    $transaction_details->amount = $transaction[0]->amount;
                                    $transaction_details->tip_amount = $transaction[0]->tip_amount;
                                    $transaction_details->tip_type = $transaction[0]->tip_type;
                                    $transaction_details->tip_add_time = $transaction[0]->tip_add_time;
                                    $transaction_details->used_qr_id = $transaction[0]->used_qr_id;
                                    $transaction_details->status_id = $returned;
                                    $transaction_details->return_reason = $return_reason->id;
                                    $transaction_details->returned_on = Carbon::parse($returns['returnedOn']);
                                    $transaction_details->transaction_type_id = $transaction[0]->transaction_type_id;
                                    $transaction_details->transaction_place = ACHECK21;
                                    $transaction_details->acheck_document_id = $returns['documentId'];
                                    $transaction_details->save();

                                    // Save the Transaction ID with Actual Return code that does not exists in the master table if The return Code is D100
                                    if ($return_reason->reason_code == 'D100') {
                                        $unknownReasonCode = new UnknownReturnReason();
                                        $unknownReasonCode->transaction_id = $transaction[0]->id;
                                        $unknownReasonCode->return_code = $returns['returnCode'];
                                        $unknownReasonCode->acheck_document_id = $returns['documentId'];
                                        $unknownReasonCode->save();

                                        // Send Email to Admin for Unknown Return Reason received from ACHECK21
                                        Log::channel('return-transaction')->info(__METHOD__ . "(" . __LINE__ . ") - Sending Email to Admin as an Unknown Return Code: " . $returns['returnCode'] . " from ACHECK21...");
                                        $transaction_details = [
                                            'transaction' => $transaction[0],
                                            'return_code' => $returns['returnCode'],
                                        ];
                                        $this->emailexecutor->returnUpdateFailureEmailDueToUnkonwnReasonCode($transaction_details);
                                    }

                                    // Enabling the automatic purchase power feature as the consumer got a return transaction
                                    User::where('user_id', $transaction[0]->consumer_id)->update(['disable_automatic_purchase_power' => 0]);

                                    // If the consumer is manually linked and have Spending Limit of more than 150 then decrease it to 150 and also the weekly spending limit to 150
                                    $consumer = User::where('user_id', $transaction[0]->consumer_id)->first();
                                    if ($consumer->bank_link_type == 0 && $consumer->purchase_power > 150) {
                                        if ($consumer->existing_user == 0) {
                                            User::where('user_id', $consumer->user_id)->update(['standard_daily_limit' => 150, 'purchase_power' => 150, 'weekly_spending_limit' => 150]);
                                        } else {
                                            User::where('user_id', $consumer->user_id)->update(['purchase_power' => 150, 'weekly_spending_limit' => 150]);
                                        }
                                        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Purchase power and weekly spending limit decreased to 150 for User ID: " . $consumer->user_id);
                                    }

                                    // Email to Consumer based on return transaction
                                    Log::channel('return-transaction')->debug(__METHOD__ . "(" . __LINE__ . ") - Sending Email to Consumer based on return transaction...");
                                    $transaction_details_params = [
                                        'user_id' => $transaction[0]->consumer_id,
                                        'transaction_id' => $transaction[0]->id,
                                        'return_reason' => $return_reason->new_title,
                                    ];
                                    $this->emailexecutor->consumerReturnTransactionMail($transaction_details_params);

                                    // Remove the Account from Bank Details for Probable Return as the original return came from Acheck21 and now we don't need to stop the consumer from tranction as the return transaction will automatically block the consumer from any transaction until it got settled
                                    $account_details = BankAccountInfo::find($transaction[0]->account_id); // Fetch the account details used for the transaction
                                    $check_exists = BankDetailsForProbableReturn::where(['account_no' => $account_details->account_no, 'routing_no' => $account_details->routing_no])->count(); // Check if row exists in the table
                                    if ($check_exists > 0) { // This is done in order to print the log as many support issues were raised for consumer with not deleted records from the bank details probable returns table after return
                                        BankDetailsForProbableReturn::where(['account_no' => $account_details->account_no, 'routing_no' => $account_details->routing_no])->delete(); // Delete data from the table
                                        Log::channel('return-transaction')->debug(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Bank details deleted from the table for Error Code: 1007 for Transaction ID: " . $transaction[0]->id . " and Consumer ID: " . $consumer->user_id);
                                    } else {
                                        Log::channel('return-transaction')->debug(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Bank details does not exists in the bank details probabale returns table for Transaction ID: " . $transaction[0]->id . " and Consumer ID: " . $consumer->user_id);
                                    }

                                    Log::channel('return-transaction')->debug(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Return details updated for: " . json_encode($returns));
                                    Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Return details updated for document Id: " . $returns['documentId'] . " into transaction: " . $transaction[0]->id);
                                    $this->info("Return data updated for Transaction ID: " . $transaction[0]->id);
                                } else {
                                    Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Duplicate return data found for transaction for document id: " . $returns['documentId']);
                                    $this->info("Duplicate return transaction found. Skipping data.");
                                }
                            } else {
                                Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Transaction is not the latest row with Transaction ID : " . $transaction[0]->id . ' and document id:' . $returns['documentId'] . ". No action taken.");
                                $this->info("Transaction is not the latest row. Skipping data.");
                            }
                        } else {
                            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - No return transaction found for transaction for document id: " . $returns['documentId']);
                            $this->info("No return transaction found. Skipping data.");
                        }
                        $records++;
                        if (($records % 50) == 0) {
                            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Processed $records...");
                        }
                    } catch (\Exception $e) {
                        Log::channel('return-transaction')->error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured while trying to update status of Transaction ID: " . $transaction[0]->id . " for return.", [EXCEPTION => $e]);
                        continue; // Continue the process in case of anu exception occured.
                        $message = trans('message.db_transaction_fail');
                        $this->info($message); // Exception Returned
                    }
                }
                // send valid response to front end along with the total record count for returned transactions
                $message = trans('message.transaction_return_status_updated');
                Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . $response_decoded['recordsTotal'] . " Transactions updated with Return Data.");
                $this->info($message);
            } else {
                Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . $response_decoded['recordsTotal'] . " return transactions found from ACHECK21. No action taken.");
                // return valid response incase no returned transaction found from ACHECK21
                $message = trans('message.no_transaction_returns');
                $this->info($message);
            }
        } catch (\Exception $e) {
            Log::channel('return-transaction')->error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured while trying to update status of transactions for returns.", [EXCEPTION => $e]);
            $message = trans('message.db_transaction_fail');
            $this->info($message); // Exception Returned
        }
    }
}
