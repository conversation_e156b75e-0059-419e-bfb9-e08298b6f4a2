<?php

namespace App\Console\Commands;

use App\Http\Factories\Finicity\FinicityFactory;
use App\Http\Factories\PurchasePower\PurchasePowerFactory;
use App\Models\BankAccountInfo;
use App\Models\ConsumerAccountBalance;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class RefreshBalanceCall extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:refreshbalance';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will call refresh balance for the consumers with pending ecommerce transactions and for whom the refresh balance is not yet called.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->finicityFactory = new FinicityFactory();
        $this->purchasePower = new PurchasePowerFactory();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // Check wheather the parrellel purchase power algo is enabled or disabled
        $checkParellelAlgoEnabled = getSettingsValue('prallel_pp_algo', 0);
        // Check wheather the new purchase power algo is enabled or disabled
        $checkNewAlgoEnabled = getSettingsValue('enable_new_pp_algo', 0);
        // Fetch the Status ID
        $pending = getStatus(PENDING);
        $process_for_acheck = getStatus(PROCESSED_FOR_ACHECK21);
        $status = getStatus(BANK_ACTIVE);

        $this->info("Fetching consumers to refresh balance and update purchase power...");

        // Fetching all the users who have ecommerce pending transactions and for whom the refresh balance is not called for this day
        $user_sql = "SELECT DISTINCT u.*
        FROM transaction_details td
        JOIN transaction_details td1 ON td.id = td1.transaction_ref_no
        JOIN users u ON td.consumer_id = u.user_id
        WHERE
        td.transaction_ref_no IS NULL AND td.status_id = ? AND td1.status_id = ?  AND u.refresh_balance_called = 0 AND u.bank_link_type = 1 AND td.is_ecommerce = 1 ";
        $users = DB::select($user_sql, [$pending, $process_for_acheck]);

        foreach ($users as $user) {
            // Fetch all the finicicty linked accounts for the consumer
            Log::channel('refresh-balance-call')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Refreshing all the bank account balances for consumer with User ID: " . $user->user_id);

            $response = $this->finicityFactory->getConsumerRefreshAccountsBalance($user);
            try {
                $inc = 1;
                foreach ($response['accounts'] as $res) {
                    $bank_account = BankAccountInfo::where(['user_id' => $user->user_id, 'account_id' => $res['id']])->first();
                    if (!empty($bank_account)) {
                        // calculate the purchase power
                        $availableBalanceAmount = isset($res['detail']['availableBalanceAmount']) ? $res['detail']['availableBalanceAmount'] : 0;
                        $balance = $availableBalanceAmount > $res['balance'] ? $availableBalanceAmount : $res['balance'];
                        // Prepare data for purchase power calculation
                        $purchasePowerCalculationData = [
                            'account_id' => $bank_account->id,
                            'account_no' => $bank_account->account_no,
                            'balance' => $balance,
                            'updated_at' => Carbon::now(),
                            'parellel_pp_enabled' => $checkParellelAlgoEnabled,
                            'enable_new_pp_algo' => $checkNewAlgoEnabled,
                            'total_account_count' => count($response['accounts']),
                            'current_account' => $inc,
                            'user_details' => $user,
                        ];
                        $purchase_power_data = $this->purchasePower->calculatePurchasePower($purchasePowerCalculationData);
                        $purchase_power = $purchase_power_data['purchase_power'];
                        // add the bank balance into DB
                        $consumer_balance = new ConsumerAccountBalance();
                        $consumer_balance->consumer_id = $user->user_id;
                        $consumer_balance->account_id = $bank_account->id;
                        $consumer_balance->balance = $balance;
                        $consumer_balance->response_raw_balance = $res['balance'];
                        $consumer_balance->response_available_balance = $availableBalanceAmount;
                        $consumer_balance->purchase_power = $purchase_power;
                        $consumer_balance->purchase_power_source = $user->purchase_power_source;
                        $consumer_balance->source = SCHEDULED_REFRESH_BALANCE;
                        $consumer_balance->banking_solution_response = json_encode($res);
                        $consumer_balance->save();

                        if ($status === $bank_account->status) {
                            Log::channel('refresh-balance-call')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Calculated purchase power for User ID: " . $user->user_id . " is : $" . $purchase_power);
                            User::where(['user_id' => $user->user_id, 'disable_automatic_purchase_power' => 0])->update(array('purchase_power' => $purchase_power, 'refresh_balance_called' => 1));
                            Log::channel('refresh-balance-call')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Consumer purchase power updated into database successfully.");
                        }
                    }
                    $inc++;
                }
            } catch (\Exception $e) {
                Log::channel('refresh-balance-call')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while calling Refresh Customer Accounts for consumer with User ID: " . $user->user_id, [EXCEPTION => $e]);
                continue;
            }

            $this->info("Refresh Balance called for consumer with User ID: " . $user->user_id);
        }
    }
}
