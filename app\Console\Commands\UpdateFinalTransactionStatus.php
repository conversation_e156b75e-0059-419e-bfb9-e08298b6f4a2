<?php

namespace App\Console\Commands;

use App\Models\HolidayList;
use App\Models\JobRunnerHistory;
use App\Models\TransactionDetails;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class UpdateFinalTransactionStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:transaction';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command updates the final status for all the transactions that has been successfully settled.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->_updateTransactionStatus();
    }
    /**
     * This function updates the final status for all the transactions that has been successfully settled
     */
    private function _updateTransactionStatus()
    {
        $update_status_after_representment = 0;
        $today = Carbon::now();

        // Create an array of the next 15 days
        $dateArray = collect();
        for ($i = 0; $i < env('RELEASE_LOOP_PERIOD'); $i++) {
            $dateArray->push($today->copy()->subDays($i)->toDateString());
        }

        // Fetch the holidays from the HolidayList table in a single query
        $holidayList = HolidayList::whereIn('holiday_date', $dateArray)->pluck('holiday_date')->toArray();

        // Loop through the date array and check for weekdays
        $weekday_count = 0;
        $weekend_days = [];
        foreach ($dateArray as $day) {
            $date = Carbon::parse($day);
            $update_status_after_representment++;
            
            // Only increment weekday_count if it's not a weekend or holiday
            if (!$date->isWeekend() && !in_array($day, $holidayList)) {
                $weekday_count++;
            } else {
                $weekend_days[] = $day;
            }
            
            // Stop once we reach the desired number of weekdays
            if ($weekday_count >= env('UPDATE_STATUS_AFTER_REPRESENTMENT')) {
                break;
            }
        }
        if ($update_status_after_representment > env('UPDATE_STATUS_AFTER_REPRESENTMENT')) {
            Log::channel('update-final-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction data from $update_status_after_representment days ago was retrieved instead of ".env('UPDATE_STATUS_AFTER_REPRESENTMENT')." days ago due to a holiday or weekend on: ", $weekend_days);
        }

        Log::channel('update-final-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Fetching all the transactions from " . $update_status_after_representment . " days earlier...");
        $this->info("Fetching transactions...");
        $pending = getStatus(PENDING);
        $success = getStatus(SUCCESS);
        $processed_for_acheck = getStatus(PROCESSED_FOR_ACHECK21);
        //fetch all the transactions before mentioned day that has been posted for representment and update the status to success
        $sub_query_days_interval = $update_status_after_representment - 1;
        $transactions = TransactionDetails::on(MYSQL_RO)->join('user_bank_account_info', 'transaction_details.account_id', '=', 'user_bank_account_info.id')
            ->join('timezone_masters', 'timezone_masters.id', '=', 'transaction_details.timezone_id')
            ->join('users', 'users.user_id', '=', 'transaction_details.consumer_id')
            ->leftJoin('transaction_release_dates', function ($join) { // Will exclude the records in transaction_release_dates table
                $join->on('transaction_release_dates.transaction_id', '=', 'transaction_details.id')
                    ->where('transaction_release_dates.invalid', '=', 0);
            })
            ->select('transaction_details.*', 'timezone_masters.timezone_name', 'users.algo_user_type')
            ->where('transaction_details.transaction_ref_no', null)
            ->selectRaw('if(user_bank_account_info.account_id IS NOT NULL,1,0) AS bank_link_type')
            ->where('transaction_details.isCanpay', 0)->where('transaction_details.is_v1', 0)
            ->where('transaction_details.status_id', $pending)
            ->whereNotNull('user_bank_account_info.account_id') // Condition to fetch only Direct Transactions
            ->whereNull('transaction_release_dates.id')
            ->whereRaw('transaction_details.scheduled_posting_date <= (DATE_SUB(CURDATE(), INTERVAL ' . $update_status_after_representment . ' DAY))')
            ->whereNotIn('transaction_details.id', function ($query) use ($processed_for_acheck, $sub_query_days_interval) {
                $query->select('transaction_details.transaction_ref_no')
                    ->from(with(new TransactionDetails)->getTable())
                    ->where('transaction_details.status_id', $processed_for_acheck)
                    ->where('transaction_details.isCanpay', 0)
                    ->where('transaction_details.merchant_id', null)
                    ->where('transaction_details.transaction_ref_no', '!=', null)
                    ->whereRaw('transaction_details.local_transaction_date BETWEEN (DATE_SUB(CURDATE(), INTERVAL ' . $sub_query_days_interval . ' DAY)) AND CURDATE()')->get();
            })
            ->get();
        $today = date('Y-m-d');
        if (sizeof($transactions) != 0) {
            foreach ($transactions as $transaction) {
                $releasedays = env('DIRECT_BANK_LINK_RELEASE_DAYS');
                $isCheckOnLogin = 0;
                $data['bank_link_type'] = 1;
                if ($transaction->is_represented == 1 && $transaction->return_reason != null && $transaction->represent_count > 0 && $transaction->status_id == $pending) {
                    Log::channel('update-final-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction ID : " . $transaction->id . " checking started for Manual Bank link conditions as the transaction is represented");
                    $transaction_represented = TransactionDetails::where('transaction_ref_no', $transaction->id)->where('is_represented', 1)->orderBy('created_at', 'DESC')->first();
                    $releaseDate = getTransactionReleaseDate(date('Y-m-d', strtotime($transaction_represented->created_at)), $transaction->id);
                    $releasedays = env('MANUAL_BANK_LINK_RELEASE_DAYS');
                    $data['bank_link_type'] = 0;
                } else if ($transaction->bank_link_type == 1) { // Check if user is direct linked at the time of transaction
                    Log::channel('update-final-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction ID : " . $transaction->id . " checking started for Direct link conditions");
                    $transaction_amount = $transaction->amount + $transaction->tip_amount;
                    [$releaseDate, $isCheckOnLogin] = getDirectTransactionConditions($transaction->scheduled_posting_date, $transaction->consumer_id, $transaction->account_id, $transaction_amount, $transaction->id);
                   
                    if (!$releaseDate) {
                        $releaseDate = getTransactionReleaseDate($transaction->scheduled_posting_date, $transaction->id);
                        $releasedays = env('MANUAL_BANK_LINK_RELEASE_DAYS');
                        $data['bank_link_type'] = 0;
                    }
                } else {
                    Log::channel('update-final-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction ID : " . $transaction->id . " checking started for Manual Bank link conditions");
                    $releaseDate = getTransactionReleaseDate($transaction->scheduled_posting_date, $transaction->id);
                    $releasedays = env('MANUAL_BANK_LINK_RELEASE_DAYS');
                    $data['bank_link_type'] = 0;
                }
                if ($releaseDate && $releaseDate <= $today) {
                    // update the parent transaction
                    TransactionDetails::where('id', $transaction->id)->update(array('status_id' => $success));
                    // Settle Points associated with the Transaction
                    updateRewardStatus($transaction, UPDATE_FINAL_TRANSACTION);
                    // created new success row
                    createSuccessTransaction($transaction, UPDATE_FINAL_TRANSACTION);
                    // Check and update the user's algo_user_type
                    checkAndUpdateAlgoUserType($transaction);
                    Log::channel('update-final-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction ID : " . $transaction->id . "  updated and released in " . $releasedays . " working days successfully.");
                    $this->info("Transaction ID: " . $transaction->id . " updated successfully.");
                } else {
                    // Insert into Transaction Release Dates table
                    $data['transaction_id'] = $transaction->id;
                    $data['release_date'] = $releaseDate;
                    insertIntoTransactionReleaseDates($data, UPDATE_FINAL_TRANSACTION, $isCheckOnLogin);
                    $this->info("Transaction ID: " . $transaction->id . " inserted into Transaction Release Dates table.");
                }
            }
        } else {
            Log::channel('update-final-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No transactions found to update.");
            $this->info("No transactions found to update.");
        }
    }
}
