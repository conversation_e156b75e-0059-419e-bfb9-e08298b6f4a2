<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class TestKafkaListener extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:kafka';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will test the Kafka listener';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // $this->info("Kafka listener started...");
        // $conf = new \RdKafka\Conf();
        // $conf->set('bootstrap.servers', ENV('KAFKA_BOOTSTRAP_SERVER'));
        // $conf->set('security.protocol', ENV('KAFKA_SECURITY_PROTOCOL'));
        // $conf->set('sasl.mechanism', 'PLAIN');
        // $conf->set('sasl.username', ENV('KAFKA_USERNAME'));
        // $conf->set('sasl.password', ENV('KAFKA_PASSWORD'));
        // $conf->set('group.id', 'group');
        // $conf->set('auto.offset.reset', 'earliest');

        // $topicConf = new \RdKafka\TopicConf();

        // $conf->setDefaultTopicConf($topicConf);

        // $consumer = new \RdKafka\KafkaConsumer($conf);

        // $consumer->subscribe(['topic_kafka_listener']);

        // while (true) {
        //     $message = $consumer->consume(30 * 1000);
        //     switch ($message->err) {
        //         case RD_KAFKA_RESP_ERR_NO_ERROR:
        //             $this->info($message->payload);
        //             sleep(10);
        //             $this->info("Hello World " . $message->payload);
        //             break;
        //         case RD_KAFKA_RESP_ERR__PARTITION_EOF:
        //             $this->info("No more messages; will wait for more");
        //             break;
        //         case RD_KAFKA_RESP_ERR__TIMED_OUT:
        //             $this->info("Timed out");
        //             break;
        //         default:
        //             throw new \Exception($message->errstr(), $message->err);
        //             break;
        //     }
        // }
    }
}
