<?php

namespace App\Console\Commands;

use App\Http\Factories\Kafka\KafkaFactory;
use App\Models\ValidationLog;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PostCognitoDataInKafka extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'post:notpostedcognitodata {--from_date=}';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will post the cognito assesment data to kafka that has not been posted yet or get skipped.';
    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->info("Fetch the users whose data has not been posted to kafka...");
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Fetch the users whose data has not been posted to kafka...");
        $user_role = getRole(CONSUMER);
        $from_date = $this->option('from_date') ? Carbon::parse($this->option('from_date')) : Carbon::now()->subDay()->format('Y-m-d H:i:s');
        $users = DB::select("SELECT u.*
         FROM users u
         LEFT JOIN consumer_purchase_power_decision_table ppdt ON u.registration_ref_id = ppdt.registration_session_details_id
         WHERE u.role_id = ? AND u.created_at > ?
         AND ppdt.id IS NULL", [$user_role, $from_date]);
        if (!empty($users)) {
            $this->info("Found " . count($users) . " users for which data has not been posted to kafka...");
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Found " . count($users) . " users for which data has not been posted to kafka...");
            foreach ($users as $user) {
                // Fetch the assesment data for the user
                $assesment_data = ValidationLog::where("phone", $user->phone)->whereIn('api', ['/identity_assessments', '/identity_searches'])->orderBy('created_at', 'desc')->get();
                $this->info("Found " . count($assesment_data) . " assesment data for user " . $user->phone . "...");
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Found " . count($assesment_data) . " assesment data for user " . $user->phone . "...");
                if (!$assesment_data->isEmpty()) {
                    $identity_assessment_response = null;
                    $identity_search_response = null;

                    foreach ($assesment_data as $data) {
                        if ($data->api == '/identity_assessments') {
                            $identity_assessment_response = json_decode($data->response, true);
                        } elseif ($data->api == '/identity_searches') {
                            $identity_search_response = json_decode($data->response, true);
                        }
                    }

                    // Prepare the data to be posted
                    $kafka_cognito_object = [
                        "registration_session_id" => $user->registration_ref_id,
                        "phone" => $user->phone,
                        "email" => $user->email,
                        "error_message" => null,
                        "identity_search_response" => $identity_search_response,
                        "identity_assessment_response" => $identity_assessment_response,
                    ];

                    $kafka = new KafkaFactory();
                    $kafka->produce(ENV('KAFKA_TOPIC_NETWORK_CONSUMER_COGNITO_DATA'), json_encode($kafka_cognito_object));
                    $this->info("Successfully posted data for user " . $user->phone . " to kafka.");
                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Successfully posted data for user " . $user->phone . " to kafka.");
                }

            }
        } else {
            $this->info("No users found for which data has not been posted to kafka.");
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No users found for which data has not been posted to kafka.");
        }

    }

}
