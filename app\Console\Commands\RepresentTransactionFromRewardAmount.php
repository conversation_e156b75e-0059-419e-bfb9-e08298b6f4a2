<?php

namespace App\Console\Commands;

use App\Models\RewardWheel;
use App\Models\TransactionDetails;
use App\Models\TransactionRewardDetail;
use App\Models\UserCurrentRewardDetail;
use App\Models\UserRewardUsageHistory;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class RepresentTransactionFromRewardAmount extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'represent:rewardamount';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will represent the transaction after 60 days if still in returned status and will deduct the balance from the accumulated reward points of the consumer.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Log::channel('represent-transaction-reward-amount')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Fetching all the transactions from " . env('REWARD_AMOUNT_DEDUCTION_DAYS') . " days earlier having Returned Status...");
        $this->info("Fetching all the transactions from " . env('REWARD_AMOUNT_DEDUCTION_DAYS') . " days earlier having Returned Status...");
        // Get the Status ID
        $returned = getStatus(RETURNED);
        $reward_amount_deducted_for_return_representment = getStatus(REWARD_AMOUNT_DEDUCTED_FOR_RETURN_REPRESENTMENT);
        $success = getStatus(SUCCESS);

        $transactions = TransactionDetails::join('timezone_masters', 'timezone_masters.id', '=', 'transaction_details.timezone_id')
            ->join('terminal_master', 'transaction_details.terminal_id', '=', 'terminal_master.id')
            ->join('merchant_stores', 'terminal_master.merchant_store_id', '=', 'merchant_stores.id')
            ->select('transaction_details.*', 'timezone_masters.timezone_name', 'timezone_masters.id as timezone_id', 'merchant_stores.id as store_id')
            ->where('transaction_details.transaction_ref_no', null)
            ->where('transaction_details.isCanpay', 0)->where('transaction_details.is_v1', 0)
            ->where('transaction_details.status_id', $returned)->whereRaw('transaction_details.local_transaction_date <= (DATE_SUB(CURDATE(), INTERVAL ' . env('REWARD_AMOUNT_DEDUCTION_DAYS') . ' DAY))')
            ->get();

        if (sizeof($transactions) != 0) {
            $total_reward_amount = 0;
            $total_reward_point = 0;
            foreach ($transactions as $transaction) {

                // $reward_details = $this->_updateTransactionRewards($transaction);
                $rewards = explode('^', $reward_details);
                $total_reward_amount = $total_reward_amount + $rewards[0];
                $total_reward_point = $total_reward_point + $rewards[1];

                if ($transaction->consumer_bank_posting_amount == 0) {
                    // As the reward_amount is more than the consumer_bank_posting_amount, we need to settle the transaction
                    $this->_createTransaction($transaction, $reward_amount_deducted_for_return_representment, $reward_details);
                    // Add row to settle the transaction
                    $this->_createTransaction($transaction, $success, $reward_details);

                    // Update the Parent Transaction
                    $this->_updateTransaction($transaction, $success);

                    Log::channel('represent-transaction-reward-amount')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction ID:" . $transaction->id . " settled.");
                } else {
                    // The reward_amount will be deducted from the consumer_bank_posting_amount and the status will be updated with Reward Amount Deducted for Return Representment
                    $this->_createTransaction($transaction, $reward_amount_deducted_for_return_representment, $reward_details);

                    // Update the Parent Transaction
                    $this->_updateTransaction($transaction, $reward_amount_deducted_for_return_representment);
                    Log::channel('represent-transaction-reward-amount')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction ID:" . $transaction->id . " status changed to - Reward Amount Deducted for Return Representment.");
                }
            }
            // Post the total Amount in Acheck21
            $this->_createRewardAmountDebitTransaction($transaction, $total_reward_amount, $total_reward_point);
        } else {
            Log::channel('represent-transaction-reward-amount')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No transactions found for representment with reward amount.");
            $this->info("No transactions found for representment with reward amount.");
        }
    }

    private function _createRewardAmountDebitTransaction($transaction, $total_reward_amount, $total_reward_point)
    {
        Log::channel('represent-transaction-reward-amount')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Posting Canpay Return Recovery Reward Amount to acheck21");

        if ($total_reward_amount != 0) {
            $amount = $total_reward_amount;
            $params['amount'] = $amount;
            $params['acheck_account_id'] = env('CANPAY_RETURN_REWARD_POINT');
            //calling the factory function to create consumer transaction into acheck21
            if (env('ACHECK_POSTING')) {
                $response = $this->transaction->createCanPayRewardTransaction($params);
                $response_decoded = json_decode($response, true);
                Log::channel('represent-transaction-reward-amount')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is enabled. Posting in ACHECK21.");
            } else {
                $response_decoded['documentId'] = rand(********, ********);
                Log::channel('represent-transaction-reward-amount')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is disabled. Continue with a random number.");
            }
            if (isset($response_decoded['documentId'])) {
                // create a new transaction
                $saveTransactionArray = [
                    'transaction' => $transaction,
                    'reward_point_used' => $total_reward_point,
                    'reward_amount_used' => $total_reward_amount,
                    'tip_amount' => 0,
                    'entry_type' => 'Dr',
                    'acheck_document_id' => $response_decoded['documentId'],
                ];
                $this->_saveTransaction($saveTransactionArray);

                Log::channel('represent-transaction-reward-amount')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "CanPay Return Recovery Reward amount details was stored successfully for amount: " . $params['amount']);
                $this->info("CanPay Return Recovery Reward Amount details was stored successfully.");
            } else {
                Log::channel('represent-transaction-reward-amount')->error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "There was some problem trying to post return recovery reward point transactions into Acheck21.");
                $this->info("There was some problem trying to post return recovery reward point transactions into Acheck21.");
            }
        } else {
            Log::channel('represent-transaction-reward-amount')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No Transactions found with Canpay Return Recovery Points.");
            $this->info("No Transactions found with Canpay Return Recovery Points.");
        }
    }

    private function _saveTransaction($params)
    {
        $process_for_acheck21 = getStatus(PROCESSED_FOR_ACHECK21);
        $transaction_details = new TransactionDetails();
        $transaction_details->transaction_number = $params['transaction']->transaction_number;
        $transaction_details->transaction_time = Carbon::now();
        $transaction_details->local_transaction_time = Carbon::now($transaction_details->timezone_name);
        $transaction_details->local_transaction_year = date("Y", strtotime($transaction_details->local_transaction_time));
        $transaction_details->local_transaction_month = date("m", strtotime($transaction_details->local_transaction_time));
        $transaction_details->local_transaction_date = date("Y-m-d", strtotime($transaction_details->local_transaction_time));
        $transaction_details->timezone_id = $params['transaction']->timezone_id;
        $transaction_details->reward_point_used = $params['reward_point_used'];
        $transaction_details->reward_amount_used = $params['reward_amount_used'];
        $transaction_details->amount = $params['reward_amount_used'];
        $transaction_details->tip_type = $params['transaction']->tip_type;
        $transaction_details->tip_add_time = $params['transaction']->tip_add_time;
        $transaction_details->entry_type = $params['entry_type'];
        $transaction_details->status_id = $process_for_acheck21;
        $transaction_details->transaction_type_id = $params['transaction']->transaction_type_id;
        $transaction_details->transaction_place = ACHECK21;
        $transaction_details->reward_acheck_posting_return_recovery = 1;
        $transaction_details->save();
    }

    private function _createTransaction($transaction, $status_id, $reward_details)
    {
        $rewards = explode('^', $reward_details);
        $transaction_details = new TransactionDetails();
        $transaction_details->transaction_number = $transaction->transaction_number;
        $transaction_details->transaction_ref_no = $transaction->id;
        $transaction_details->user_id = $transaction->user_id;
        $transaction_details->consumer_id = $transaction->consumer_id;
        $transaction_details->terminal_id = $transaction->terminal_id;
        $transaction_details->transaction_time = Carbon::now();
        $transaction_details->local_transaction_time = Carbon::now($transaction->timezone_name);
        $transaction_details->local_transaction_year = date("Y", strtotime($transaction_details->local_transaction_time));
        $transaction_details->local_transaction_month = date("m", strtotime($transaction_details->local_transaction_time));
        $transaction_details->local_transaction_date = date("Y-m-d", strtotime($transaction_details->local_transaction_time));
        $transaction_details->timezone_id = $transaction->timezone_id;
        $transaction_details->consumer_bank_posting_amount = $transaction->consumer_bank_posting_amount;
        $transaction_details->reward_amount_used = $transaction->reward_amount_used;
        $transaction_details->reward_point_used = $transaction->reward_point_used;
        $transaction_details->amount = $transaction->amount;
        $transaction_details->return_representment_reward_deduction_amount = $rewards[0];
        $transaction_details->return_representment_reward_deduction_point = $rewards[1];
        $transaction_details->tip_amount = $transaction->tip_amount;
        $transaction_details->tip_type = $transaction->tip_type;
        $transaction_details->tip_add_time = $transaction->tip_add_time;
        $transaction_details->used_qr_id = $transaction->used_qr_id;
        $transaction_details->status_id = $status_id;
        $transaction_details->transaction_type_id = $transaction->transaction_type_id;
        $transaction_details->transaction_place = REWARD_AMOUNT_DEDUCTED;
        $transaction_details->save();
    }

    private function _updateTransaction($transaction, $status_id)
    {
        $transaction_details = TransactionDetails::find($transaction->id);
        $transaction_details->status_id = $status_id;
        $transaction_details->save();
    }

    /**
     * _updateTransactionRewards
     * Update the Reward Wheel Module tables after succesful trnsaction
     * @param  mixed $transaction
     * @return void
     */
    private function _updateTransactionRewards($transaction)
    {
        Log::channel('represent-transaction-reward-amount')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Update the Reward Points for Return representment for Transaction ID: " . $transaction->id);

        $active = getStatus(ACTIVE);
        $reward_inactive = getStatus(REWARD_WHEEL_INACTIVE);
        $reward_status = [$active, $reward_inactive];

        $reward_wheel_point_details = UserCurrentRewardDetail::leftJoin('reward_store_maps', function ($join) use ($transaction) {
            $join->on('reward_store_maps.reward_wheel_id', '=', 'reward_wheels.id');
            $join->on('reward_store_maps.store_id', '=', DB::raw("'$transaction->store_id'"));
        })
            ->select('user_current_reward_details.*')
            ->whereIn('reward_wheels.status_id', $reward_status)
            ->where('user_current_reward_details.reward_amount', '>', 0)
            ->where('user_current_reward_details.consumer_id', $transaction->consumer_id)
            ->orderBy('is_merchant_wheel', 'DESC')
            ->get();

        $transaction_amount = $transaction->consumer_bank_posting_amount;
        $reward_point_left = floor($transaction_amount / env('EXCHANGE_RATE')); // Calculating the equivalent integer value for Reward Point
        Log::channel('represent-transaction-reward-amount')->info("Transaction Amount equivalent reward point: " . $reward_point_left);
        if (!empty($reward_wheel_point_details)) {
            $reward_point_used = 0;
            $reward_amount_used = 0;
            foreach ($reward_wheel_point_details as $reward_point_detail) { // Loop for Consumer Reward Point Session
                $reward_usage_details = UserRewardUsageHistory::join('reward_wheels', 'reward_wheels.id', '=', 'user_reward_usage_history.reward_wheel_id')
                    ->where(['user_reward_usage_history.user_id' => $transaction->consumer_id, 'user_reward_usage_history.entry_type' => CREDIT, 'user_reward_usage_history.reward_wheel_id' => $reward_point_detail->reward_wheel_id])->where('user_reward_usage_history.reward_point_left', '>', 0)
                    ->whereIn('reward_wheels.status_id', $reward_status)
                    ->orderBy('user_reward_usage_history.created_at')->get(); // Fetch the Credit Row against the Reward Wheel ID

                $reward_point_left = $reward_point_detail->reward_point < $reward_point_left ? $reward_point_detail->reward_point : $reward_point_left; // Set the Reward point left to the Consumer Reward Session or Transaction Amount whichever is lower
                if (count($reward_usage_details) > 0) {
                    if ($reward_point_left > 0) {
                        // These variables will store points and amount only for a particular reward wheel
                        $reward_wheel_point_left = 0;
                        $reward_wheel_amount_used = 0;
                        $break = 0;
                        foreach ($reward_usage_details as $reward) {
                            if ($reward_point_left >= $reward->reward_point_left) {
                                $reward_point_for_decrement = $reward->reward_point_left;
                                $reward_point_used += $reward->reward_point_left;
                                $reward_amount_for_decrement = $reward->reward_amount_left;
                                Log::channel('represent-transaction-reward-amount')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Reward Point Left is more than point left in the User Reward Usage History - Reward Amount Left: " . $reward_amount_for_decrement . " and Reward Point Used: " . $reward_point_used . " for Transaction ID:" . $transaction->id);
                            } else {
                                $reward_point_for_decrement = $reward_point_left;
                                $reward_point_used += $reward_point_for_decrement;
                                $reward_amount_for_decrement = $reward_point_left * env('EXCHANGE_RATE');
                                $break = 1;
                                Log::channel('represent-transaction-reward-amount')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Reward Point Left is less than point left in the User Reward Usage History - Reward Amount Left: " . $reward_amount_for_decrement . " and Reward Point Used: " . $reward_point_for_decrement . " for Transaction ID:" . $transaction->id);
                            }
                            $reward_wheel_point_left = $reward_point_for_decrement;
                            $reward_wheel_amount_used += $reward_amount_for_decrement;
                            $reward_amount_used += $reward_amount_for_decrement;
                            // Update the User Reward Usage History table with the reward points and reward amount Used
                            $user_reward_usage_history = UserRewardUsageHistory::find($reward->id);
                            $user_reward_usage_history->reward_point_left = $user_reward_usage_history->reward_point_left - $reward_point_for_decrement;
                            $user_reward_usage_history->reward_amount_left = $user_reward_usage_history->reward_amount_left - $reward_amount_for_decrement;
                            $user_reward_usage_history->save();

                            if ($break == 1) {
                                break;
                            }
                        }

                        // Add details of transaction in UserRewardUsageHistory table with entry type debit
                        $reward_usage_history = new UserRewardUsageHistory(); // incorrect data inserted in table
                        $reward_usage_history->user_id = $transaction->consumer_id;
                        $reward_usage_history->reward_wheel_id = $reward_point_detail->reward_wheel_id;
                        $reward_usage_history->entry_type = DEBIT;
                        $reward_usage_history->reason = REWARD_AMOUNT_REPRESENTMENT;
                        $reward_usage_history->transaction_id = $transaction->id;
                        $reward_usage_history->reward_point = $reward_wheel_point_left;
                        $reward_usage_history->reward_amount = $reward_wheel_amount_used;
                        $reward_usage_history->exchange_rate = env('EXCHANGE_RATE');
                        $reward_usage_history->save();

                        // Update UserCurrentRewardDetail table
                        $user_current_reward_details = UserCurrentRewardDetail::where(['user_id' => $transaction->consumer_id]);
                        $reward->is_generic_point == 0 ? $user_current_reward_details->where('corporate_parent_id', $reward->corporate_parent_id) : '';
                        $user_current_reward_details = $user_current_reward_details->first();
                        $user_current_reward_details->reward_point = $user_current_reward_details->reward_point - $reward_wheel_point_left;
                        $user_current_reward_details->reward_amount = $user_current_reward_details->reward_amount - $reward_wheel_amount_used;
                        $user_current_reward_details->save();

                        // Add in the TransactionRewardDetails
                        $transaction_reward_details = new TransactionRewardDetail();
                        $transaction_reward_details->transaction_id = $transaction->id;
                        $transaction_reward_details->reward_wheel_id = $reward_point_detail->reward_wheel_id;
                        $transaction_reward_details->reward_point = $reward_wheel_point_left;
                        $transaction_reward_details->reward_amount = $reward_wheel_amount_used;
                        $transaction_reward_details->exchange_rate = env('EXCHANGE_RATE');
                        $transaction_reward_details->save();

                        Log::channel('represent-transaction-reward-amount')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Reward Point details updated for Transaction ID: " . $transaction->id);
                    } else {
                        Log::channel('represent-transaction-reward-amount')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - No Reward Details to update for Reward Wheel ID: " . $reward_point_detail->reward_wheel_id . " for Transaction ID:" . $transaction->id);
                    }
                } else {
                    Log::channel('represent-transaction-reward-amount')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Reward Point does not exists for Reward Wheel ID: " . $reward_point_detail->reward_wheel_id . " for Transaction ID:" . $transaction->id);
                }
            }

            Log::channel('represent-transaction-reward-amount')->info($transaction->consumer_bank_posting_amount);
            // Update the reward point used in the transaction_details table after calculation of equivalent reward point from reward amount
            $transaction->consumer_bank_posting_amount = $transaction->consumer_bank_posting_amount - $reward_amount_used;
            $transaction->reward_amount_used = $transaction->reward_amount_used + $reward_amount_used;
            $transaction->reward_point_used = $transaction->reward_point_used + $reward_point_used;
            $transaction->return_representment_reward_deduction_amount = $transaction->return_representment_reward_deduction_amount + $reward_amount_used;
            $transaction->return_representment_reward_deduction_point = $transaction->return_representment_reward_deduction_point + $reward_point_used;
            $transaction->save();

            Log::channel('represent-transaction-reward-amount')->info($transaction);

            Log::channel('represent-transaction-reward-amount')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Tables updated for Reward Point: " . $reward_point_used . " and Reward Amount: " . $reward_amount_used . " and Consumer Bank Posting Amount: " . $transaction->consumer_bank_posting_amount . " for Transaction ID: " . $transaction->id);
        } else {
            Log::channel('represent-transaction-reward-amount')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - No Reward Point used during Transaction for Transaction ID: " . $transaction->id);
        }

        Log::channel('represent-transaction-reward-amount')->info($reward_amount_used . '^' . $reward_point_used);
        return $reward_amount_used . '^' . $reward_point_used;
    }
}
