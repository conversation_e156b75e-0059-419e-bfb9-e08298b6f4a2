<?php

namespace App\Console\Commands;

use App\Http\Clients\Acheck21HttpClient;
use App\Http\Factories\Transaction\TransactionFactory;
use App\Http\Factories\Webhook\WebhookFactory;
use App\Models\Acheck21HistoryTable;
use App\Models\StatusMaster;
use App\Models\TransactionDetails;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class RevokeVoidTransaction extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'revoke:voidtransaction {--transaction_ids=}';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will revoke void Transactions.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->transaction = new TransactionFactory();
        $this->acheck = new Acheck21HttpClient();
        $this->merchantWebhook = new WebhookFactory();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->info("Revoking Void Transactions...");
        if ($this->option('transaction_ids') != '') {
            $id_array = explode(",", $this->option('transaction_ids'));
            Log::channel('revoke-void-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Revoke Void Transaction Started... ");

            foreach ($id_array as $transaction_id) {
                $transaction = TransactionDetails::find($transaction_id);
                $voided = getStatus(VOIDED);
                if ($transaction->status_id == $voided) {
                    $reverted_status = $this->revokeVoidTransaction($transaction);
                    if ($reverted_status) {
                        $this->info("Transaction Void Revoked for Transaction ID: " . $transaction_id);
                    } else {
                        $this->info("Transaction ID: " . $transaction_id . " cannot be revoke voided"); 
                    }
                } else {
                    $this->info("Transaction ID: " . $transaction_id . " cannot be revoke voided as Status ID is: " . $transaction->status_id);
                    Log::channel('revoke-void-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction ID: " . $transaction_id . " cannot be voided as Status ID is: " . $transaction->status_id);
                }
            }
        } else {
            $this->info("Transaction ID not given.");
        }
    }


    /**
     * revokeVoidTransaction
     * This function will revoke the Void Transaction
     * @param  mixed $params
     * @return void
     */
    private function revokeVoidTransaction($transaction)
    {
        DB::beginTransaction();
        try {
            // Get the Status ID
            $pending = getStatus(PENDING);
            $void_revoked = getStatus(VOID_REVOKED);
            $voided = getStatus(VOIDED);
            $process_for_acheck21 = getStatus(PROCESSED_FOR_ACHECK21);
            $awaiting_consumer_approval = getStatus(AWAITINGCONSUMERAPPROVAL);
            $approved_by_consumer = getStatus(APPROVED_BY_CONSUMER);
            $request_timeout = getStatus(REQUEST_TIMEOUT);

            //get transaction id
            $transactionId = $transaction->id;
            $scheduled_posting_date = $transaction->scheduled_posting_date;
            if ($transaction->change_request_transaction_ref_no != null) {
                // get modified transaction
                $modified_transaction = TransactionDetails::where('id', $transaction->change_request_transaction_ref_no)->first();
                if ($modified_transaction) {
                    $scheduled_posting_date = $modified_transaction->scheduled_posting_date;
                    //get modified transaction id
                    $transactionId = $transaction->change_request_transaction_ref_no;

                    // if accpeted/approved modification then make it pending
                    if (in_array($transaction->consumer_approval_for_change_request, [$approved_by_consumer])) {
                        // Update the Child row to Pending
                        $modified_transaction->status_id = $pending;
                        $modified_transaction->save();
                    }
                }
                // Update the Parent row change request to 1
                $transaction->change_request = 1;
            } else {
                // if parent transaction not modified
                if ($transaction->consumer_approval_for_change_request == null) {
                    // Update the Parent row to Pending
                    $transaction->status_id = $pending;
                } else {
                    // Update the Parent row change request to 1
                    $transaction->change_request = 1;
                }
            }
            $transaction->save();

            // use for void previous
            $latestTransation = TransactionDetails::where('id', $transactionId)->first();
            $transaction_sql = "SELECT td1.acheck_document_id, rmm.acheck_account_id
                FROM transaction_details td
                JOIN terminal_master tm ON tm.id = td.terminal_id
                JOIN merchant_stores ms ON tm.merchant_store_id = ms.id
                JOIN registered_merchant_master rmm ON rmm.id = ms.merchant_id
                LEFT JOIN transaction_details td1 FORCE INDEX(idx_trans_ref_no) ON td.id = td1.transaction_ref_no AND td1.status_id = ?
                WHERE td.id = ? ";

            $transaction_details = DB::select($transaction_sql, [$process_for_acheck21, $transactionId]);

            $params['acheck_account_id'] = $transaction_details[0]->acheck_account_id;
            $params['acheck_document_id'] = $transaction_details[0]->acheck_document_id;

            // Check the Status of the Last row
            $transaction_lastest_row = TransactionDetails::where(['transaction_ref_no' => $transactionId])
                ->orWhere(['id' => $transactionId])
                ->orderBy('created_at', 'DESC')->first();

            // Check if the Transaction is posted to Acheck21 (If posted, then delete it from Acheck21 side)
            if (!empty($transaction_lastest_row) && $transaction_lastest_row->status_id == $process_for_acheck21) {

                // Void the Transaction on the Acheck21 side
                if (env('ACHECK_POSTING')) {
                    $response = $this->acheck->deleteTransaction($params);
                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is enabled. Posting in ACHECK21.");
                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction voided in the Acheck21 side for amount: " . $transaction->amount . " with Transaction Number: " . $transaction->transaction_number . " ,acheck_account_id: " . $params['acheck_account_id'] . " and acheck_document_id: " . $params['acheck_document_id']);
                } else {
                    $response = 204;
                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is disabled. Continue with static 204 response.");
                }

                if ($response == 204) {
                    // If the Transaction is Voided From Admin Panel but the Voided Sub row does not exists then need to insert it from here to keep the log of voided transaction
                    $this->_createTransaction($latestTransation, $voided);
                }
            } else if (!empty($transaction_lastest_row) && $transaction_lastest_row->status_id != $voided) {
                // If the Transaction is Voided From Admin Panel but the Voided Sub row does not exists then need to insert it from here to keep the log of voided transaction
                $this->_createTransaction($latestTransation, $voided);
            }

            // use for webhook call
            $status = SETTLED;
            $expiry_date_for_book_now = '';
            // Add new row with status Void Revoked
            $this->_createTransaction($latestTransation, $void_revoked);
            if (!empty($transaction_lastest_row) && $transaction_lastest_row->status_id == $process_for_acheck21) {
                // Post to Acheck21 after Void Revoked
                $this->_postTransactionIntoAcheck21($latestTransation, $params);
            } else {

                // use for webhook call
                if ($transaction->attempt_count > 0) {
                    $transactionStatusId = $transaction->consumer_approval_for_change_request;
                    if ($transaction->consumer_approval_for_change_request == $awaiting_consumer_approval && $transaction->expiration_datetime) {
                        $store_current_date_in_utc = Carbon::now('UTC');
                        $expiration_datetime_in_utc = Carbon::parse($transaction->expiration_datetime_in_utc);
                        if ($expiration_datetime_in_utc->lt($store_current_date_in_utc)) {
                            $transactionStatusId = $request_timeout;
                        }
                    }
                } else {
                    $transactionStatusId = $pending;
                }
                $status = StatusMaster::find($transactionStatusId)->status;
                //need to Accept Payment if transaction is admin driven and status pending , approved or auto approved
                $expiry_date_for_book_now = '';
                if ($transaction->admin_driven && in_array($transactionStatusId, [$pending, $approved_by_consumer])) {
                    if (!isset($scheduled_posting_date)) {
                        $expiry_date_for_book_now = date('m-d-Y', strtotime('+' . env('ADMIN_DRIVEN_TRANSACTION_EXPIRY_DAYS') . ' day', strtotime($transaction->local_transaction_date)));
                    }
                    if ($transactionStatusId == $pending && isset($scheduled_posting_date)) {
                        $status = SETTLED;
                    }
                }
                if ($expiry_date_for_book_now == '') {
                    // Post to Acheck21 after Void Revoked
                    $this->_postTransactionIntoAcheck21($latestTransation, $params);
                }
            }
            //webhook call after revoke void
            $this->merchantWebhook->modificationWebhookCall($transactionId, $status, $expiry_date_for_book_now);

            // After Void Revoked Revert back the Points to Original
            $reverted_status = addDebitAfterTransactionVoidRevoked($transaction);
            if (!$reverted_status) {
                DB::rollback();
                Log::info(__METHOD__ . "(Line: " . __LINE__ . ") - " . "Reward Point does not exists for Transaction ID: " . $transaction->id);
                return false;
            }

            // Revert Back the Points Against the Transaction
            revertPointsAganistTransaction($transaction, 0);

            // Void Revoke Lottery Rewards if the User has won Lottery
            voidRevokeLotteryReward($transaction, 0);

            DB::commit();
            Log::channel('revoke-void-transaction')->info(__METHOD__ . "(Line: " . __LINE__ . ") - " . "Transaction ID: " . $transaction->id . " revoked successfully.");
            return true;
        } catch (\Exception $e) {
            Log::channel('revoke-void-transaction')->error(__METHOD__ . "(Line: " . __LINE__ . ") - " . "Exception while storing transaction details.", [EXCEPTION => $e]);
            DB::rollback();
            return false;
        }
    }


    private function _createTransaction($transaction, $status_id)
    {
        $voided = getStatus(VOIDED);
        $transaction_details = new TransactionDetails();
        $transaction_details->transaction_number = $transaction->transaction_number;
        $transaction_details->transaction_ref_no = $transaction->id;
        $transaction_details->user_id = $transaction->user_id;
        $transaction_details->consumer_id = $transaction->consumer_id;
        $transaction_details->terminal_id = $transaction->terminal_id;
        $transaction_details->transaction_time = $transaction->transaction_time;
        $transaction_details->local_transaction_time = $transaction->local_transaction_time;
        $transaction_details->local_transaction_year = date("Y", strtotime($transaction_details->local_transaction_time));
        $transaction_details->local_transaction_month = date("m", strtotime($transaction_details->local_transaction_time));
        $transaction_details->local_transaction_date = date("Y-m-d", strtotime($transaction_details->local_transaction_time));
        $transaction_details->timezone_id = $transaction->timezone_id;
        $transaction_details->amount = $transaction->amount;
        $transaction_details->tip_amount = $transaction->tip_amount;
        $transaction_details->tip_type = $transaction->tip_type;
        $transaction_details->pp_type = $transaction->pp_type;
        $transaction_details->pp_value = $transaction->pp_value;
        $transaction_details->tip_add_time = $transaction->tip_add_time;
        $transaction_details->used_qr_id = $transaction->used_qr_id;
        $transaction_details->status_id = $status_id;
        $transaction_details->account_id = $transaction->account_id;
        $transaction_details->authcode = $transaction->authcode;
        $transaction_details->zipline_session_id = $transaction->zipline_session_id;
        $transaction_details->zipline_trans_id = $transaction->zipline_trans_id;
        $transaction_details->bank_balance_at_transation_time = $transaction->bank_balance_at_transation_time;
        $transaction_details->transaction_type_id = $transaction->transaction_type_id;
        if ($status_id == $voided) {
            $time = Carbon::now()->addSeconds(1);
            $transaction_details->voided_time = $transaction->voided_time;
            $transaction_details->voided_by = $transaction->voided_by;
            $transaction_details->voided_local_time = $transaction->voided_local_time;
            $transaction_details->comment = $transaction->comment;
            $transaction_details->created_at = $time;
            $transaction_details->updated_at = $time;
        } else {
            $time = Carbon::now()->addSeconds(2);
            $transaction_details->created_at = $time;
            $transaction_details->updated_at = $time;
        }
        $transaction_details->save();
        Log::channel('revoke-void-transaction')->info(__METHOD__ . "(Line: " . __LINE__ . ") - " . "Status ID: " . $status_id . " for Transaction ID: " . $transaction->id . "inserted successfully.");
    }

    private function _postTransactionIntoAcheck21($transaction, $params)
    {
        $params['amount'] = $transaction->amount + $transaction->tip_amount;
        $params['consumer_id'] = $transaction->consumer_id;

        // Fetch the Status ID
        $failed = getStatus(FAILED);
        $success = getStatus(SUCCESS);

        $history = array(
            'transaction_id' => $transaction->id,
            'transaction_posting' => 1,
            'status_id' => $failed,
        );
        //calling the factory function to create consumer transaction into acheck21
        try {
            Log::channel('revoke-void-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Posting transaction for amount: " . $params['amount']);
            Log::channel('revoke-void-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Posting transaction for client id: " . $params['acheck_account_id']);
            $params['account_id'] = $transaction->account_id;
            if (env('ACHECK_POSTING')) {
                $response = $this->createConsumerTransaction($params);
                $response_decoded = json_decode($response, true);
                Log::channel('revoke-void-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is enabled. Posting in ACHECK21.");
            } else {
                $response_decoded['documentId'] = rand(********, ********);
                Log::channel('revoke-void-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is disabled. Continue with a random number.");
            }
            //check if transaction posted successfully to acheck21
            if (isset($response_decoded['documentId'])) {
                $response_decoded['transaction_time'] = $transaction->transaction_time;
                $response_decoded['transaction_local_time'] = $transaction->local_transaction_time;
                //store transaction data into database
                $this->_createConsumerTransaction($transaction, $response_decoded);
                // store success log into transaction posting history table
                $history['status_id'] = $success;
                Acheck21HistoryTable::create($history);
            }
            Log::channel('revoke-void-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transactions posted into acheck21 successfully.");
        } catch (\Exception $e) {
            Acheck21HistoryTable::create($history);
            Log::channel('revoke-void-transaction')->error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Exception occured during Transaction", [EXCEPTION => $e]);
        }
    }
}
