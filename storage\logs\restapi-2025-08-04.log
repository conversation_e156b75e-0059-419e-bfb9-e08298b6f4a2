 {"@timestamp":"2025-08-04T07:48:02.543800+00:00","V":"V-a51d9a6","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"9343122be1518bcfe78dad282c87cf41","USER_ID":"","LEVEL":"INFO","PATH":"/","METHOD":"GET","PARAM":[],"STATUS":200,"RESPONSE":null,"ET":"9.099"}
 {"@timestamp":"2025-08-04T09:05:40.033654+00:00","V":"V-a51d9a6","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"94455f2c0f150efa98cfae05b91f6716","USER_ID":"578fe7fcfb246aa88009794ec2581edf","LEVEL":"INFO","PATH":"admin/updatestoretimings","METHOD":"POST","PARAM":{"store_ids":"1b3c259c6680438555a4d13c042e8b42,c351fd48f27e45050ff812855c30ca46,822abf2ed51bed78efd0fae79a0d1172,18b90c87e714ffd636ceeeab5a8577c3,0924582cbe2f62d1d0b3095d5a533e3b,f2243a0f4cc69a863899c837c87102fd"},"STATUS":500,"RESPONSE":{"message":"cURL error 60: SSL certificate problem: unable to get local issuer certificate (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://maps.googleapis.com/maps/api/place/findplacefromtext/json?fields=place_id&input=The%20Local%20Charm%20CanPay%203456%20East%20Circle%20Drive%20NE,%20Suite%20103&inputtype=textquery&key=AIzaSyB2rEslfuVTQ20-ZF3VH7ZMhqp6OlAGPHk","exception":"GuzzleHttp\\Exception\\RequestException","file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlFactory.php","line":276,"trace":[{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlFactory.php","line":205,"function":"createRejection","class":"GuzzleHttp\\Handler\\CurlFactory","type":"::"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlFactory.php","line":157,"function":"finishError","class":"GuzzleHttp\\Handler\\CurlFactory","type":"::"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlHandler.php","line":47,"function":"finish","class":"GuzzleHttp\\Handler\\CurlFactory","type":"::"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\Proxy.php","line":28,"function":"__invoke","class":"GuzzleHttp\\Handler\\CurlHandler","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\Proxy.php","line":48,"function":"GuzzleHttp\\Handler\\{closure}","class":"GuzzleHttp\\Handler\\Proxy","type":"::"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\guzzlehttp\\guzzle\\src\\PrepareBodyMiddleware.php","line":35,"function":"GuzzleHttp\\Handler\\{closure}","class":"GuzzleHttp\\Handler\\Proxy","type":"::"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php","line":31,"function":"__invoke","class":"GuzzleHttp\\PrepareBodyMiddleware","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\guzzlehttp\\guzzle\\src\\RedirectMiddleware.php","line":71,"function":"GuzzleHttp\\{closure}","class":"GuzzleHttp\\Middleware","type":"::"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php","line":66,"function":"__invoke","class":"GuzzleHttp\\RedirectMiddleware","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\guzzlehttp\\guzzle\\src\\HandlerStack.php","line":75,"function":"GuzzleHttp\\{closure}","class":"GuzzleHttp\\Middleware","type":"::"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\guzzlehttp\\guzzle\\src\\Client.php","line":333,"function":"__invoke","class":"GuzzleHttp\\HandlerStack","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\guzzlehttp\\guzzle\\src\\Client.php","line":169,"function":"transfer","class":"GuzzleHttp\\Client","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\guzzlehttp\\guzzle\\src\\Client.php","line":189,"function":"requestAsync","class":"GuzzleHttp\\Client","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\guzzlehttp\\guzzle\\src\\ClientTrait.php","line":44,"function":"request","class":"GuzzleHttp\\Client","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\app\\Http\\Clients\\GooglePlacesHttpClient.php","line":81,"function":"get","class":"GuzzleHttp\\Client","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\app\\Console\\Commands\\UpdateStoreTimings.php","line":67,"function":"findplacefromtext","class":"App\\Http\\Clients\\GooglePlacesHttpClient","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\illuminate\\container\\BoundMethod.php","line":36,"function":"handle","class":"App\\Console\\Commands\\UpdateStoreTimings","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\illuminate\\container\\Util.php","line":43,"function":"Illuminate\\Container\\{closure}","class":"Illuminate\\Container\\BoundMethod","type":"::"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\illuminate\\container\\BoundMethod.php","line":95,"function":"unwrapIfClosure","class":"Illuminate\\Container\\Util","type":"::"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\illuminate\\container\\BoundMethod.php","line":35,"function":"callBoundMethod","class":"Illuminate\\Container\\BoundMethod","type":"::"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\illuminate\\container\\Container.php","line":696,"function":"call","class":"Illuminate\\Container\\BoundMethod","type":"::"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\illuminate\\console\\Command.php","line":213,"function":"call","class":"Illuminate\\Container\\Container","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\symfony\\console\\Command\\Command.php","line":279,"function":"execute","class":"Illuminate\\Console\\Command","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\illuminate\\console\\Command.php","line":182,"function":"run","class":"Symfony\\Component\\Console\\Command\\Command","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\symfony\\console\\Application.php","line":1094,"function":"run","class":"Illuminate\\Console\\Command","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\symfony\\console\\Application.php","line":342,"function":"doRunCommand","class":"Symfony\\Component\\Console\\Application","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\symfony\\console\\Application.php","line":193,"function":"doRun","class":"Symfony\\Component\\Console\\Application","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\illuminate\\console\\Application.php","line":166,"function":"run","class":"Symfony\\Component\\Console\\Application","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php","line":216,"function":"call","class":"Illuminate\\Console\\Application","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\illuminate\\support\\Facades\\Facade.php","line":361,"function":"call","class":"Laravel\\Lumen\\Console\\Kernel","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\app\\Http\\Controllers\\MerchantController.php","line":1479,"function":"__callStatic","class":"Illuminate\\Support\\Facades\\Facade","type":"::"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\illuminate\\container\\BoundMethod.php","line":36,"function":"updateStoreTimings","class":"App\\Http\\Controllers\\MerchantController","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\illuminate\\container\\Util.php","line":43,"function":"Illuminate\\Container\\{closure}","class":"Illuminate\\Container\\BoundMethod","type":"::"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\illuminate\\container\\BoundMethod.php","line":95,"function":"unwrapIfClosure","class":"Illuminate\\Container\\Util","type":"::"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\illuminate\\container\\BoundMethod.php","line":35,"function":"callBoundMethod","class":"Illuminate\\Container\\BoundMethod","type":"::"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\illuminate\\container\\Container.php","line":696,"function":"call","class":"Illuminate\\Container\\BoundMethod","type":"::"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php","line":391,"function":"call","class":"Illuminate\\Container\\Container","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php","line":356,"function":"callControllerCallable","class":"Laravel\\Lumen\\Application","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php","line":331,"function":"callLumenController","class":"Laravel\\Lumen\\Application","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php","line":284,"function":"callControllerAction","class":"Laravel\\Lumen\\Application","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php","line":264,"function":"callActionOnArrayBasedRoute","class":"Laravel\\Lumen\\Application","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php","line":48,"function":"Laravel\\Lumen\\Concerns\\{closure}","class":"Laravel\\Lumen\\Application","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\app\\Http\\Middleware\\XssSanitizer.php","line":17,"function":"Laravel\\Lumen\\Routing\\{closure}","class":"Laravel\\Lumen\\Routing\\Pipeline","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\illuminate\\pipeline\\Pipeline.php","line":209,"function":"handle","class":"App\\Http\\Middleware\\XssSanitizer","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php","line":30,"function":"Illuminate\\Pipeline\\{closure}","class":"Illuminate\\Pipeline\\Pipeline","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\app\\Http\\Middleware\\JwtMiddleware.php","line":32,"function":"Laravel\\Lumen\\Routing\\{closure}","class":"Laravel\\Lumen\\Routing\\Pipeline","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\illuminate\\pipeline\\Pipeline.php","line":209,"function":"handle","class":"App\\Http\\Middleware\\JwtMiddleware","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php","line":30,"function":"Illuminate\\Pipeline\\{closure}","class":"Illuminate\\Pipeline\\Pipeline","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\illuminate\\pipeline\\Pipeline.php","line":127,"function":"Laravel\\Lumen\\Routing\\{closure}","class":"Laravel\\Lumen\\Routing\\Pipeline","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php","line":428,"function":"then","class":"Illuminate\\Pipeline\\Pipeline","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php","line":263,"function":"sendThroughPipeline","class":"Laravel\\Lumen\\Application","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php","line":171,"function":"handleFoundRoute","class":"Laravel\\Lumen\\Application","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php","line":48,"function":"Laravel\\Lumen\\Concerns\\{closure}","class":"Laravel\\Lumen\\Application","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\app\\Http\\Middleware\\SecureHeaders.php","line":17,"function":"Laravel\\Lumen\\Routing\\{closure}","class":"Laravel\\Lumen\\Routing\\Pipeline","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\illuminate\\pipeline\\Pipeline.php","line":209,"function":"handle","class":"App\\Http\\Middleware\\SecureHeaders","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php","line":30,"function":"Illuminate\\Pipeline\\{closure}","class":"Illuminate\\Pipeline\\Pipeline","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\app\\Http\\Middleware\\RESTAPILogger.php","line":26,"function":"Laravel\\Lumen\\Routing\\{closure}","class":"Laravel\\Lumen\\Routing\\Pipeline","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\illuminate\\pipeline\\Pipeline.php","line":209,"function":"handle","class":"App\\Http\\Middleware\\RESTAPILogger","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php","line":30,"function":"Illuminate\\Pipeline\\{closure}","class":"Illuminate\\Pipeline\\Pipeline","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\app\\Http\\Middleware\\CorsMiddleware.php","line":27,"function":"Laravel\\Lumen\\Routing\\{closure}","class":"Laravel\\Lumen\\Routing\\Pipeline","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\illuminate\\pipeline\\Pipeline.php","line":209,"function":"handle","class":"App\\Http\\Middleware\\CorsMiddleware","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php","line":30,"function":"Illuminate\\Pipeline\\{closure}","class":"Illuminate\\Pipeline\\Pipeline","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\illuminate\\pipeline\\Pipeline.php","line":127,"function":"Laravel\\Lumen\\Routing\\{closure}","class":"Laravel\\Lumen\\Routing\\Pipeline","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php","line":428,"function":"then","class":"Illuminate\\Pipeline\\Pipeline","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php","line":167,"function":"sendThroughPipeline","class":"Laravel\\Lumen\\Application","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php","line":112,"function":"dispatch","class":"Laravel\\Lumen\\Application","type":"->"},{"file":"C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\public\\index.php","line":36,"function":"run","class":"Laravel\\Lumen\\Application","type":"->"}]},"ET":"10.991"}
 {"@timestamp":"2025-08-04T11:10:34.315085+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"2cb3b17bf903ae2048ae433bc9797a94","USER_ID":"","LEVEL":"INFO","PATH":"merchant/forgotpassword","METHOD":"POST","PARAM":{"email":"<EMAIL>"},"STATUS":599,"RESPONSE":{"code":599,"message":"User not found.","data":null},"ET":"0.747"}
 {"@timestamp":"2025-08-04T11:12:15.838539+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"5f8c01094597f662f85069726919d1c0","USER_ID":"","LEVEL":"INFO","PATH":"merchant/login","METHOD":"POST","PARAM":{"user_type":"Store User","email":"<EMAIL>","terminal_identification_number":null},"STATUS":200,"RESPONSE":{"code":200,"message":"User logged in successfully.","data":{"user_id":"aa5c6efd7c2a216f600daaee1284d641","username":null,"user_identifier":10931,"ach_identifier":null,"email":null,"phone":"","first_name":"TrivikaBliss","middle_name":null,"last_name":null,"contact_person_first_name":"Trivika","contact_person_last_name":"Bliss","contact_person_email":"<EMAIL>","contact_person_phone":"**********","suffix":null,"date_of_birth":null,"is_date_of_birth_update":0,"is_merchant_change_password":0,"allow_merchant_fees_report":0,"allow_merchant_sms_base_payment":0,"street_address":null,"apt_number":null,"city":null,"state":null,"zipcode":null,"contact_person":null,"driving_license":null,"state_issued":null,"ssn_number":null,"status":"5837fda2cf05f99b8bbe6a301cf02372","inactivated_at":null,"previously_marked_as_suspected":0,"login_pin":null,"temp_password":null,"temp_password_expiry_time":null,"pin":null,"added_by":"578fe7fcfb246aa88009794ec2581edf","excess_purchase_amount_privilage":0,"excess_purchase_amount":null,"standard_daily_limit":null,"purchase_power":null,"purchase_power_source":"old_rule","is_algo_based":0,"algo_user_type":"new","last_algo_data_failed":0,"risk_score":null,"disable_automatic_purchase_power":0,"weekly_spending_limit":"0.00","disable_automatic_weekly_spending_limit":0,"active_allow_transaction":0,"active_allow_transaction_time":null,"role_id":"a923a8c3d24855903f2d2c4c34cb03b1","notification_token":null,"existing_user":0,"bank_link_type":0,"forced_manual_purchase_power":0,"admin_driven_bank_link":0,"admin_driven_bank_show_flag":0,"manual_bank_microbilt_error":0,"microbilt_upload_document_show":0,"bank_from_v1":0,"global_radar_review":1,"required_upload_document":0,"receive_daily_transaction_email":0,"show_void_transaction_menu":0,"migrated_at":null,"lockout":0,"refresh_balance_called":0,"merchant_point_used":0,"registration_ref_id":null,"address_updated_at":null,"batch_id":0,"invited_for_reward_wheel":0,"is_reward_wheel_invitation_accepted":0,"award_free_spin_after_invitation_acceptance":0,"from_ecommerce":0,"force_delink":0,"store_id":null,"disable_transaction":0,"bank_relink_skipped_at":null,"consumer_type":"standard","default_cp":0,"created_at":"2025-08-04T11:00:42.000000Z","updated_at":"2025-08-04T11:11:47.000000Z","role_name":"Corporate Parent","token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.*******************************************************************************************************************************.eIXJWhLQz8f2q_uEXL4aNSZswHBJWgKjQrAK_kCgo4tkQwEFHtrE8mJT-E-jWur8lxUNowjRlmy2RzVAfmygjhtDuvWm_40miPeLGu9n0keNViFapAA8piJC_mU-Eo2JTyJgkSgUHBc-LmeR_XHw21I1spAa1-dG1LEuh5BVrW8G-qXuChXQthw7PxWCffvQNFpBzMnMjYbZOUnY4SlFf6ggb2cjo7Fh4iiBrknKKxe2-jEsUU4zaTcz19gWPR-Z-mkcb_5DHvG4mUZazES961cSM0k1_VwczhE7DQ7pzDXwhUeAmTY977CYIeM7RIYfYkpPlF6y3z1_sNwbq4wV7w"}},"ET":"0.476"}
 {"@timestamp":"2025-08-04T11:12:16.543663+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"82d377aadfeb2a18e73832e1bc657ef5","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/getauthorizedterminals","METHOD":"POST","PARAM":{"search":"","status":"Active","order":"created_at","dir":"desc"},"STATUS":200,"RESPONSE":{"code":200,"message":"Terminal List fetched successfully","data":[]},"ET":"0.363"}
 {"@timestamp":"2025-08-04T11:12:17.707587+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"0c484dac7de95c33ac467ccc3a4db8e6","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/getauthorizedstores","METHOD":"GET","PARAM":[],"STATUS":200,"RESPONSE":{"code":200,"message":"All authorised stores fetched successfully.","data":[{"id":"c351fd48f27e45050ff812855c30ca46","merchant_id":"a3a3ab82f7379037aed8c66e79b165e5","petition_id":"","store_id":"Radi","ref_no":null,"ach_merchant_name":null,"retailer":"Radiant Finds CanPay","pos_employee_login":0,"lat":"40.57142639","long":"-74.11155701","timezone_id":"8e62ee6dff5dfd57fa46e1a7b33d133b","address":"338 New Dorp Lane","zip":"10306","contact_no":"**********","email":"<EMAIL>","transaction_type_id":"ab7098be816bc2091bc456c6334a835d","location_type":"Retail","city":"Staten Island","state":"NY","county":"Richmond","website_address":"citivamedical.com","delivery_hub_address":null,"average_monthly_sales":"100000.00","average_ticket_per_sale":150,"pos_provider":"f20ce8c861d928d76de0d27ad49796ce","recreational_retail_terminal_needed":0,"medical_terminal_needed":3,"multi_tablet_charging_stand":0,"hardware_shipped":"To DBA","merchant_daily_velocity_limit":"500.00","status":"aba5887e987117d2ef4c0711dc59d988","sync_disabled":0,"is_ecommerce":0,"is_text_based_payment":0,"text_based_logo_url":null,"ecommerce_admin_driven":0,"is_generic":1,"is_delivery_hub":0,"merchant_short_code":null,"map_place_id":null,"hub_updated_by":null,"created_at":"2025-08-04T09:05:23.000000Z","updated_at":"2025-08-04T09:05:23.000000Z","map_viewable":0},{"id":"1b3c259c6680438555a4d13c042e8b42","merchant_id":"2f582c3b1c67f59712fd58fc86bdab72","petition_id":"","store_id":"Blis","ref_no":null,"ach_merchant_name":null,"retailer":"Urban Bliss CanPay","pos_employee_login":0,"lat":"40.57142639","long":"-74.11155701","timezone_id":"8e62ee6dff5dfd57fa46e1a7b33d133b","address":"3456 East Circle Drive NE, Suite 103","zip":"55906","contact_no":"877-308-3344","email":"<EMAIL>","transaction_type_id":"ab7098be816bc2091bc456c6334a835d","location_type":"Retail","city":"Rochester","state":"MN","county":"Olmstead","website_address":"oneplant.us","delivery_hub_address":null,"average_monthly_sales":"250000.00","average_ticket_per_sale":135,"pos_provider":"2a6e464103f38476711d54568cc13740","recreational_retail_terminal_needed":3,"medical_terminal_needed":0,"multi_tablet_charging_stand":0,"hardware_shipped":"To DBA","merchant_daily_velocity_limit":"500.00","status":"aba5887e987117d2ef4c0711dc59d988","sync_disabled":0,"is_ecommerce":0,"is_text_based_payment":0,"text_based_logo_url":null,"ecommerce_admin_driven":0,"is_generic":1,"is_delivery_hub":0,"merchant_short_code":null,"map_place_id":null,"hub_updated_by":null,"created_at":"2025-08-04T09:05:23.000000Z","updated_at":"2025-08-04T09:05:23.000000Z","map_viewable":0}]},"ET":"0.301"}
 {"@timestamp":"2025-08-04T11:12:26.563878+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"6573e0deb6ad9f2460d2e5060463676a","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/getstoretransactiontypes","METHOD":"POST","PARAM":{"store_id":"1b3c259c6680438555a4d13c042e8b42"},"STATUS":200,"RESPONSE":{"code":200,"message":"Transaction types for store fetched successfully.","data":[]},"ET":"0.287"}
 {"@timestamp":"2025-08-04T11:12:42.604878+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"d7f957b9e075b5746463809ad553161c","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/getauthorizedterminals","METHOD":"POST","PARAM":{"search":"","status":"Active","order":"created_at","dir":"desc"},"STATUS":200,"RESPONSE":{"code":200,"message":"Terminal List fetched successfully","data":[]},"ET":"0.316"}
 {"@timestamp":"2025-08-04T11:12:44.016699+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"4b3ea48e0f80da42110ecdb0395280e3","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/getauthorizedstores","METHOD":"GET","PARAM":[],"STATUS":200,"RESPONSE":{"code":200,"message":"All authorised stores fetched successfully.","data":[{"id":"c351fd48f27e45050ff812855c30ca46","merchant_id":"a3a3ab82f7379037aed8c66e79b165e5","petition_id":"","store_id":"Radi","ref_no":null,"ach_merchant_name":null,"retailer":"Radiant Finds CanPay","pos_employee_login":0,"lat":"40.57142639","long":"-74.11155701","timezone_id":"8e62ee6dff5dfd57fa46e1a7b33d133b","address":"338 New Dorp Lane","zip":"10306","contact_no":"**********","email":"<EMAIL>","transaction_type_id":"ab7098be816bc2091bc456c6334a835d","location_type":"Retail","city":"Staten Island","state":"NY","county":"Richmond","website_address":"citivamedical.com","delivery_hub_address":null,"average_monthly_sales":"100000.00","average_ticket_per_sale":150,"pos_provider":"f20ce8c861d928d76de0d27ad49796ce","recreational_retail_terminal_needed":0,"medical_terminal_needed":3,"multi_tablet_charging_stand":0,"hardware_shipped":"To DBA","merchant_daily_velocity_limit":"500.00","status":"aba5887e987117d2ef4c0711dc59d988","sync_disabled":0,"is_ecommerce":0,"is_text_based_payment":0,"text_based_logo_url":null,"ecommerce_admin_driven":0,"is_generic":1,"is_delivery_hub":0,"merchant_short_code":null,"map_place_id":null,"hub_updated_by":null,"created_at":"2025-08-04T09:05:23.000000Z","updated_at":"2025-08-04T09:05:23.000000Z","map_viewable":0},{"id":"1b3c259c6680438555a4d13c042e8b42","merchant_id":"2f582c3b1c67f59712fd58fc86bdab72","petition_id":"","store_id":"Blis","ref_no":null,"ach_merchant_name":null,"retailer":"Urban Bliss CanPay","pos_employee_login":0,"lat":"40.57142639","long":"-74.11155701","timezone_id":"8e62ee6dff5dfd57fa46e1a7b33d133b","address":"3456 East Circle Drive NE, Suite 103","zip":"55906","contact_no":"877-308-3344","email":"<EMAIL>","transaction_type_id":"ab7098be816bc2091bc456c6334a835d","location_type":"Retail","city":"Rochester","state":"MN","county":"Olmstead","website_address":"oneplant.us","delivery_hub_address":null,"average_monthly_sales":"250000.00","average_ticket_per_sale":135,"pos_provider":"2a6e464103f38476711d54568cc13740","recreational_retail_terminal_needed":3,"medical_terminal_needed":0,"multi_tablet_charging_stand":0,"hardware_shipped":"To DBA","merchant_daily_velocity_limit":"500.00","status":"aba5887e987117d2ef4c0711dc59d988","sync_disabled":0,"is_ecommerce":0,"is_text_based_payment":0,"text_based_logo_url":null,"ecommerce_admin_driven":0,"is_generic":1,"is_delivery_hub":0,"merchant_short_code":null,"map_place_id":null,"hub_updated_by":null,"created_at":"2025-08-04T09:05:23.000000Z","updated_at":"2025-08-04T09:05:23.000000Z","map_viewable":0}]},"ET":"0.288"}
 {"@timestamp":"2025-08-04T11:12:50.432922+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"be4f7198903d22f54d2b68d6a492a004","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/getstoretransactiontypes","METHOD":"POST","PARAM":{"store_id":"1b3c259c6680438555a4d13c042e8b42"},"STATUS":200,"RESPONSE":{"code":200,"message":"Transaction types for store fetched successfully.","data":[]},"ET":"0.290"}
 {"@timestamp":"2025-08-04T11:13:19.765009+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"********************************","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/logout","METHOD":"GET","PARAM":[],"STATUS":200,"RESPONSE":{"code":200,"message":"User logged out successfully.","data":null},"ET":"0.244"}
 {"@timestamp":"2025-08-04T11:13:32.049855+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"5610187815264542b584236f7b450390","USER_ID":"","LEVEL":"INFO","PATH":"merchant/login","METHOD":"POST","PARAM":{"user_type":"Store User","email":"<EMAIL>","terminal_identification_number":null},"STATUS":200,"RESPONSE":{"code":200,"message":"User logged in successfully.","data":{"user_id":"aa5c6efd7c2a216f600daaee1284d641","username":null,"user_identifier":10931,"ach_identifier":null,"email":null,"phone":"","first_name":"TrivikaBliss","middle_name":null,"last_name":null,"contact_person_first_name":"Trivika","contact_person_last_name":"Bliss","contact_person_email":"<EMAIL>","contact_person_phone":"**********","suffix":null,"date_of_birth":null,"is_date_of_birth_update":0,"is_merchant_change_password":0,"allow_merchant_fees_report":0,"allow_merchant_sms_base_payment":0,"street_address":null,"apt_number":null,"city":null,"state":null,"zipcode":null,"contact_person":null,"driving_license":null,"state_issued":null,"ssn_number":null,"status":"5837fda2cf05f99b8bbe6a301cf02372","inactivated_at":null,"previously_marked_as_suspected":0,"login_pin":null,"temp_password":null,"temp_password_expiry_time":null,"pin":null,"added_by":"578fe7fcfb246aa88009794ec2581edf","excess_purchase_amount_privilage":0,"excess_purchase_amount":null,"standard_daily_limit":null,"purchase_power":null,"purchase_power_source":"old_rule","is_algo_based":0,"algo_user_type":"new","last_algo_data_failed":0,"risk_score":null,"disable_automatic_purchase_power":0,"weekly_spending_limit":"0.00","disable_automatic_weekly_spending_limit":0,"active_allow_transaction":0,"active_allow_transaction_time":null,"role_id":"a923a8c3d24855903f2d2c4c34cb03b1","notification_token":null,"existing_user":0,"bank_link_type":0,"forced_manual_purchase_power":0,"admin_driven_bank_link":0,"admin_driven_bank_show_flag":0,"manual_bank_microbilt_error":0,"microbilt_upload_document_show":0,"bank_from_v1":0,"global_radar_review":1,"required_upload_document":0,"receive_daily_transaction_email":0,"show_void_transaction_menu":0,"migrated_at":null,"lockout":0,"refresh_balance_called":0,"merchant_point_used":0,"registration_ref_id":null,"address_updated_at":null,"batch_id":0,"invited_for_reward_wheel":0,"is_reward_wheel_invitation_accepted":0,"award_free_spin_after_invitation_acceptance":0,"from_ecommerce":0,"force_delink":0,"store_id":null,"disable_transaction":0,"bank_relink_skipped_at":null,"consumer_type":"standard","default_cp":0,"created_at":"2025-08-04T11:00:42.000000Z","updated_at":"2025-08-04T11:11:47.000000Z","role_name":"Corporate Parent","token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.*******************************************************************************************************************************.VZE6g03X00-efyMMT9My4t70jjCXKw-Ic6z73cPnmbbp3xKmxfUFVCsVLRceLAYnEH7W1RAErCzxc6RK70CgBIh4aYTruzxuoJgwzhxIp4lMXuGwG53iP59u96rev3y0gt4BMO0_747Y4H3XJX3xYLumEj2Jl2aVhpAnEqmrIPoVsXI9va-E4c3iEr1PgcWYZTTpTy2h1PLnQ8AVczZdKYb7pLBlWeCfNlq6BGu-8p0Gkm9PzgK-AMT0yPJs5mj2eR295sz28v8LWr3s6L2W9TtbAuGAL28PB_1b9iTx7rpaZaWJTDtll69AQKCMw5omW8gudwKJIAJqhlJk6rZihg"}},"ET":"0.368"}
 {"@timestamp":"2025-08-04T11:13:32.607835+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"ee2e9b526c085775630ada5d2b5deeb1","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/getauthorizedterminals","METHOD":"POST","PARAM":{"search":"","status":"Active","order":"created_at","dir":"desc"},"STATUS":200,"RESPONSE":{"code":200,"message":"Terminal List fetched successfully","data":[]},"ET":"0.277"}
 {"@timestamp":"2025-08-04T11:13:35.950729+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"4b42dedbbb26743b6ae866a4d9bc243b","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/getauthorizedstores","METHOD":"GET","PARAM":[],"STATUS":200,"RESPONSE":{"code":200,"message":"All authorised stores fetched successfully.","data":[{"id":"c351fd48f27e45050ff812855c30ca46","merchant_id":"a3a3ab82f7379037aed8c66e79b165e5","petition_id":"","store_id":"Radi","ref_no":null,"ach_merchant_name":null,"retailer":"Radiant Finds CanPay","pos_employee_login":0,"lat":"40.57142639","long":"-74.11155701","timezone_id":"8e62ee6dff5dfd57fa46e1a7b33d133b","address":"338 New Dorp Lane","zip":"10306","contact_no":"**********","email":"<EMAIL>","transaction_type_id":"ab7098be816bc2091bc456c6334a835d","location_type":"Retail","city":"Staten Island","state":"NY","county":"Richmond","website_address":"citivamedical.com","delivery_hub_address":null,"average_monthly_sales":"100000.00","average_ticket_per_sale":150,"pos_provider":"f20ce8c861d928d76de0d27ad49796ce","recreational_retail_terminal_needed":0,"medical_terminal_needed":3,"multi_tablet_charging_stand":0,"hardware_shipped":"To DBA","merchant_daily_velocity_limit":"500.00","status":"aba5887e987117d2ef4c0711dc59d988","sync_disabled":0,"is_ecommerce":0,"is_text_based_payment":0,"text_based_logo_url":null,"ecommerce_admin_driven":0,"is_generic":1,"is_delivery_hub":0,"merchant_short_code":null,"map_place_id":null,"hub_updated_by":null,"created_at":"2025-08-04T09:05:23.000000Z","updated_at":"2025-08-04T09:05:23.000000Z","map_viewable":0},{"id":"1b3c259c6680438555a4d13c042e8b42","merchant_id":"2f582c3b1c67f59712fd58fc86bdab72","petition_id":"","store_id":"Blis","ref_no":null,"ach_merchant_name":null,"retailer":"Urban Bliss CanPay","pos_employee_login":0,"lat":"40.57142639","long":"-74.11155701","timezone_id":"8e62ee6dff5dfd57fa46e1a7b33d133b","address":"3456 East Circle Drive NE, Suite 103","zip":"55906","contact_no":"877-308-3344","email":"<EMAIL>","transaction_type_id":"ab7098be816bc2091bc456c6334a835d","location_type":"Retail","city":"Rochester","state":"MN","county":"Olmstead","website_address":"oneplant.us","delivery_hub_address":null,"average_monthly_sales":"250000.00","average_ticket_per_sale":135,"pos_provider":"2a6e464103f38476711d54568cc13740","recreational_retail_terminal_needed":3,"medical_terminal_needed":0,"multi_tablet_charging_stand":0,"hardware_shipped":"To DBA","merchant_daily_velocity_limit":"500.00","status":"aba5887e987117d2ef4c0711dc59d988","sync_disabled":0,"is_ecommerce":0,"is_text_based_payment":0,"text_based_logo_url":null,"ecommerce_admin_driven":0,"is_generic":1,"is_delivery_hub":0,"merchant_short_code":null,"map_place_id":null,"hub_updated_by":null,"created_at":"2025-08-04T09:05:23.000000Z","updated_at":"2025-08-04T09:05:23.000000Z","map_viewable":0}]},"ET":"0.288"}
 {"@timestamp":"2025-08-04T11:13:43.640799+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"21ce57cfabf49adf11c18f2822358fcc","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/getstoretransactiontypes","METHOD":"POST","PARAM":{"store_id":"c351fd48f27e45050ff812855c30ca46"},"STATUS":200,"RESPONSE":{"code":200,"message":"Transaction types for store fetched successfully.","data":[{"id":"2af0f143dff69e2511ea5ffde5458208","type":"None","label":"N"}]},"ET":"0.292"}
 {"@timestamp":"2025-08-04T11:13:51.951324+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"af2c3063fcc3c410d181e2817f6e819a","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/getauthorizedterminals","METHOD":"POST","PARAM":{"search":"","status":"Active","order":"created_at","dir":"desc"},"STATUS":200,"RESPONSE":{"code":200,"message":"Terminal List fetched successfully","data":[]},"ET":"0.278"}
 {"@timestamp":"2025-08-04T11:13:53.867592+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"b1ea6752f961bb87e2cafe39ed3e90d6","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/getauthorizedstores","METHOD":"GET","PARAM":[],"STATUS":200,"RESPONSE":{"code":200,"message":"All authorised stores fetched successfully.","data":[{"id":"c351fd48f27e45050ff812855c30ca46","merchant_id":"a3a3ab82f7379037aed8c66e79b165e5","petition_id":"","store_id":"Radi","ref_no":null,"ach_merchant_name":null,"retailer":"Radiant Finds CanPay","pos_employee_login":0,"lat":"40.57142639","long":"-74.11155701","timezone_id":"8e62ee6dff5dfd57fa46e1a7b33d133b","address":"338 New Dorp Lane","zip":"10306","contact_no":"**********","email":"<EMAIL>","transaction_type_id":"ab7098be816bc2091bc456c6334a835d","location_type":"Retail","city":"Staten Island","state":"NY","county":"Richmond","website_address":"citivamedical.com","delivery_hub_address":null,"average_monthly_sales":"100000.00","average_ticket_per_sale":150,"pos_provider":"f20ce8c861d928d76de0d27ad49796ce","recreational_retail_terminal_needed":0,"medical_terminal_needed":3,"multi_tablet_charging_stand":0,"hardware_shipped":"To DBA","merchant_daily_velocity_limit":"500.00","status":"aba5887e987117d2ef4c0711dc59d988","sync_disabled":0,"is_ecommerce":0,"is_text_based_payment":0,"text_based_logo_url":null,"ecommerce_admin_driven":0,"is_generic":1,"is_delivery_hub":0,"merchant_short_code":null,"map_place_id":null,"hub_updated_by":null,"created_at":"2025-08-04T09:05:23.000000Z","updated_at":"2025-08-04T09:05:23.000000Z","map_viewable":0},{"id":"1b3c259c6680438555a4d13c042e8b42","merchant_id":"2f582c3b1c67f59712fd58fc86bdab72","petition_id":"","store_id":"Blis","ref_no":null,"ach_merchant_name":null,"retailer":"Urban Bliss CanPay","pos_employee_login":0,"lat":"40.57142639","long":"-74.11155701","timezone_id":"8e62ee6dff5dfd57fa46e1a7b33d133b","address":"3456 East Circle Drive NE, Suite 103","zip":"55906","contact_no":"877-308-3344","email":"<EMAIL>","transaction_type_id":"ab7098be816bc2091bc456c6334a835d","location_type":"Retail","city":"Rochester","state":"MN","county":"Olmstead","website_address":"oneplant.us","delivery_hub_address":null,"average_monthly_sales":"250000.00","average_ticket_per_sale":135,"pos_provider":"2a6e464103f38476711d54568cc13740","recreational_retail_terminal_needed":3,"medical_terminal_needed":0,"multi_tablet_charging_stand":0,"hardware_shipped":"To DBA","merchant_daily_velocity_limit":"500.00","status":"aba5887e987117d2ef4c0711dc59d988","sync_disabled":0,"is_ecommerce":0,"is_text_based_payment":0,"text_based_logo_url":null,"ecommerce_admin_driven":0,"is_generic":1,"is_delivery_hub":0,"merchant_short_code":null,"map_place_id":null,"hub_updated_by":null,"created_at":"2025-08-04T09:05:23.000000Z","updated_at":"2025-08-04T09:05:23.000000Z","map_viewable":0}]},"ET":"0.296"}
 {"@timestamp":"2025-08-04T11:14:02.201193+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"5e53e2b093945d13923fa6653b61bab5","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/getstoretransactiontypes","METHOD":"POST","PARAM":{"store_id":"1b3c259c6680438555a4d13c042e8b42"},"STATUS":200,"RESPONSE":{"code":200,"message":"Transaction types for store fetched successfully.","data":[]},"ET":"0.278"}
 {"@timestamp":"2025-08-04T11:14:11.805053+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"63ec5d75b52d7ba9ddaa56a803311b21","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/getstoretransactiontypes","METHOD":"POST","PARAM":{"store_id":"c351fd48f27e45050ff812855c30ca46"},"STATUS":200,"RESPONSE":{"code":200,"message":"Transaction types for store fetched successfully.","data":[{"id":"2af0f143dff69e2511ea5ffde5458208","type":"None","label":"N"}]},"ET":"0.265"}
 {"@timestamp":"2025-08-04T11:21:33.333496+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"ada324ac917daedcc044513ba868f364","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/getauthorizedterminals","METHOD":"POST","PARAM":{"search":"","status":"Active","order":"created_at","dir":"desc"},"STATUS":200,"RESPONSE":{"code":200,"message":"Terminal List fetched successfully","data":[]},"ET":"0.758"}
 {"@timestamp":"2025-08-04T11:21:34.611387+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"4924da98e52ef692c26c25457be474af","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/getauthorizedstores","METHOD":"GET","PARAM":[],"STATUS":200,"RESPONSE":{"code":200,"message":"All authorised stores fetched successfully.","data":[{"id":"c351fd48f27e45050ff812855c30ca46","merchant_id":"a3a3ab82f7379037aed8c66e79b165e5","petition_id":"","store_id":"Radi","ref_no":null,"ach_merchant_name":null,"retailer":"Radiant Finds CanPay","pos_employee_login":0,"lat":"40.57142639","long":"-74.11155701","timezone_id":"8e62ee6dff5dfd57fa46e1a7b33d133b","address":"338 New Dorp Lane","zip":"10306","contact_no":"**********","email":"<EMAIL>","transaction_type_id":"ab7098be816bc2091bc456c6334a835d","location_type":"Retail","city":"Staten Island","state":"NY","county":"Richmond","website_address":"citivamedical.com","delivery_hub_address":null,"average_monthly_sales":"100000.00","average_ticket_per_sale":150,"pos_provider":"f20ce8c861d928d76de0d27ad49796ce","recreational_retail_terminal_needed":0,"medical_terminal_needed":3,"multi_tablet_charging_stand":0,"hardware_shipped":"To DBA","merchant_daily_velocity_limit":"500.00","status":"aba5887e987117d2ef4c0711dc59d988","sync_disabled":0,"is_ecommerce":0,"is_text_based_payment":0,"text_based_logo_url":null,"ecommerce_admin_driven":0,"is_generic":1,"is_delivery_hub":0,"merchant_short_code":null,"map_place_id":null,"hub_updated_by":null,"created_at":"2025-08-04T09:05:23.000000Z","updated_at":"2025-08-04T09:05:23.000000Z","map_viewable":0},{"id":"1b3c259c6680438555a4d13c042e8b42","merchant_id":"2f582c3b1c67f59712fd58fc86bdab72","petition_id":"","store_id":"Blis","ref_no":null,"ach_merchant_name":null,"retailer":"Urban Bliss CanPay","pos_employee_login":0,"lat":"40.57142639","long":"-74.11155701","timezone_id":"8e62ee6dff5dfd57fa46e1a7b33d133b","address":"3456 East Circle Drive NE, Suite 103","zip":"55906","contact_no":"877-308-3344","email":"<EMAIL>","transaction_type_id":"ab7098be816bc2091bc456c6334a835d","location_type":"Retail","city":"Rochester","state":"MN","county":"Olmstead","website_address":"oneplant.us","delivery_hub_address":null,"average_monthly_sales":"250000.00","average_ticket_per_sale":135,"pos_provider":"2a6e464103f38476711d54568cc13740","recreational_retail_terminal_needed":3,"medical_terminal_needed":0,"multi_tablet_charging_stand":0,"hardware_shipped":"To DBA","merchant_daily_velocity_limit":"500.00","status":"aba5887e987117d2ef4c0711dc59d988","sync_disabled":0,"is_ecommerce":0,"is_text_based_payment":0,"text_based_logo_url":null,"ecommerce_admin_driven":0,"is_generic":1,"is_delivery_hub":0,"merchant_short_code":null,"map_place_id":null,"hub_updated_by":null,"created_at":"2025-08-04T09:05:23.000000Z","updated_at":"2025-08-04T09:05:23.000000Z","map_viewable":0}]},"ET":"0.406"}
 {"@timestamp":"2025-08-04T11:21:39.846447+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"795dcb7f4416b25e670d64f354f28b20","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/getstoretransactiontypes","METHOD":"POST","PARAM":{"store_id":"c351fd48f27e45050ff812855c30ca46"},"STATUS":200,"RESPONSE":{"code":200,"message":"Transaction types for store fetched successfully.","data":[{"id":"2af0f143dff69e2511ea5ffde5458208","type":"None","label":"N"}]},"ET":"0.311"}
 {"@timestamp":"2025-08-04T11:21:54.117126+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"5daf0ce0bcfccf78e31c30a2966de35b","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/getstoretransactiontypes","METHOD":"POST","PARAM":{"store_id":"1b3c259c6680438555a4d13c042e8b42"},"STATUS":200,"RESPONSE":{"code":200,"message":"Transaction types for store fetched successfully.","data":[]},"ET":"0.318"}
 {"@timestamp":"2025-08-04T11:22:00.589213+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"364e4b5bf10c5982ea77b9c11990987e","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/getstoretransactiontypes","METHOD":"POST","PARAM":{"store_id":"c351fd48f27e45050ff812855c30ca46"},"STATUS":200,"RESPONSE":{"code":200,"message":"Transaction types for store fetched successfully.","data":[{"id":"2af0f143dff69e2511ea5ffde5458208","type":"None","label":"N"}]},"ET":"0.336"}
 {"@timestamp":"2025-08-04T11:23:28.397544+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"afb6ca949263460cd51cde44d2fd52ed","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/getstoretransactiontypes","METHOD":"POST","PARAM":{"store_id":"c351fd48f27e45050ff812855c30ca46"},"STATUS":200,"RESPONSE":{"code":200,"message":"Transaction types for store fetched successfully.","data":[{"id":"ab74755ff23dc095c56ae44e6454e791","type":"Retail","label":"RE"}]},"ET":"0.448"}
 {"@timestamp":"2025-08-04T11:23:29.596467+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"755d2172e59dd56ff71fbcc18327f719","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/getstoretransactiontypes","METHOD":"POST","PARAM":{"store_id":"1b3c259c6680438555a4d13c042e8b42"},"STATUS":200,"RESPONSE":{"code":200,"message":"Transaction types for store fetched successfully.","data":[]},"ET":"0.370"}
 {"@timestamp":"2025-08-04T11:23:31.035425+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"7f6a481d3cfea39058495e8006d7e2b9","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/getstoretransactiontypes","METHOD":"POST","PARAM":{"store_id":"c351fd48f27e45050ff812855c30ca46"},"STATUS":200,"RESPONSE":{"code":200,"message":"Transaction types for store fetched successfully.","data":[{"id":"ab74755ff23dc095c56ae44e6454e791","type":"Retail","label":"RE"}]},"ET":"0.338"}
 {"@timestamp":"2025-08-04T11:24:03.912319+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"a5ae025535c8e61eda7604a1d5ddc03b","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/getstoretransactiontypes","METHOD":"POST","PARAM":{"store_id":"1b3c259c6680438555a4d13c042e8b42"},"STATUS":200,"RESPONSE":{"code":200,"message":"Transaction types for store fetched successfully.","data":[]},"ET":"0.366"}
 {"@timestamp":"2025-08-04T11:24:17.886911+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"59da16aec66db04e0b708ae9de8534e0","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/getstoretransactiontypes","METHOD":"POST","PARAM":{"store_id":"c351fd48f27e45050ff812855c30ca46"},"STATUS":200,"RESPONSE":{"code":200,"message":"Transaction types for store fetched successfully.","data":[{"id":"ab74755ff23dc095c56ae44e6454e791","type":"Retail","label":"RE"}]},"ET":"0.394"}
 {"@timestamp":"2025-08-04T11:24:19.828652+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"8aa91da7d868ead1dbf8c65690c02aae","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/getstoretransactiontypes","METHOD":"POST","PARAM":{"store_id":"1b3c259c6680438555a4d13c042e8b42"},"STATUS":200,"RESPONSE":{"code":200,"message":"Transaction types for store fetched successfully.","data":[{"id":"ab74755ff23dc095c56ae44e6454e791","type":"Retail","label":"RE"}]},"ET":"0.372"}
 {"@timestamp":"2025-08-04T11:24:26.557545+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"08c2d4cd15d9f207739aebaf762abb36","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/getauthorizedterminals","METHOD":"POST","PARAM":{"search":"","status":"Active","order":"created_at","dir":"desc"},"STATUS":200,"RESPONSE":{"code":200,"message":"Terminal List fetched successfully","data":[]},"ET":"0.384"}
 {"@timestamp":"2025-08-04T11:24:29.805873+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"adbb8af7ef62a6c95c5942b879f4c902","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/getauthorizedstores","METHOD":"GET","PARAM":[],"STATUS":200,"RESPONSE":{"code":200,"message":"All authorised stores fetched successfully.","data":[{"id":"c351fd48f27e45050ff812855c30ca46","merchant_id":"a3a3ab82f7379037aed8c66e79b165e5","petition_id":"","store_id":"Radi","ref_no":null,"ach_merchant_name":null,"retailer":"Radiant Finds CanPay","pos_employee_login":0,"lat":"40.57142639","long":"-74.11155701","timezone_id":"8e62ee6dff5dfd57fa46e1a7b33d133b","address":"338 New Dorp Lane","zip":"10306","contact_no":"**********","email":"<EMAIL>","transaction_type_id":"ab7098be816bc2091bc456c6334a835d","location_type":"Retail","city":"Staten Island","state":"NY","county":"Richmond","website_address":"citivamedical.com","delivery_hub_address":null,"average_monthly_sales":"100000.00","average_ticket_per_sale":150,"pos_provider":"f20ce8c861d928d76de0d27ad49796ce","recreational_retail_terminal_needed":0,"medical_terminal_needed":3,"multi_tablet_charging_stand":0,"hardware_shipped":"To DBA","merchant_daily_velocity_limit":"500.00","status":"aba5887e987117d2ef4c0711dc59d988","sync_disabled":0,"is_ecommerce":0,"is_text_based_payment":0,"text_based_logo_url":null,"ecommerce_admin_driven":0,"is_generic":1,"is_delivery_hub":0,"merchant_short_code":null,"map_place_id":null,"hub_updated_by":null,"created_at":"2025-08-04T09:05:23.000000Z","updated_at":"2025-08-04T09:05:23.000000Z","map_viewable":0},{"id":"1b3c259c6680438555a4d13c042e8b42","merchant_id":"2f582c3b1c67f59712fd58fc86bdab72","petition_id":"","store_id":"Blis","ref_no":null,"ach_merchant_name":null,"retailer":"Urban Bliss CanPay","pos_employee_login":0,"lat":"40.57142639","long":"-74.11155701","timezone_id":"8e62ee6dff5dfd57fa46e1a7b33d133b","address":"3456 East Circle Drive NE, Suite 103","zip":"55906","contact_no":"877-308-3344","email":"<EMAIL>","transaction_type_id":"ab7098be816bc2091bc456c6334a835d","location_type":"Retail","city":"Rochester","state":"MN","county":"Olmstead","website_address":"oneplant.us","delivery_hub_address":null,"average_monthly_sales":"250000.00","average_ticket_per_sale":135,"pos_provider":"2a6e464103f38476711d54568cc13740","recreational_retail_terminal_needed":3,"medical_terminal_needed":0,"multi_tablet_charging_stand":0,"hardware_shipped":"To DBA","merchant_daily_velocity_limit":"500.00","status":"aba5887e987117d2ef4c0711dc59d988","sync_disabled":0,"is_ecommerce":0,"is_text_based_payment":0,"text_based_logo_url":null,"ecommerce_admin_driven":0,"is_generic":1,"is_delivery_hub":0,"merchant_short_code":null,"map_place_id":null,"hub_updated_by":null,"created_at":"2025-08-04T09:05:23.000000Z","updated_at":"2025-08-04T09:05:23.000000Z","map_viewable":0}]},"ET":"0.419"}
 {"@timestamp":"2025-08-04T11:24:37.074926+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"ba898052fda775595895995c39afc040","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/getstoretransactiontypes","METHOD":"POST","PARAM":{"store_id":"c351fd48f27e45050ff812855c30ca46"},"STATUS":200,"RESPONSE":{"code":200,"message":"Transaction types for store fetched successfully.","data":[{"id":"ab74755ff23dc095c56ae44e6454e791","type":"Retail","label":"RE"}]},"ET":"0.374"}
 {"@timestamp":"2025-08-04T11:24:38.887519+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"3c391dfd51a93c062eb4535896fb4ecf","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/getstoretransactiontypes","METHOD":"POST","PARAM":{"store_id":"1b3c259c6680438555a4d13c042e8b42"},"STATUS":200,"RESPONSE":{"code":200,"message":"Transaction types for store fetched successfully.","data":[{"id":"ab74755ff23dc095c56ae44e6454e791","type":"Retail","label":"RE"}]},"ET":"0.349"}
 {"@timestamp":"2025-08-04T11:24:41.689828+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"7199936cad9fdc5f70296b328f257610","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/getauthorizedterminals","METHOD":"POST","PARAM":{"search":"","status":"Active","order":"created_at","dir":"desc"},"STATUS":200,"RESPONSE":{"code":200,"message":"Terminal List fetched successfully","data":[]},"ET":"0.383"}
 {"@timestamp":"2025-08-04T11:24:49.834978+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"9b528fc12fa2b914f74a6cc8a626ce2e","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/getauthorizedstores","METHOD":"GET","PARAM":[],"STATUS":200,"RESPONSE":{"code":200,"message":"All authorised stores fetched successfully.","data":[{"id":"c351fd48f27e45050ff812855c30ca46","merchant_id":"a3a3ab82f7379037aed8c66e79b165e5","petition_id":"","store_id":"Radi","ref_no":null,"ach_merchant_name":null,"retailer":"Radiant Finds CanPay","pos_employee_login":0,"lat":"40.57142639","long":"-74.11155701","timezone_id":"8e62ee6dff5dfd57fa46e1a7b33d133b","address":"338 New Dorp Lane","zip":"10306","contact_no":"**********","email":"<EMAIL>","transaction_type_id":"ab7098be816bc2091bc456c6334a835d","location_type":"Retail","city":"Staten Island","state":"NY","county":"Richmond","website_address":"citivamedical.com","delivery_hub_address":null,"average_monthly_sales":"100000.00","average_ticket_per_sale":150,"pos_provider":"f20ce8c861d928d76de0d27ad49796ce","recreational_retail_terminal_needed":0,"medical_terminal_needed":3,"multi_tablet_charging_stand":0,"hardware_shipped":"To DBA","merchant_daily_velocity_limit":"500.00","status":"aba5887e987117d2ef4c0711dc59d988","sync_disabled":0,"is_ecommerce":0,"is_text_based_payment":0,"text_based_logo_url":null,"ecommerce_admin_driven":0,"is_generic":1,"is_delivery_hub":0,"merchant_short_code":null,"map_place_id":null,"hub_updated_by":null,"created_at":"2025-08-04T09:05:23.000000Z","updated_at":"2025-08-04T09:05:23.000000Z","map_viewable":0},{"id":"1b3c259c6680438555a4d13c042e8b42","merchant_id":"2f582c3b1c67f59712fd58fc86bdab72","petition_id":"","store_id":"Blis","ref_no":null,"ach_merchant_name":null,"retailer":"Urban Bliss CanPay","pos_employee_login":0,"lat":"40.57142639","long":"-74.11155701","timezone_id":"8e62ee6dff5dfd57fa46e1a7b33d133b","address":"3456 East Circle Drive NE, Suite 103","zip":"55906","contact_no":"877-308-3344","email":"<EMAIL>","transaction_type_id":"ab7098be816bc2091bc456c6334a835d","location_type":"Retail","city":"Rochester","state":"MN","county":"Olmstead","website_address":"oneplant.us","delivery_hub_address":null,"average_monthly_sales":"250000.00","average_ticket_per_sale":135,"pos_provider":"2a6e464103f38476711d54568cc13740","recreational_retail_terminal_needed":3,"medical_terminal_needed":0,"multi_tablet_charging_stand":0,"hardware_shipped":"To DBA","merchant_daily_velocity_limit":"500.00","status":"aba5887e987117d2ef4c0711dc59d988","sync_disabled":0,"is_ecommerce":0,"is_text_based_payment":0,"text_based_logo_url":null,"ecommerce_admin_driven":0,"is_generic":1,"is_delivery_hub":0,"merchant_short_code":null,"map_place_id":null,"hub_updated_by":null,"created_at":"2025-08-04T09:05:23.000000Z","updated_at":"2025-08-04T09:05:23.000000Z","map_viewable":0}]},"ET":"0.382"}
 {"@timestamp":"2025-08-04T11:24:55.003398+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"5fab61a18c6c5745e485a395b49574fb","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/getstoretransactiontypes","METHOD":"POST","PARAM":{"store_id":"c351fd48f27e45050ff812855c30ca46"},"STATUS":200,"RESPONSE":{"code":200,"message":"Transaction types for store fetched successfully.","data":[{"id":"ab74755ff23dc095c56ae44e6454e791","type":"Retail","label":"RE"}]},"ET":"0.327"}
 {"@timestamp":"2025-08-04T11:25:03.376146+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"299e07550b359cab33c754b08ee9d6c5","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/setupterminal","METHOD":"POST","PARAM":{"terminal_name":"ter00001","retailer":{"id":"c351fd48f27e45050ff812855c30ca46","retailer":"Radiant Finds CanPay"},"camera_option":"front","tip_allowed":0,"store_id":"c351fd48f27e45050ff812855c30ca46","transaction_type_id":"ab74755ff23dc095c56ae44e6454e791"},"STATUS":599,"RESPONSE":{"code":599,"message":"Failed to create terminal.","data":null},"ET":"1.578"}
 {"@timestamp":"2025-08-04T11:25:11.098084+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"2d27d51c4b96a792aec3106cb95bc5df","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/setupterminal","METHOD":"POST","PARAM":{"terminal_name":"ter00001","retailer":{"id":"c351fd48f27e45050ff812855c30ca46","retailer":"Radiant Finds CanPay"},"camera_option":"front","tip_allowed":0,"store_id":"c351fd48f27e45050ff812855c30ca46","transaction_type_id":"ab74755ff23dc095c56ae44e6454e791"},"STATUS":598,"RESPONSE":{"code":598,"message":"This Terminal ID is already in use. Please choose a different ID.","data":null},"ET":"0.334"}
 {"@timestamp":"2025-08-04T11:25:27.750805+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"63e1e446fd4c63cb9d75636618ac650e","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/setupterminal","METHOD":"POST","PARAM":{"terminal_name":"Medic001","retailer":{"id":"c351fd48f27e45050ff812855c30ca46","retailer":"Radiant Finds CanPay"},"camera_option":"front","tip_allowed":0,"store_id":"c351fd48f27e45050ff812855c30ca46","transaction_type_id":"ab74755ff23dc095c56ae44e6454e791"},"STATUS":599,"RESPONSE":{"code":599,"message":"Failed to create terminal.","data":null},"ET":"0.521"}
 {"@timestamp":"2025-08-04T11:25:42.867206+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"0f178da575b78be7a72f91ca2a095827","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/setupterminal","METHOD":"POST","PARAM":{"terminal_name":"SOU00001","retailer":{"id":"c351fd48f27e45050ff812855c30ca46","retailer":"Radiant Finds CanPay"},"camera_option":"front","tip_allowed":0,"store_id":"c351fd48f27e45050ff812855c30ca46","transaction_type_id":"ab74755ff23dc095c56ae44e6454e791"},"STATUS":599,"RESPONSE":{"code":599,"message":"Failed to create terminal.","data":null},"ET":"0.513"}
 {"@timestamp":"2025-08-04T11:27:39.989668+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"85dbb4a5230c424ccd820fd3b68912ec","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/setupterminal","METHOD":"POST","PARAM":{"terminal_name":"SOU00001","retailer":{"id":"c351fd48f27e45050ff812855c30ca46","retailer":"Radiant Finds CanPay"},"camera_option":"front","tip_allowed":0,"store_id":"c351fd48f27e45050ff812855c30ca46","transaction_type_id":"ab74755ff23dc095c56ae44e6454e791"},"STATUS":598,"RESPONSE":{"code":598,"message":"This Terminal ID is already in use. Please choose a different ID.","data":null},"ET":"0.418"}
 {"@timestamp":"2025-08-04T11:27:58.590305+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"522c96b7fc8b43086573056416096831","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/setupterminal","METHOD":"POST","PARAM":{"terminal_name":"deep0001","retailer":{"id":"c351fd48f27e45050ff812855c30ca46","retailer":"Radiant Finds CanPay"},"camera_option":"front","tip_allowed":0,"store_id":"c351fd48f27e45050ff812855c30ca46","transaction_type_id":"ab74755ff23dc095c56ae44e6454e791"},"STATUS":200,"RESPONSE":{"code":200,"message":"Terminal  created successfully.","data":{"id":"290f817c6983dfc71ef51ade191a6cfa","merchant_store_id":"c351fd48f27e45050ff812855c30ca46","terminal_name":"deep0001","transaction_type_id":"ab74755ff23dc095c56ae44e6454e791","unique_identification_id":"fa754f41e3e2976a8836a0f611cfc9db","tip_allowed":"0","added_by":"aa5c6efd7c2a216f600daaee1284d641","camera_option":"front","status":"aba5887e987117d2ef4c0711dc59d988","updated_at":"2025-08-04T11:27:55.000000Z","created_at":"2025-08-04T11:27:55.000000Z"}},"ET":"3.223"}
 {"@timestamp":"2025-08-04T11:28:02.266068+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"6cd63a3164c85e82c6a17c6ca302ca8c","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/getauthorizedterminals","METHOD":"POST","PARAM":{"search":"","status":"Active","order":"created_at","dir":"desc"},"STATUS":200,"RESPONSE":{"code":200,"message":"Terminal List fetched successfully","data":[{"terminal_name":"deep0001","transaction_type_id":"ab74755ff23dc095c56ae44e6454e791","transaction_type":"Retail","store_id":"c351fd48f27e45050ff812855c30ca46","store_name":"Radiant Finds CanPay","edit":"290f817c6983dfc71ef51ade191a6cfa","created_at":"08-04-2025 11:27 AM","status_name":"Active","status":"aba5887e987117d2ef4c0711dc59d988","unique_identification_id":"fa754f41e3e2976a8836a0f611cfc9db"},{"terminal_name":"SOU00001","transaction_type_id":"ab74755ff23dc095c56ae44e6454e791","transaction_type":"Retail","store_id":"c351fd48f27e45050ff812855c30ca46","store_name":"Radiant Finds CanPay","edit":"acbb91a44ee8e1a3cebfac8aec7ffde9","created_at":"08-04-2025 11:25 AM","status_name":"Active","status":"aba5887e987117d2ef4c0711dc59d988","unique_identification_id":"1c7572202dee0856338779e78529b1df"},{"terminal_name":"Medic001","transaction_type_id":"ab74755ff23dc095c56ae44e6454e791","transaction_type":"Retail","store_id":"c351fd48f27e45050ff812855c30ca46","store_name":"Radiant Finds CanPay","edit":"bde686b48405515ebb2aeace153155c8","created_at":"08-04-2025 11:25 AM","status_name":"Active","status":"aba5887e987117d2ef4c0711dc59d988","unique_identification_id":"1a23e6e35e2dc0815dad4798bb2777c4"},{"terminal_name":"ter00001","transaction_type_id":"ab74755ff23dc095c56ae44e6454e791","transaction_type":"Retail","store_id":"c351fd48f27e45050ff812855c30ca46","store_name":"Radiant Finds CanPay","edit":"77950d4123b3afed95e1134a7cdbfe51","created_at":"08-04-2025 11:25 AM","status_name":"Active","status":"aba5887e987117d2ef4c0711dc59d988","unique_identification_id":"1a20d02bf82d9e7433782f289ddcebf0"}]},"ET":"0.522"}
 {"@timestamp":"2025-08-04T11:28:07.416473+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"aec907728b76e3cb330c986bd2c23cd7","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/getauthorizedstores","METHOD":"GET","PARAM":[],"STATUS":200,"RESPONSE":{"code":200,"message":"All authorised stores fetched successfully.","data":[{"id":"c351fd48f27e45050ff812855c30ca46","merchant_id":"a3a3ab82f7379037aed8c66e79b165e5","petition_id":"","store_id":"Radi","ref_no":null,"ach_merchant_name":null,"retailer":"Radiant Finds CanPay","pos_employee_login":0,"lat":"40.57142639","long":"-74.11155701","timezone_id":"8e62ee6dff5dfd57fa46e1a7b33d133b","address":"338 New Dorp Lane","zip":"10306","contact_no":"**********","email":"<EMAIL>","transaction_type_id":"ab7098be816bc2091bc456c6334a835d","location_type":"Retail","city":"Staten Island","state":"NY","county":"Richmond","website_address":"citivamedical.com","delivery_hub_address":null,"average_monthly_sales":"100000.00","average_ticket_per_sale":150,"pos_provider":"f20ce8c861d928d76de0d27ad49796ce","recreational_retail_terminal_needed":0,"medical_terminal_needed":3,"multi_tablet_charging_stand":0,"hardware_shipped":"To DBA","merchant_daily_velocity_limit":"500.00","status":"aba5887e987117d2ef4c0711dc59d988","sync_disabled":0,"is_ecommerce":0,"is_text_based_payment":0,"text_based_logo_url":null,"ecommerce_admin_driven":0,"is_generic":1,"is_delivery_hub":0,"merchant_short_code":null,"map_place_id":null,"hub_updated_by":null,"created_at":"2025-08-04T09:05:23.000000Z","updated_at":"2025-08-04T09:05:23.000000Z","map_viewable":0},{"id":"1b3c259c6680438555a4d13c042e8b42","merchant_id":"2f582c3b1c67f59712fd58fc86bdab72","petition_id":"","store_id":"Blis","ref_no":null,"ach_merchant_name":null,"retailer":"Urban Bliss CanPay","pos_employee_login":0,"lat":"40.57142639","long":"-74.11155701","timezone_id":"8e62ee6dff5dfd57fa46e1a7b33d133b","address":"3456 East Circle Drive NE, Suite 103","zip":"55906","contact_no":"877-308-3344","email":"<EMAIL>","transaction_type_id":"ab7098be816bc2091bc456c6334a835d","location_type":"Retail","city":"Rochester","state":"MN","county":"Olmstead","website_address":"oneplant.us","delivery_hub_address":null,"average_monthly_sales":"250000.00","average_ticket_per_sale":135,"pos_provider":"2a6e464103f38476711d54568cc13740","recreational_retail_terminal_needed":3,"medical_terminal_needed":0,"multi_tablet_charging_stand":0,"hardware_shipped":"To DBA","merchant_daily_velocity_limit":"500.00","status":"aba5887e987117d2ef4c0711dc59d988","sync_disabled":0,"is_ecommerce":0,"is_text_based_payment":0,"text_based_logo_url":null,"ecommerce_admin_driven":0,"is_generic":1,"is_delivery_hub":0,"merchant_short_code":null,"map_place_id":null,"hub_updated_by":null,"created_at":"2025-08-04T09:05:23.000000Z","updated_at":"2025-08-04T09:05:23.000000Z","map_viewable":0}]},"ET":"0.358"}
 {"@timestamp":"2025-08-04T11:28:07.830289+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"cd4b5ff5bb580011efa10ff357e39296","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/getstoretransactiontypes","METHOD":"POST","PARAM":{"store_id":"c351fd48f27e45050ff812855c30ca46"},"STATUS":200,"RESPONSE":{"code":200,"message":"Transaction types for store fetched successfully.","data":[{"id":"ab74755ff23dc095c56ae44e6454e791","type":"Retail","label":"RE"}]},"ET":"0.399"}
 {"@timestamp":"2025-08-04T11:28:10.943368+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"303516e41bd96dc42fca09c99b3fdd31","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/updateterminal","METHOD":"POST","PARAM":{"terminal_name":"SOU00001","transaction_type_id":"ab74755ff23dc095c56ae44e6454e791","transaction_type":"Retail","store_id":"c351fd48f27e45050ff812855c30ca46","store_name":"Radiant Finds CanPay","edit":"acbb91a44ee8e1a3cebfac8aec7ffde9","created_at":"08-04-2025 11:25 AM","status_name":"Inactive","status":"aba5887e987117d2ef4c0711dc59d988","unique_identification_id":"1c7572202dee0856338779e78529b1df","id":"acbb91a44ee8e1a3cebfac8aec7ffde9","terminal_unique_id":"1c7572202dee0856338779e78529b1df"},"STATUS":200,"RESPONSE":{"code":200,"message":"Terminal updated successfully.","data":{"id":"acbb91a44ee8e1a3cebfac8aec7ffde9","merchant_store_id":"c351fd48f27e45050ff812855c30ca46","terminal_name":"SOU00001","transaction_type_id":"ab74755ff23dc095c56ae44e6454e791","unique_identification_id":"1c7572202dee0856338779e78529b1df","tip_allowed":0,"camera_option":"front","status":"8cb2bea34db1ee4fc8c9f0088e5e0dcf","added_by":"aa5c6efd7c2a216f600daaee1284d641","is_web":0,"created_at":"2025-08-04T11:25:42.000000Z","updated_at":"2025-08-04T11:28:10.000000Z"}},"ET":"0.455"}
 {"@timestamp":"2025-08-04T11:28:12.656704+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"e94555df234e16c2ba6b9f42b362256d","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/getauthorizedterminals","METHOD":"POST","PARAM":{"search":"","status":"Active","order":"created_at","dir":"desc"},"STATUS":200,"RESPONSE":{"code":200,"message":"Terminal List fetched successfully","data":[{"terminal_name":"deep0001","transaction_type_id":"ab74755ff23dc095c56ae44e6454e791","transaction_type":"Retail","store_id":"c351fd48f27e45050ff812855c30ca46","store_name":"Radiant Finds CanPay","edit":"290f817c6983dfc71ef51ade191a6cfa","created_at":"08-04-2025 11:27 AM","status_name":"Active","status":"aba5887e987117d2ef4c0711dc59d988","unique_identification_id":"fa754f41e3e2976a8836a0f611cfc9db"},{"terminal_name":"Medic001","transaction_type_id":"ab74755ff23dc095c56ae44e6454e791","transaction_type":"Retail","store_id":"c351fd48f27e45050ff812855c30ca46","store_name":"Radiant Finds CanPay","edit":"bde686b48405515ebb2aeace153155c8","created_at":"08-04-2025 11:25 AM","status_name":"Active","status":"aba5887e987117d2ef4c0711dc59d988","unique_identification_id":"1a23e6e35e2dc0815dad4798bb2777c4"},{"terminal_name":"ter00001","transaction_type_id":"ab74755ff23dc095c56ae44e6454e791","transaction_type":"Retail","store_id":"c351fd48f27e45050ff812855c30ca46","store_name":"Radiant Finds CanPay","edit":"77950d4123b3afed95e1134a7cdbfe51","created_at":"08-04-2025 11:25 AM","status_name":"Active","status":"aba5887e987117d2ef4c0711dc59d988","unique_identification_id":"1a20d02bf82d9e7433782f289ddcebf0"}]},"ET":"0.475"}
 {"@timestamp":"2025-08-04T11:28:15.096281+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"494fdb53d4f5e0a555eb10808f2a74d4","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/getauthorizedstores","METHOD":"GET","PARAM":[],"STATUS":200,"RESPONSE":{"code":200,"message":"All authorised stores fetched successfully.","data":[{"id":"c351fd48f27e45050ff812855c30ca46","merchant_id":"a3a3ab82f7379037aed8c66e79b165e5","petition_id":"","store_id":"Radi","ref_no":null,"ach_merchant_name":null,"retailer":"Radiant Finds CanPay","pos_employee_login":0,"lat":"40.57142639","long":"-74.11155701","timezone_id":"8e62ee6dff5dfd57fa46e1a7b33d133b","address":"338 New Dorp Lane","zip":"10306","contact_no":"**********","email":"<EMAIL>","transaction_type_id":"ab7098be816bc2091bc456c6334a835d","location_type":"Retail","city":"Staten Island","state":"NY","county":"Richmond","website_address":"citivamedical.com","delivery_hub_address":null,"average_monthly_sales":"100000.00","average_ticket_per_sale":150,"pos_provider":"f20ce8c861d928d76de0d27ad49796ce","recreational_retail_terminal_needed":0,"medical_terminal_needed":3,"multi_tablet_charging_stand":0,"hardware_shipped":"To DBA","merchant_daily_velocity_limit":"500.00","status":"aba5887e987117d2ef4c0711dc59d988","sync_disabled":0,"is_ecommerce":0,"is_text_based_payment":0,"text_based_logo_url":null,"ecommerce_admin_driven":0,"is_generic":1,"is_delivery_hub":0,"merchant_short_code":null,"map_place_id":null,"hub_updated_by":null,"created_at":"2025-08-04T09:05:23.000000Z","updated_at":"2025-08-04T09:05:23.000000Z","map_viewable":0},{"id":"1b3c259c6680438555a4d13c042e8b42","merchant_id":"2f582c3b1c67f59712fd58fc86bdab72","petition_id":"","store_id":"Blis","ref_no":null,"ach_merchant_name":null,"retailer":"Urban Bliss CanPay","pos_employee_login":0,"lat":"40.57142639","long":"-74.11155701","timezone_id":"8e62ee6dff5dfd57fa46e1a7b33d133b","address":"3456 East Circle Drive NE, Suite 103","zip":"55906","contact_no":"877-308-3344","email":"<EMAIL>","transaction_type_id":"ab7098be816bc2091bc456c6334a835d","location_type":"Retail","city":"Rochester","state":"MN","county":"Olmstead","website_address":"oneplant.us","delivery_hub_address":null,"average_monthly_sales":"250000.00","average_ticket_per_sale":135,"pos_provider":"2a6e464103f38476711d54568cc13740","recreational_retail_terminal_needed":3,"medical_terminal_needed":0,"multi_tablet_charging_stand":0,"hardware_shipped":"To DBA","merchant_daily_velocity_limit":"500.00","status":"aba5887e987117d2ef4c0711dc59d988","sync_disabled":0,"is_ecommerce":0,"is_text_based_payment":0,"text_based_logo_url":null,"ecommerce_admin_driven":0,"is_generic":1,"is_delivery_hub":0,"merchant_short_code":null,"map_place_id":null,"hub_updated_by":null,"created_at":"2025-08-04T09:05:23.000000Z","updated_at":"2025-08-04T09:05:23.000000Z","map_viewable":0}]},"ET":"0.429"}
 {"@timestamp":"2025-08-04T11:28:15.491540+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"2e62748dfe5c387ab17611f6b6dc64e9","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/getstoretransactiontypes","METHOD":"POST","PARAM":{"store_id":"c351fd48f27e45050ff812855c30ca46"},"STATUS":200,"RESPONSE":{"code":200,"message":"Transaction types for store fetched successfully.","data":[{"id":"ab74755ff23dc095c56ae44e6454e791","type":"Retail","label":"RE"}]},"ET":"0.381"}
 {"@timestamp":"2025-08-04T11:28:16.316981+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"2202b0be53ccf9569a1a071aeea3f8fc","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/updateterminal","METHOD":"POST","PARAM":{"terminal_name":"Medic001","transaction_type_id":"ab74755ff23dc095c56ae44e6454e791","transaction_type":"Retail","store_id":"c351fd48f27e45050ff812855c30ca46","store_name":"Radiant Finds CanPay","edit":"bde686b48405515ebb2aeace153155c8","created_at":"08-04-2025 11:25 AM","status_name":"Inactive","status":"aba5887e987117d2ef4c0711dc59d988","unique_identification_id":"1a23e6e35e2dc0815dad4798bb2777c4","id":"bde686b48405515ebb2aeace153155c8","terminal_unique_id":"1a23e6e35e2dc0815dad4798bb2777c4"},"STATUS":200,"RESPONSE":{"code":200,"message":"Terminal updated successfully.","data":{"id":"bde686b48405515ebb2aeace153155c8","merchant_store_id":"c351fd48f27e45050ff812855c30ca46","terminal_name":"Medic001","transaction_type_id":"ab74755ff23dc095c56ae44e6454e791","unique_identification_id":"1a23e6e35e2dc0815dad4798bb2777c4","tip_allowed":0,"camera_option":"front","status":"8cb2bea34db1ee4fc8c9f0088e5e0dcf","added_by":"aa5c6efd7c2a216f600daaee1284d641","is_web":0,"created_at":"2025-08-04T11:25:27.000000Z","updated_at":"2025-08-04T11:28:16.000000Z"}},"ET":"0.411"}
 {"@timestamp":"2025-08-04T11:28:17.989430+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"6c1c236287f6ecba81184f873ae7bba6","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/getauthorizedterminals","METHOD":"POST","PARAM":{"search":"","status":"Active","order":"created_at","dir":"desc"},"STATUS":200,"RESPONSE":{"code":200,"message":"Terminal List fetched successfully","data":[{"terminal_name":"deep0001","transaction_type_id":"ab74755ff23dc095c56ae44e6454e791","transaction_type":"Retail","store_id":"c351fd48f27e45050ff812855c30ca46","store_name":"Radiant Finds CanPay","edit":"290f817c6983dfc71ef51ade191a6cfa","created_at":"08-04-2025 11:27 AM","status_name":"Active","status":"aba5887e987117d2ef4c0711dc59d988","unique_identification_id":"fa754f41e3e2976a8836a0f611cfc9db"},{"terminal_name":"ter00001","transaction_type_id":"ab74755ff23dc095c56ae44e6454e791","transaction_type":"Retail","store_id":"c351fd48f27e45050ff812855c30ca46","store_name":"Radiant Finds CanPay","edit":"77950d4123b3afed95e1134a7cdbfe51","created_at":"08-04-2025 11:25 AM","status_name":"Active","status":"aba5887e987117d2ef4c0711dc59d988","unique_identification_id":"1a20d02bf82d9e7433782f289ddcebf0"}]},"ET":"0.444"}
 {"@timestamp":"2025-08-04T11:28:22.185276+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"97450adfb4b0594f62b7870010f524df","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/getauthorizedstores","METHOD":"GET","PARAM":[],"STATUS":200,"RESPONSE":{"code":200,"message":"All authorised stores fetched successfully.","data":[{"id":"c351fd48f27e45050ff812855c30ca46","merchant_id":"a3a3ab82f7379037aed8c66e79b165e5","petition_id":"","store_id":"Radi","ref_no":null,"ach_merchant_name":null,"retailer":"Radiant Finds CanPay","pos_employee_login":0,"lat":"40.57142639","long":"-74.11155701","timezone_id":"8e62ee6dff5dfd57fa46e1a7b33d133b","address":"338 New Dorp Lane","zip":"10306","contact_no":"**********","email":"<EMAIL>","transaction_type_id":"ab7098be816bc2091bc456c6334a835d","location_type":"Retail","city":"Staten Island","state":"NY","county":"Richmond","website_address":"citivamedical.com","delivery_hub_address":null,"average_monthly_sales":"100000.00","average_ticket_per_sale":150,"pos_provider":"f20ce8c861d928d76de0d27ad49796ce","recreational_retail_terminal_needed":0,"medical_terminal_needed":3,"multi_tablet_charging_stand":0,"hardware_shipped":"To DBA","merchant_daily_velocity_limit":"500.00","status":"aba5887e987117d2ef4c0711dc59d988","sync_disabled":0,"is_ecommerce":0,"is_text_based_payment":0,"text_based_logo_url":null,"ecommerce_admin_driven":0,"is_generic":1,"is_delivery_hub":0,"merchant_short_code":null,"map_place_id":null,"hub_updated_by":null,"created_at":"2025-08-04T09:05:23.000000Z","updated_at":"2025-08-04T09:05:23.000000Z","map_viewable":0},{"id":"1b3c259c6680438555a4d13c042e8b42","merchant_id":"2f582c3b1c67f59712fd58fc86bdab72","petition_id":"","store_id":"Blis","ref_no":null,"ach_merchant_name":null,"retailer":"Urban Bliss CanPay","pos_employee_login":0,"lat":"40.57142639","long":"-74.11155701","timezone_id":"8e62ee6dff5dfd57fa46e1a7b33d133b","address":"3456 East Circle Drive NE, Suite 103","zip":"55906","contact_no":"877-308-3344","email":"<EMAIL>","transaction_type_id":"ab7098be816bc2091bc456c6334a835d","location_type":"Retail","city":"Rochester","state":"MN","county":"Olmstead","website_address":"oneplant.us","delivery_hub_address":null,"average_monthly_sales":"250000.00","average_ticket_per_sale":135,"pos_provider":"2a6e464103f38476711d54568cc13740","recreational_retail_terminal_needed":3,"medical_terminal_needed":0,"multi_tablet_charging_stand":0,"hardware_shipped":"To DBA","merchant_daily_velocity_limit":"500.00","status":"aba5887e987117d2ef4c0711dc59d988","sync_disabled":0,"is_ecommerce":0,"is_text_based_payment":0,"text_based_logo_url":null,"ecommerce_admin_driven":0,"is_generic":1,"is_delivery_hub":0,"merchant_short_code":null,"map_place_id":null,"hub_updated_by":null,"created_at":"2025-08-04T09:05:23.000000Z","updated_at":"2025-08-04T09:05:23.000000Z","map_viewable":0}]},"ET":"0.416"}
 {"@timestamp":"2025-08-04T11:28:22.584537+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"edaa901f5fc8af89907ab41de723e80e","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/getstoretransactiontypes","METHOD":"POST","PARAM":{"store_id":"c351fd48f27e45050ff812855c30ca46"},"STATUS":200,"RESPONSE":{"code":200,"message":"Transaction types for store fetched successfully.","data":[{"id":"ab74755ff23dc095c56ae44e6454e791","type":"Retail","label":"RE"}]},"ET":"0.383"}
 {"@timestamp":"2025-08-04T11:28:24.057666+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"b018c832fa99c08b01fb3668df53a8e8","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/updateterminal","METHOD":"POST","PARAM":{"terminal_name":"ter00001","transaction_type_id":"ab74755ff23dc095c56ae44e6454e791","transaction_type":"Retail","store_id":"c351fd48f27e45050ff812855c30ca46","store_name":"Radiant Finds CanPay","edit":"77950d4123b3afed95e1134a7cdbfe51","created_at":"08-04-2025 11:25 AM","status_name":"Inactive","status":"aba5887e987117d2ef4c0711dc59d988","unique_identification_id":"1a20d02bf82d9e7433782f289ddcebf0","id":"77950d4123b3afed95e1134a7cdbfe51","terminal_unique_id":"1a20d02bf82d9e7433782f289ddcebf0"},"STATUS":200,"RESPONSE":{"code":200,"message":"Terminal updated successfully.","data":{"id":"77950d4123b3afed95e1134a7cdbfe51","merchant_store_id":"c351fd48f27e45050ff812855c30ca46","terminal_name":"ter00001","transaction_type_id":"ab74755ff23dc095c56ae44e6454e791","unique_identification_id":"1a20d02bf82d9e7433782f289ddcebf0","tip_allowed":0,"camera_option":"front","status":"8cb2bea34db1ee4fc8c9f0088e5e0dcf","added_by":"aa5c6efd7c2a216f600daaee1284d641","is_web":0,"created_at":"2025-08-04T11:25:02.000000Z","updated_at":"2025-08-04T11:28:24.000000Z"}},"ET":"0.410"}
 {"@timestamp":"2025-08-04T11:28:26.318344+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"d3dd3c36cbb3eee30d50aa3ba77e2abb","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","LEVEL":"INFO","PATH":"merchant/getauthorizedterminals","METHOD":"POST","PARAM":{"search":"","status":"Active","order":"created_at","dir":"desc"},"STATUS":200,"RESPONSE":{"code":200,"message":"Terminal List fetched successfully","data":[{"terminal_name":"deep0001","transaction_type_id":"ab74755ff23dc095c56ae44e6454e791","transaction_type":"Retail","store_id":"c351fd48f27e45050ff812855c30ca46","store_name":"Radiant Finds CanPay","edit":"290f817c6983dfc71ef51ade191a6cfa","created_at":"08-04-2025 11:27 AM","status_name":"Active","status":"aba5887e987117d2ef4c0711dc59d988","unique_identification_id":"fa754f41e3e2976a8836a0f611cfc9db"}]},"ET":"0.453"}
 {"@timestamp":"2025-08-04T11:28:37.821394+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"435a86e5979cfd3723f4c72e49b3b076","USER_ID":"","LEVEL":"INFO","PATH":"/","METHOD":"GET","PARAM":[],"STATUS":200,"RESPONSE":null,"ET":"0.365"}
 {"@timestamp":"2025-08-04T11:30:42.329064+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"f452ed695b339deb6ad1763e7da02b71","USER_ID":"","LEVEL":"INFO","PATH":"consumer/check/phoneNumber","METHOD":"POST","PARAM":{"phoneNo":"**********","sessionId":*************},"STATUS":200,"RESPONSE":{"code":200,"message":"Mobile number available for use.","data":"**********"},"ET":"0.731"}
 {"@timestamp":"2025-08-04T11:30:43.308333+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"a93df8724b3d16ac17cc92b2d5194b59","USER_ID":"","LEVEL":"INFO","PATH":"consumer/verify/phoneNumber","METHOD":"POST","PARAM":{"phoneNo":"**********","sessionId":*************,"firstName":"Ashi","middleName":"","lastName":"Yogi","suffix":"Suffix","from_ecommerce":0},"STATUS":200,"RESPONSE":{"code":200,"message":"Verification code sent successfully.","data":"790897"},"ET":"0.647"}
 {"@timestamp":"2025-08-04T11:30:50.859421+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"57af2de5888c56d8b5d12c6011a3bd0c","USER_ID":"","LEVEL":"INFO","PATH":"consumer/verify/otp","METHOD":"POST","PARAM":{"otp":"790897","phoneNo":"**********","sessionId":*************},"STATUS":200,"RESPONSE":{"code":200,"message":"OTP verified successfully.","data":{"id":"b1272264b2e9ea510cc04180143affad","session_id":"*************","email":null,"phone":"**********","first_name":"Ashi","middle_name":"","last_name":"Yogi","date_of_birth":null,"apt_number":null,"street_address":null,"city":null,"state":null,"zipcode":null,"ssn_number":null,"suffix":"","password":null,"consumer_type":"standard","email_verified":0,"id_validation":0,"is_validation_success":0,"bank_validation":0,"status_id":null,"steps_completed":1,"reset_reg_session":0,"registration_complete":0,"posted_for_fraud_detection":0,"from_ecommerce":0,"store_id":null,"created_at":"2025-08-04T11:30:43.000000Z","updated_at":"2025-08-04T11:30:50.000000Z"}},"ET":"0.543"}
 {"@timestamp":"2025-08-04T11:30:52.767461+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"042e4166417c77ffade936217ce579c4","USER_ID":"","LEVEL":"INFO","PATH":"consumer/redirect","METHOD":"POST","PARAM":{"sessionId":*************,"phoneNo":"**********"},"STATUS":200,"RESPONSE":{"code":200,"message":"Redirecting to next step.","data":2},"ET":"0.546"}
 {"@timestamp":"2025-08-04T11:31:09.736733+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"041b84fe58ee82a933c7903e685edae7","USER_ID":"","LEVEL":"INFO","PATH":"consumer/enterPin","METHOD":"POST","PARAM":{"pin":"1111","sessionId":*************},"STATUS":200,"RESPONSE":{"code":200,"message":"Quick access pin updated successfully.","data":3},"ET":"0.784"}
 {"@timestamp":"2025-08-04T11:32:58.102697+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"876f13cb33c0c4a07c1661a33e6d82e8","USER_ID":"","LEVEL":"INFO","PATH":"consumer/check/email","METHOD":"POST","PARAM":{"email":"<EMAIL>","sessionId":*************,"is_lite_to_normal_registration_started":false},"STATUS":200,"RESPONSE":{"code":200,"message":"Email available for use.","data":"<EMAIL>"},"ET":"0.556"}
 {"@timestamp":"2025-08-04T11:33:05.230618+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"f4116ba0dd9e699f4e969fc6e664d38d","USER_ID":"","LEVEL":"INFO","PATH":"consumer/idValidationWithPhone","METHOD":"POST","PARAM":{"email":"<EMAIL>","dateOfBirth":"12-01-1999","address":"123 New york","city":"New York","state":"NY","zip":"12211","aptNumber":"Appartment","sessionId":*************,"is_lite_to_normal_registration_started":false},"STATUS":200,"RESPONSE":{"code":200,"message":"SSN required for identity validation. Please provide SSN.","data":4},"ET":"6.770"}
 {"@timestamp":"2025-08-04T11:33:46.529658+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"0c58d6fc0733897d3888a15dac479513","USER_ID":"","LEVEL":"INFO","PATH":"consumer/identityAssessment","METHOD":"POST","PARAM":{"ssn":"1111","sessionId":*************},"STATUS":200,"RESPONSE":{"code":200,"message":"Identity validation failed. Please upload identity documents manually.","data":5},"ET":"3.931"}
 {"@timestamp":"2025-08-04T11:34:21.628113+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"0276b1d34af7de833366ca446e405166","USER_ID":"","LEVEL":"INFO","PATH":"consumer/login","METHOD":"POST","PARAM":{"phone":"**********"},"STATUS":308,"RESPONSE":{"code":308,"message":"Identity requires to be validated manually.Please upload your identity proof to continue enrollment.","data":{"steps_completed":5,"session_id":**********,"state":"NY","age":25,"microbilt_error_need_bank_link":false}},"ET":"0.965"}
 {"@timestamp":"2025-08-04T11:34:31.934938+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"16da68cd0743268c6ca232d07776b856","USER_ID":"","LEVEL":"INFO","PATH":"consumer/idValidationManual","METHOD":"POST","PARAM":{"sessionId":"**********","fileFront":{"Illuminate\\Http\\UploadedFile":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\php2E66.tmp"},"fileBack":{"Illuminate\\Http\\UploadedFile":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\php2E67.tmp"}},"STATUS":599,"RESPONSE":{"code":599,"message":"Invalid image type.","data":null},"ET":"0.667"}
 {"@timestamp":"2025-08-04T11:34:40.106322+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"dfe4f6f0ca18086cca0c3f486d2b2ffc","USER_ID":"","LEVEL":"INFO","PATH":"consumer/login","METHOD":"POST","PARAM":{"phone":"**********"},"STATUS":308,"RESPONSE":{"code":308,"message":"Identity requires to be validated manually.Please upload your identity proof to continue enrollment.","data":{"steps_completed":5,"session_id":**********,"state":"NY","age":25,"microbilt_error_need_bank_link":false}},"ET":"0.869"}
 {"@timestamp":"2025-08-04T11:35:06.878024+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"57f2f37845b8c454601bd985e0d02bfe","USER_ID":"","LEVEL":"INFO","PATH":"consumer/idValidationManual","METHOD":"POST","PARAM":{"sessionId":"**********","fileFront":{"Illuminate\\Http\\UploadedFile":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\php9FA1.tmp"},"fileBack":{"Illuminate\\Http\\UploadedFile":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\php9FB1.tmp"}},"STATUS":200,"RESPONSE":{"code":200,"message":"Identity proof uploaded successfully.","data":2},"ET":"6.622"}
 {"@timestamp":"2025-08-04T11:35:29.473134+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"e37d2f423f9770ee6993701f60442472","USER_ID":"","LEVEL":"INFO","PATH":"consumer/login","METHOD":"POST","PARAM":{"phone":"**********"},"STATUS":308,"RESPONSE":{"code":308,"message":"Only one enrollment allowed. Your identity validation is pending. CanPay will contact you once your submission has been reviewed.","data":{"steps_completed":null,"session_id":**********,"state":"NY","age":25,"microbilt_error_need_bank_link":false}},"ET":"0.872"}
 {"@timestamp":"2025-08-04T11:46:00.707758+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","PU":"LAPTOP-FKD2B6A9","TID":"53467e7653531082888c82b1b56b17ee","USER_ID":"","LEVEL":"INFO","PATH":"consumer/login","METHOD":"POST","PARAM":{"phone":"**********"},"STATUS":308,"RESPONSE":{"code":308,"message":"Only one enrollment allowed. Your identity validation is pending. CanPay will contact you once your submission has been reviewed.","data":{"steps_completed":null,"session_id":**********,"state":"NY","age":25,"microbilt_error_need_bank_link":false}},"ET":"1.344"}
