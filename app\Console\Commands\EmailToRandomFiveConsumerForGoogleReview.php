<?php
namespace App\Console\Commands;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Factories\EmailExecutor\EmailExecutorFactory;
use App\Models\UserEmailHistoryOfGoogleReview;
use Illuminate\Console\Command;

class EmailToRandomFiveConsumerForGoogleReview extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:for-google-review';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will send email to random five consumers for the Google review';
    private $chunkSize = 500;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->emailexecutor = new EmailExecutorFactory();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->info('Sending Email to random five consumers for google review started...');
        $succes_status_id = getStatus(SUCCESS);
        $pending_status_id = getStatus(PENDING);
        // fetch random five consumers with successful transaction
        $sql = "SELECT DISTINCT u.user_id, u.email, COUNT(td2.id) as tr_count,
        td1.local_transaction_time as tr_time
        FROM users u
        JOIN transaction_details td1 ON u.user_id = td1.consumer_id
        AND td1.status_id = ? AND td1.local_transaction_date = CURDATE()
        JOIN transaction_details td2 ON u.user_id = td2.consumer_id
        AND td2.status_id = ? AND td2.transaction_ref_no IS NULL
        LEFT JOIN user_email_history_of_google_review ugr ON u.user_id = ugr.user_id
        WHERE ugr.user_id IS NULL
        AND (
            (u.existing_user = 1 AND u.migrated_at <= DATE_SUB(NOW(), INTERVAL 90 DAY))
            OR
            (u.existing_user = 0 AND u.created_at <= DATE_SUB(NOW(), INTERVAL 90 DAY))
        )
        GROUP BY u.user_id
        HAVING tr_count >= 10
        ORDER BY tr_time DESC
        LIMIT ".env('NO_OF_RANDOM_CONSUMER_FOR_GOOGLE_REVIEW')."";

        $searchArray = [$pending_status_id, $succes_status_id];
        $result = DB::select($sql, $searchArray);
        Log::channel('email-for-google-review')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Random five consumers with successful transaction: " .json_encode($result));

        if(count($result) > 0) {
            foreach ($result as $user) {
                // send a email notification for each consumer
                $params['user_id'] = $user->user_id;
                $params['email'] = $user->email;
                $this->emailexecutor->emailRandomFiveConsumersForGoogleReview($params);
                $userEmailHistoryOfGoogleReview = new UserEmailHistoryOfGoogleReview();
                $userEmailHistoryOfGoogleReview->user_id = $user->user_id;
                $userEmailHistoryOfGoogleReview->save();
                Log::channel('email-for-google-review')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Email sent successfully for userID: " .$user->user_id);
            }
            Log::channel('email-for-google-review')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Email sent successfully for all users.");
        }
        else{
            Log::channel('email-for-google-review')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - User not found to mail for google review feedback.");
        }
    }
}
