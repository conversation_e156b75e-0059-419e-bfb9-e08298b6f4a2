<?php
namespace App\Console\Commands;

use App\Http\Clients\Acheck21HttpClient;
use App\Http\Factories\Transaction\TransactionFactory;
use App\Models\Acheck21DocumentIdHistory;
use App\Models\BankAccountInfo;
use App\Models\ConsumerAccountBalance;
use App\Models\ReturnRepresentHistory;
use App\Models\TransactionDetails;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class RepresentDailyReturn extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'represent:dailyreturn';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command represents returned transaction through acheck21 which are approved by consumer.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->transaction = new TransactionFactory();
        $this->acheck = new Acheck21HttpClient();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->info("Representing return transactions...");
        $success = getStatus(SUCCESS);
        $failed = getStatus(FAILED);
        // represent R01 transactions for finicity linked consumers
        $this->_representR01Transaction($success, $failed);
        $this->_representTransaction($success, $failed);
        // represent transactions for manually linked consumer which are waiting for webhook call
        $this->_representManualLinkedTransactions($success, $failed);
        $this->_createCanPayReturnTransaction();
    }

    private function _representR01Transaction($success, $failed)
    {
        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "=========== REPESENTING REPRESENTABLE RETURN TRANSACTIONS FOR FINICITY LINKED BANK ACCOUNTS===============");
        //fetch all the approved by consumer returned transactions
        $transactions = TransactionDetails::join('terminal_master', 'terminal_master.id', '=', 'transaction_details.terminal_id')->join('merchant_stores', 'terminal_master.merchant_store_id', '=', 'merchant_stores.id')->join('registered_merchant_master', 'registered_merchant_master.id', '=', 'merchant_stores.merchant_id')->join('users', 'users.user_id', '=', 'transaction_details.consumer_id')->join('status_master', 'transaction_details.status_id', '=', 'status_master.id')->join('return_reason_masters', 'transaction_details.return_reason', '=', 'return_reason_masters.id')->leftJoin('timezone_masters', 'timezone_masters.id', '=', 'transaction_details.timezone_id')->select('transaction_details.*', 'registered_merchant_master.acheck_account_id', 'return_reason_masters.reason_code as reason_code', 'timezone_masters.timezone_name', 'users.bank_link_type as bank_link_type', DB::raw('sum(transaction_details.consumer_bank_posting_amount) as sum'), DB::raw('sum(transaction_details.tip_amount) as tip_sum'))->where('users.bank_link_type', 1)->where('transaction_details.is_represented', 0)->where('transaction_details.consumer_approval', 1)->where('transaction_details.approved_to_represent', 1)->where('transaction_details.represent_block', 0)->where('status_master.code', RETURNED)->where('return_reason_masters.canpay_represent', 1)->where('return_reason_masters.bank_login', 0)->groupBy('transaction_details.consumer_id')->get();
        if (sizeof($transactions) != 0) {
            foreach ($transactions as $transaction) {
                $total_amount = ($transaction->sum);
                // storing into history table for each transaction
                $history = new ReturnRepresentHistory();
                $history->consumer_id = $transaction->consumer_id;
                $history->transaction_id = $transaction->id;
                $history->amount = $transaction->amount + $transaction->tip_amount;
                $history->is_manual = 0;
                try {
                    if ($transaction->return_from_primary_account == 1) {
                        // check account balance
                        $bank_account = ConsumerAccountBalance::on(MYSQL_RO)->where('error', 0)->where('account_id', $transaction->transaction_represented)->orderBy("created_at", 'DESC')->first();
                    } else {
                        $bank_account = ConsumerAccountBalance::on(MYSQL_RO)->where('error', 0)->where('account_id', $transaction->account_id)->orderBy("created_at", 'DESC')->first();
                    }
                    if (isset($bank_account->balance)) {
                        // check if account balance is sufficient for this transaction
                        if ($bank_account->balance >= $total_amount) {
                            // if transaction is nonrepresentable post new transaction
                            if ($transaction->return_from_primary_account == 1) {
                                //post the consumer debit transaction
                                Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Posting consumer transaction to acheck21 for transaction amount: " . $transaction->consumer_bank_posting_amount . " with id: " . $transaction->id);
                                $params['amount'] = $transaction->consumer_bank_posting_amount;
                                $params['consumer_id'] = $transaction->consumer_id;
                                $params['acheck_account_id'] = $transaction->acheck_account_id;
                                // send over the current active bank account id
                                $params['account_id'] = $bank_account->account_id;

                                if (env('ACHECK_POSTING')) {
                                    //calling the factory function to create consumer transaction into acheck21
                                    $response = $this->transaction->createConsumerReturnTransaction($params);
                                    $response_decoded = json_decode($response, true);
                                    Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is enabled. Posting in ACHECK21.");
                                } else {
                                    $response_decoded['documentId'] = rand(********, ********);
                                    Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is disabled. Continue with a random number.");
                                }

                                $this->_createTransaction($transaction, $response_decoded['documentId'], $bank_account->account_id);

                                $transaction->account_id = $bank_account->account_id;
                                // update the parent transaction
                                $transaction->is_represented = 1;
                                $transaction->represent_count = $transaction->represent_count + 1;
                                $transaction->save();
                                //adding details to store into history table
                                $history->outcome = "Success. New Transaction posted into acheck21. Executed from daily finicity scheduler.";
                                $history->reason_representable = 0;
                                $history->status_id = $success;
                                Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction represented(created new) for transaction amount: " . $transaction->consumer_bank_posting_amount . " with id: " . $transaction->id);
                            } else { //represent the transaction
                                Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Representing transaction to acheck21 for transaction amount: " . $transaction->consumer_bank_posting_amount . " with id: " . $transaction->id);
                                $reference_transaction = TransactionDetails::join('status_master', 'transaction_details.status_id', '=', 'status_master.id')->where('transaction_details.transaction_ref_no', $transaction->id)->where('status_master.code', PROCESSED_FOR_ACHECK21)->orderBy("transaction_details.created_at", 'ASC')->first();
                                $response = $this->acheck->representTransaction($reference_transaction->acheck_document_id);
                                if ($response == 204) {
                                    $this->_createTransaction($transaction, $reference_transaction->acheck_document_id, $transaction->account_id);
                                    // update the parent transaction
                                    $transaction->is_represented = 1;
                                    $transaction->represent_count = $transaction->represent_count + 1;
                                    $transaction->save();
                                    //adding details to store into history table
                                    $history->outcome = "Success. Old transaction represented into acheck21. Executed from daily scheduler.";
                                    $history->reason_representable = 1;
                                    $history->status_id = $success;
                                    Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction represented for transaction amount: " . $transaction->consumer_bank_posting_amount . " with id: " . $transaction->id);
                                } else {
                                    //adding details to store into history table
                                    $history->outcome = $response;
                                    $history->reason_representable = 1;
                                    $history->status_id = $failed;
                                    Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck21 could not represent the transaction for transaction amount: " . $transaction->consumer_bank_posting_amount . " with id: " . $transaction->id);
                                }
                            }
                        } else {
                            //adding details to store into history table
                            $history->outcome = "Consumer transaction amount: " . $transaction->consumer_bank_posting_amount . " but account balance is: " . $bank_account->balance;
                            $history->status_id = $failed;
                            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer transaction amount: " . $transaction->consumer_bank_posting_amount . " but account balance is: " . $bank_account->balance);
                        }
                    } else {
                        //adding details to store into history table
                        $history->outcome = "Consumer transaction amount: " . $transaction->consumer_bank_posting_amount . " but no account balance found";
                        $history->status_id = $failed;
                        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer transaction amount: " . $transaction->consumer_bank_posting_amount . " but no account balance found with transaction id: " . $transaction->id . " for consumer id: " . $transaction->consumer_id);
                    }
                    $history->save();
                } catch (\Exception $e) {
                    //adding details to store into history table
                    $history->outcome = $e;
                    $history->status_id = $failed;
                    Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "There was a problem trying to represent transaction with id: " . $transaction->id, [EXCEPTION => $e]);
                    $history->save();
                    continue;
                }
            }
            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transactions represented successfully.");
            $this->info("Transactions represented successfully.");
        } else {
            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No transactions found to represent.");
            $this->info("No transactions found to represent.");
        }
    }
    /**
     * This function fetches bank balance for all consumers who has added bank from finicity and keeps record in the database
     */
    private function _representTransaction($success, $failed)
    {
        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "=========== REPESENTING CONSUMER APPROVED RETURN TRANSACTIONS FOR FINICITY LINKED BANK ACCOUNTS===============");
        //fetch all the approved by consumer returned transactions
        $transactions = TransactionDetails::join('terminal_master', 'terminal_master.id', '=', 'transaction_details.terminal_id')->join('merchant_stores', 'terminal_master.merchant_store_id', '=', 'merchant_stores.id')->join('registered_merchant_master', 'registered_merchant_master.id', '=', 'merchant_stores.merchant_id')->join('users', 'users.user_id', '=', 'transaction_details.consumer_id')->join('status_master', 'transaction_details.status_id', '=', 'status_master.id')->join('return_reason_masters', 'transaction_details.return_reason', '=', 'return_reason_masters.id')->leftJoin('timezone_masters', 'timezone_masters.id', '=', 'transaction_details.timezone_id')->select('transaction_details.*', 'registered_merchant_master.acheck_account_id', 'return_reason_masters.canpay_represent as canpay_represent', 'timezone_masters.timezone_name', 'return_reason_masters.new_banking as new_banking', 'users.bank_link_type as bank_link_type', DB::raw('sum(transaction_details.consumer_bank_posting_amount) as sum'), DB::raw('sum(transaction_details.tip_amount) as tip_sum'))->where('users.bank_link_type', 1)->where('transaction_details.is_represented', 0)->where('transaction_details.consumer_approval', 1)->where('transaction_details.represent_block', 1)->where('status_master.code', RETURNED)->groupBy('transaction_details.consumer_id')->get();
        if (sizeof($transactions) != 0) {
            foreach ($transactions as $transaction) {
                $total_amount = ($transaction->sum);
                // storing into history table for each transaction
                $history = new ReturnRepresentHistory();
                $history->consumer_id = $transaction->consumer_id;
                $history->transaction_id = $transaction->id;
                $history->amount = $transaction->amount + $transaction->tip_amount;
                $history->is_manual = 0;
                try {
                    if ($transaction->return_from_primary_account == 1) {
                        $bank_account = BankAccountInfo::on(MYSQL_RO)->where('id', $transaction->transaction_represented)->first();
                    } else {
                        $bank_account = BankAccountInfo::on(MYSQL_RO)->where('id', $transaction->account_id)->first();
                    }
                    // if transaction should be posted as new transaction
                    if ($transaction->return_from_primary_account == 1) {
                        //post the consumer debit transaction
                        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Posting consumer transaction to acheck21 for transaction amount: " . $transaction->consumer_bank_posting_amount . " with id: " . $transaction->id);
                        $params['amount'] = $transaction->consumer_bank_posting_amount;
                        $params['consumer_id'] = $transaction->consumer_id;
                        $params['acheck_account_id'] = $transaction->acheck_account_id;
                        // send over the current active bank account id
                        $params['account_id'] = $bank_account->id;

                        if (env('ACHECK_POSTING')) {
                            //calling the factory function to create consumer transaction into acheck21
                            $response = $this->transaction->createConsumerReturnTransaction($params);
                            $response_decoded = json_decode($response, true);
                            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is enabled. Posting in ACHECK21.");
                        } else {
                            $response_decoded['documentId'] = rand(********, ********);
                            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is disabled. Continue with a random number.");
                        }

                        $this->_createTransaction($transaction, $response_decoded['documentId'], $bank_account->id);

                        $transaction->account_id = $bank_account->id;
                        // update the parent transaction
                        $transaction->is_represented = 1;
                        $transaction->represent_count = $transaction->represent_count + 1;
                        $transaction->save();
                        //adding details to store into history table
                        $history->outcome = "Success. New Transaction posted into acheck21. Executed from daily finicity scheduler.";
                        $history->reason_representable = 0;
                        $history->status_id = $success;
                        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction represented(created new) for transaction amount: " . $transaction->consumer_bank_posting_amount . " with id: " . $transaction->id);
                    } else { //represent the transaction
                        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Representing transaction to acheck21 for transaction amount: " . $transaction->consumer_bank_posting_amount . " with id: " . $transaction->id);
                        $reference_transaction = TransactionDetails::join('status_master', 'transaction_details.status_id', '=', 'status_master.id')->where('transaction_details.transaction_ref_no', $transaction->id)->where('status_master.code', PROCESSED_FOR_ACHECK21)->orderBy("transaction_details.created_at", 'ASC')->first();
                        $response = $this->acheck->representTransaction($reference_transaction->acheck_document_id);
                        if ($response == 204) {
                            $this->_createTransaction($transaction, $reference_transaction->acheck_document_id, $transaction->account_id);
                            // update the parent transaction
                            $transaction->is_represented = 1;
                            $transaction->represent_count = $transaction->represent_count + 1;
                            $transaction->save();
                            //adding details to store into history table
                            $history->outcome = "Success. Old transaction represented into acheck21. Executed from daily scheduler.";
                            $history->reason_representable = 1;
                            $history->status_id = $success;
                            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction represented for transaction amount: " . $transaction->consumer_bank_posting_amount . " with id: " . $transaction->id);
                        } else {
                            //adding details to store into history table
                            $history->outcome = $response;
                            $history->reason_representable = 1;
                            $history->status_id = $failed;
                            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck21 could not represent the transaction for transaction amount: " . $transaction->consumer_bank_posting_amount . " with id: " . $transaction->id);
                        }
                    }
                    $history->save();
                } catch (\Exception $e) {
                    //adding details to store into history table
                    $history->outcome = $e;
                    $history->status_id = $failed;
                    Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "There was a problem trying to represent transaction with id: " . $transaction->id, [EXCEPTION => $e]);
                    $history->save();
                    continue;
                }
            }
            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transactions represented successfully.");
            $this->info("Transactions represented successfully.");
        } else {
            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No transactions found to represent.");
            $this->info("No transactions found to represent.");
        }

    }
    /**
     * This function represents manually linked consumer trasnactions who have opted to pay now but waiting for webhook call
     */
    private function _representManualLinkedTransactions($success, $failed)
    {
        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "=========== REPESENTING PAY NOW RETURN TRANSACTIONS FOR MANUALLY LINKED BANK ACCOUNTS===============");
        //fetch all the approved by consumer returned transactions
        $transactions = TransactionDetails::join('terminal_master', 'terminal_master.id', '=', 'transaction_details.terminal_id')->join('merchant_stores', 'terminal_master.merchant_store_id', '=', 'merchant_stores.id')->join('registered_merchant_master', 'registered_merchant_master.id', '=', 'merchant_stores.merchant_id')->join('status_master', 'transaction_details.status_id', '=', 'status_master.id')->join('return_reason_masters', 'transaction_details.return_reason', '=', 'return_reason_masters.id')->leftJoin('timezone_masters', 'timezone_masters.id', '=', 'transaction_details.timezone_id')->join('users', 'users.user_id', '=', 'transaction_details.consumer_id')->select('transaction_details.*', 'return_reason_masters.canpay_represent as canpay_represent', 'return_reason_masters.new_banking as new_banking', 'timezone_masters.timezone_name', 'registered_merchant_master.acheck_account_id')->where('transaction_details.is_represented', 0)->where('users.bank_link_type', 0)->where('transaction_details.represent_block', 1)->where('status_master.code', RETURNED)->get();
        if (sizeof($transactions) != 0) {
            foreach ($transactions as $transaction) {
                // storing into history table for each transaction
                $history = new ReturnRepresentHistory();
                $history->consumer_id = $transaction->consumer_id;
                $history->transaction_id = $transaction->id;
                $history->amount = $transaction->amount + $transaction->tip_amount;
                $history->is_manual = 1;
                try {
                    // if transaction is nonrepresentable post new transaction
                    if ($transaction->return_from_primary_account == 1) {
                        //post the consumer debit transaction
                        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Posting consumer transaction to acheck21 for transaction amount: " . $transaction->consumer_bank_posting_amount . " with id: " . $transaction->id);
                        $bank_account_info = BankAccountInfo::on(MYSQL_RO)->where('id', $transaction->transaction_represented)->first();
                        $history->reason_representable = 0;
                        if (isset($bank_account_info->id)) {
                            $params['amount'] = $transaction->consumer_bank_posting_amount;
                            $params['consumer_id'] = $transaction->consumer_id;
                            $params['acheck_account_id'] = $transaction->acheck_account_id;
                            // send over the current active bank account id
                            $params['account_id'] = $bank_account_info->id;

                            if (env('ACHECK_POSTING')) {
                                //calling the factory function to create consumer transaction into acheck21
                                $response = $this->transaction->createConsumerReturnTransaction($params);
                                $response_decoded = json_decode($response, true);
                                Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is enabled. Posting in ACHECK21.");
                            } else {
                                $response_decoded['documentId'] = rand(********, ********);
                                Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is disabled. Continue with a random number.");
                            }

                            // update transaction account id with new linked account id
                            $this->_createTransaction($transaction, $response_decoded['documentId'], $bank_account_info->id);
                            $transaction->account_id = $bank_account_info->id;
                            // update the parent transaction
                            $transaction->is_represented = 1;
                            $transaction->represent_count = $transaction->represent_count + 1;
                            $transaction->save();
                            //adding details to store into history table
                            $history->outcome = "Success. New Transaction posted into acheck21. Executed from daily pay now scheduler.";
                            $history->status_id = $success;
                            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction represented(created new) for transaction amount: " . $transaction->consumer_bank_posting_amount . " with id: " . $transaction->id);
                        } else {
                            //adding details to store into history table
                            $history->outcome = "Consumer transaction amount: " . $transaction->consumer_bank_posting_amount . " but no active bank account found with consumer id: " . $transaction->consumer_id . " for transaction id: " . $transaction->id;
                            $history->status_id = $success;
                            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer transaction amount: " . $transaction->consumer_bank_posting_amount . " but no active bank account found with consumer id: " . $transaction->consumer_id . " for transaction id: " . $transaction->id);
                        }
                    } else { //represent the transaction
                        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Representing transaction to acheck21 for transaction amount: " . $transaction->consumer_bank_posting_amount . " with id: " . $transaction->id);
                        $reference_transaction = TransactionDetails::join('status_master', 'transaction_details.status_id', '=', 'status_master.id')->where('transaction_details.transaction_ref_no', $transaction->id)->where('status_master.code', PROCESSED_FOR_ACHECK21)->orderBy("transaction_details.created_at", 'ASC')->first();
                        $response = $this->acheck->representTransaction($reference_transaction->acheck_document_id);
                        if ($response == 204) {
                            $this->_createTransaction($transaction, $reference_transaction->acheck_document_id, $transaction->id);
                            // update the parent transaction
                            $transaction->is_represented = 1;
                            $transaction->represent_count = $transaction->represent_count + 1;
                            $transaction->save();
                            //adding details to store into history table
                            $history->outcome = "Success. Old transaction represented into acheck21. Executed from daily pay now scheduler.";
                            $history->reason_representable = 1;
                            $history->status_id = $success;
                            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction represented for transaction amount: " . $transaction->consumer_bank_posting_amount . " with id: " . $transaction->id);
                        } else {
                            //adding details to store into history table
                            $history->outcome = $response;
                            $history->reason_representable = 1;
                            $history->status_id = $failed;
                            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck21 could not represent the transaction for transaction amount: " . $transaction->consumer_bank_posting_amount . " with id: " . $transaction->id);
                        }
                    }
                    $history->save();
                } catch (\Exception $e) {
                    //adding details to store into history table
                    $history->outcome = $e;
                    $history->status_id = $failed;
                    Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "There was a problem trying to represent transaction with id: " . $transaction->id, [EXCEPTION => $e]);
                    $history->save();
                    continue;
                }
            }
            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transactions represented successfully.");
            $this->info("Transactions represented successfully.");
        } else {
            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No pay now return transactions found to represent.");
            $this->info("No transactions found to represent.");
        }

    }
    private function _createTransaction($transaction, $doc_id, $account_id)
    {
        //create a new transaction
        $transaction_details = new TransactionDetails();
        $transaction_details->transaction_number = $transaction->transaction_number;
        $transaction_details->transaction_ref_no = $transaction->id;
        $transaction_details->user_id = $transaction->user_id;
        $transaction_details->consumer_id = $transaction->consumer_id;
        $transaction_details->terminal_id = $transaction->terminal_id;
        $transaction_details->account_id = $account_id;
        $transaction_details->transaction_time = Carbon::now();
        $transaction_details->local_transaction_time = Carbon::now($transaction->timezone_name);
        $transaction_details->local_transaction_year = date("Y", strtotime($transaction_details->local_transaction_time));
        $transaction_details->local_transaction_month = date("m", strtotime($transaction_details->local_transaction_time));
        $transaction_details->local_transaction_date = date("Y-m-d", strtotime($transaction_details->local_transaction_time));
        $transaction_details->timezone_id = $transaction->timezone_id;
        $transaction_details->consumer_bank_posting_amount = $transaction->consumer_bank_posting_amount;
        $transaction_details->reward_amount_used = $transaction->reward_amount_used;
        $transaction_details->reward_point_used = $transaction->reward_point_used;
        $transaction_details->return_representment_reward_deduction_amount = $transaction->return_representment_reward_deduction_amount;
        $transaction_details->amount = $transaction->amount;
        $transaction_details->tip_amount = $transaction->tip_amount;
        $transaction_details->tip_type = $transaction->tip_type;
        $transaction_details->tip_add_time = $transaction->tip_add_time;
        $transaction_details->used_qr_id = $transaction->used_qr_id;
        $transaction_details->status_id = getStatus(PROCESSED_FOR_ACHECK21);
        $transaction_details->transaction_type_id = $transaction->transaction_type_id;
        $transaction_details->transaction_place = ACHECK21;
        $transaction_details->acheck_document_id = $doc_id;
        $transaction_details->is_represented = 1;
        $transaction_details->save();
        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction details was stored successfully.");

        //Add Document ID in Acheck21DocumentIdHistory table and update Previous Ignore Flags
        $this->_addDocumentIdHistory($transaction_details);
    }

    private function _addDocumentIdHistory($transaction)
    {
        //Update the ignore flags for the previous flags
        Acheck21DocumentIdHistory::where(['transaction_ref_no' => $transaction->transaction_ref_no])->update(['ignore_flag' => 1]);

        Log::channel('post-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Ignore Flag updated for Transation Ref No. : " . $transaction->transaction_ref_no);

        //Add new Document ID in Acheck21DocumentIdHistory table
        $document_history = new Acheck21DocumentIdHistory();
        $document_history->transaction_id = $transaction->id;
        $document_history->transaction_ref_no = $transaction->transaction_ref_no;
        $document_history->amount = $transaction->amount + $transaction->tip_amount;
        $document_history->acheck_document_id = $transaction->acheck_document_id;
        $document_history->save();

        Log::channel('post-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Document ID added for Transation ID : " . $transaction->id);
    }
    /**
     * Creates CanPay Recovery Return transaction for the day
     */
    private function _createCanPayReturnTransaction()
    {
        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Posting CanPay Return Recovery transaction to acheck21 for representment");
        $transaction_sum = TransactionDetails::join('status_master', 'transaction_details.status_id', '=', 'status_master.id')->select(DB::raw('sum(transaction_details.consumer_bank_posting_amount) as sum'), DB::raw('sum(transaction_details.tip_amount) as tip_sum'))->where('transaction_details.is_represented', 1)->where('transaction_details.is_v1', 0)->where('transaction_details.transaction_ref_no', null)->where('status_master.code', RETURNED)->first(); // sum query
        if ($transaction_sum->sum != null) {
            $amount = ($transaction_sum->sum);
            $params['amount'] = -$amount;
            $params['acheck_account_id'] = env('CANPAY_RETURN_RECOVERY');
            if (env('ACHECK_POSTING')) {
                //calling the factory function to create consumer transaction into acheck21
                $response = $this->transaction->createCanPayReturnTransaction($params);
                $response_decoded = json_decode($response, true);
                Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is enabled. Posting in ACHECK21.");
            } else {
                $response_decoded['documentId'] = rand(********, ********);
                Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is disabled. Continue with a random number.");
            }

            if (isset($response_decoded['documentId'])) {
                //create a new transaction
                $transaction_details = new TransactionDetails();
                $transaction_details->transaction_number = generateTransactionId();
                $transaction_details->entry_type = "Cr";
                $transaction_details->transaction_time = Carbon::now();
                $transaction_details->local_transaction_time = Carbon::now();
                $transaction_details->local_transaction_year = date("Y", strtotime($transaction_details->local_transaction_time));
                $transaction_details->local_transaction_month = date("m", strtotime($transaction_details->local_transaction_time));
                $transaction_details->local_transaction_date = date("Y-m-d", strtotime($transaction_details->local_transaction_time));
                $transaction_details->amount = $transaction_sum->sum;
                $transaction_details->tip_amount = $transaction_sum->tip_sum;
                $transaction_details->status_id = getStatus(PROCESSED_FOR_ACHECK21);
                $transaction_details->transaction_place = ACHECK21;
                $transaction_details->acheck_document_id = $response_decoded['documentId'];
                $transaction_details->is_represented = 1;
                $transaction_details->save();

                //update the status of all the represented returned transactions from returned to pending
                $transactions = TransactionDetails::join('status_master', 'transaction_details.status_id', '=', 'status_master.id')->select("transaction_details.*")->where('transaction_details.is_represented', 1)->where('transaction_details.is_v1', 0)->where('transaction_details.transaction_ref_no', null)->where('status_master.code', RETURNED)->get();
                foreach ($transactions as $transaction) {
                    $transaction->status_id = getStatus(PENDING);
                    $transaction->save();
                }
                Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "CanPay transaction details was stored successfully for amount(without tip): " . $transaction_sum->sum);
                $this->info("CanPay transaction details was stored successfully.");
            } else {
                Log::channel('return-transaction')->error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "There was some problem trying to post transaction into Acheck21.");
                $this->info("There was some problem trying to post transaction into Acheck21.");
            }
        } else {
            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No transactions found.");
            $this->info("No transactions found.");
        }
    }
}
