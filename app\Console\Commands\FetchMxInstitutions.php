<?php

namespace App\Console\Commands;

use App\Http\Factories\EmailExecutor\EmailExecutorFactory;
use App\Http\Factories\Mx\MxFactory;
use App\Models\FinancialInstitutionMaster;
use App\Models\FinancialInstitutionRoutingNumber;
use App\Models\MxBank;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class FetchMxInstitutions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fetch:mxinstitutions {--into_table=}';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will fetch the list of institutions from mx which are active and place the mx_institution_code in the financial_institution_masters table';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->mxFactory = new MxFactory();
        $this->emailexecutor = new EmailExecutorFactory();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Process started for fetching institutions from MX...");
        $this->info("Process started for fetching institutions from MX...");
        $params['page'] = 1;
        $time_now = Carbon::now();

        while (true) {
            // Call the API to get the institution list
            $response = $this->mxFactory->getInstitutionList($params); // Fetch the user list from MX
            if (!$response) {
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Failed to fetch institutions from the API. Error: " . $response->status());
                $this->error("Failed to fetch institutions from the API. Error: " . $response->status());
                return;
            }

            $institutions = $response['institutions'];

            $institutions_enabled = [];
            $institutions_disabled = [];
            foreach ($institutions as $institution) {
                if ($this->option('into_table') == 1) {
                    // insert the data in table only.
                    MxBank::create([
                        'code' => $institution['code'],
                        'forgot_password_url' => $institution['forgot_password_url'],
                        'forgot_username_url' => $institution['forgot_username_url'],
                        'instructional_text' => $institution['instructional_text'],
                        'medium_logo_url' => $institution['medium_logo_url'],
                        'name' => $institution['name'],
                        'small_logo_url' => $institution['small_logo_url'],
                        'supports_account_identification' => $institution['supports_account_identification'],
                        'supports_account_statement' => $institution['supports_account_statement'],
                        'supports_tax_document' => $institution['supports_tax_document'],
                        'supports_account_verification' => $institution['supports_account_verification'],
                        'supports_oauth' => $institution['supports_oauth'],
                        'supports_transaction_history' => $institution['supports_transaction_history'],
                        'trouble_signing_in_url' => $institution['trouble_signing_in_url'],
                        'url' => $institution['url'],
                        'created_at' => $time_now,
                        'updated_at' => $time_now,
                    ]);
                } else {
                    $imageData = file_get_contents($institution['small_logo_url']);
                    $small_logo_base64 = null;
                    $small_logo_extension = null;
                    if($imageData != false){
                        $small_logo_base64 = base64_encode($imageData);
                        $small_logo_extension = pathinfo($institution['small_logo_url'], PATHINFO_EXTENSION);
                    }
                    $check_institution_exists = FinancialInstitutionMaster::where('bank_name', $institution['name'])->first();
                    if (!empty($check_institution_exists)) {
                        if ($institution['supports_account_identification'] == 1 && $institution['supports_account_verification'] == 1) {
                            if($check_institution_exists->is_mx == 0 || $check_institution_exists->mx_institution_code != $institution['code']){ // Check if the data got changed
                                $check_institution_exists->is_mx = 1;
                                $check_institution_exists->mx_institution_code = $institution['code'];
                                $check_institution_exists->small_logo_base64_image = $small_logo_base64;
                                $check_institution_exists->small_logo_extension = $small_logo_extension;
                                $check_institution_exists->save();
                                $institutions_enabled[] = $institution['name']; // Push the institution name in the enabled institution array
                                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Institution data updated for bank Name: " . $institution['name']);
                                $this->info("Institution data updated for bank Name: " . $institution['name']);
                            }else{
                                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . " Initalting update base 64 image logo for bank Name: " . $institution['name']);
                                $check_institution_exists->small_logo_base64_image = $small_logo_base64;
                                $check_institution_exists->small_logo_extension = $small_logo_extension;
                                $check_institution_exists->save();     
                                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Update base 64 image logo for bank Name: " . $institution['name']." successfully.");
                            }
                        } else {
                            if($check_institution_exists->is_mx == 1){ // Check if the data got changed
                                $check_institution_exists->is_mx = 0;
                                $check_institution_exists->save();
                                $institutions_disabled[] = $institution['name']; // Push the institution name in the disabled institution array
                                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "MX disabled for bank Name: " . $institution['name']);
                                $this->info("MX disabled for bank Name: " . $institution['name']);
                            }
                        }
                    } else {
                        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Institution not found with bank Name: " . $institution['name']);
                        $this->info("Institution not found with bank Name: " . $institution['name']);
                        if ($institution['supports_account_identification'] == 1 && $institution['supports_account_verification'] == 1) {
                            // insert the data in table only.
                            $insert_bank = new FinancialInstitutionMaster();
                            $insert_bank->bank_name = $institution['name'];
                            $insert_bank->is_mx = 1;
                            $insert_bank->is_fed_excluded = 1;
                            $insert_bank->small_logo_base64_image = $small_logo_base64;
                            $insert_bank->small_logo_extension = $small_logo_extension;
                            $insert_bank->mx_institution_code = $institution['code'];
                            $insert_bank->save();

                            $institutions_enabled[] = $institution['name']; // Push the institution name in the enabled institution array

                            $insert_routing = new FinancialInstitutionRoutingNumber();
                            $insert_routing->routing_no = '*********';
                            $insert_routing->new_routing_no = '*********';
                            $insert_routing->financial_institution_id  = $insert_bank->id;
                            $insert_routing->save();
                            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Institution identification and verification is 1 this why bank store in fed table with Bank Name: " . $institution['name']);
                        }
                    }
                }
            }
            if ($response['pagination']['current_page'] < $response['pagination']['total_pages']) {
                $params['page']++;
            } else {
                break; // All entries are loaded, exit the loop
            }
        }

        // Send mail if any institution got enabled or disabled from MX
        if(!empty($institutions_enabled) || !empty($institutions_disabled)){
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Institution(s) added/removed from MX. Sending email notification...");
            $this->info("Institution(s) added/removed from MX. Sending email notification...");
            $institutions_enabled_list = array_map(function($index, $institution_name) {
                $index += 1; // Add 1 to start index from 1 instead of 0
                return "<p><b>{$index}.</b> {$institution_name}</p>";
            }, array_keys($institutions_enabled), $institutions_enabled);

            $institutions_disabled_list = array_map(function($index, $institution_name) {
                $index += 1; // Add 1 to start index from 1 instead of 0
                return "<p><b>{$index}.</b> {$institution_name}</p>";
            }, array_keys($institutions_disabled), $institutions_disabled);

            $body = "";

            if (!empty($institutions_enabled)) {
                $body .= "<b>Enabled Institutions:</b>";
                $body .= implode('', $institutions_enabled_list); // Convert array to string
                $body .= "<p></p>";
            }

            if (!empty($institutions_disabled)) {
                $body .= "<b>Disabled Institutions:</b>";
                $body .= "<p></p>";
                $body .= implode('', $institutions_disabled_list); // Convert array to string
            }

            // Sending Mail Notification
            $email_params = [
                'mx_institution_list_body' => $body,
            ];
            $this->emailexecutor->MxInstitutionChangeAlertEmail($email_params);
        }

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "MX institution list fetch process completed successfully.");
        $this->info("MX institution list fetch process completed successfully.");
    }

}
