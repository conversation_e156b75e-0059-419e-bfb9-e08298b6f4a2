<?php
namespace App\Console\Commands;

use App\Http\Clients\Acheck21HttpClient;
use App\Http\Factories\Transaction\TransactionFactory;
use App\Models\ReturnRepresentHistory;
use App\Models\TransactionDetails;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class RepresentV1ConsumerReturn extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'represent:v1consumertransaction';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command represents returned transaction through acheck21 which are approved by admin.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->transaction = new TransactionFactory();
        $this->acheck = new Acheck21HttpClient();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->_representTransaction();
        $this->_postCanPayOffset();
    }
    /**
     * This function fetches bank balance for all consumers who has added bank from finicity and keeps record in the database
     */
    private function _representTransaction()
    {
        $success = getStatus(SUCCESS);
        $failed = getStatus(FAILED);
        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "=========== REPESENTING CONSUMER APPROVED RETURN TRANSACTIONS FOR FINICITY LINKED BANK ACCOUNTS===============");
        //fetch all the approved by consumer returned transactions
        $transactions = TransactionDetails::join('terminal_master', 'terminal_master.id', '=', 'transaction_details.terminal_id')->join('merchant_stores', 'terminal_master.merchant_store_id', '=', 'merchant_stores.id')->join('registered_merchant_master', 'registered_merchant_master.id', '=', 'merchant_stores.merchant_id')->join('users', 'users.user_id', '=', 'transaction_details.consumer_id')->join('status_master', 'transaction_details.status_id', '=', 'status_master.id')->leftJoin('timezone_masters', 'timezone_masters.id', '=', 'transaction_details.timezone_id')->select('transaction_details.*', 'registered_merchant_master.acheck_account_id')->where('transaction_details.is_represented', 0)->where('status_master.code', APPROVED_BY_ADMIN)->get();
        if (sizeof($transactions) != 0) {
            foreach ($transactions as $transaction) {
                // storing into history table for each transaction
                $history = new ReturnRepresentHistory();
                $history->consumer_id = $transaction->consumer_id;
                $history->transaction_id = $transaction->id;
                $history->amount = $transaction->amount + $transaction->tip_amount;
                $history->is_manual = 0;
                try {
                    //post the consumer debit transaction
                    Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Posting consumer transaction to acheck21 for transaction amount: " . $transaction->amount . " with id: " . $transaction->id);
                    $params['amount'] = $transaction->consumer_bank_posting_amount;
                    $params['consumer_id'] = $transaction->consumer_id;
                    $params['acheck_account_id'] = $transaction->acheck_account_id;
                    // send over the current active bank account id
                    $params['account_id'] = $transaction->transaction_represented;

                    if (env('ACHECK_POSTING')) {
                        //calling the factory function to create consumer transaction into acheck21
                        $response = $this->transaction->createConsumerReturnTransaction($params);
                        $response_decoded = json_decode($response, true);
                        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is enabled. Posting in ACHECK21.");
                    } else {
                        $response_decoded['documentId'] = rand(********, ********);
                        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is disabled. Continue with a random number.");
                    }

                    $this->_createTransaction($transaction, $response_decoded['documentId']);
                    // update the parent transaction
                    $transaction->is_represented = 1;
                    $transaction->represent_count = $transaction->represent_count + 1;
                    $transaction->save();
                    //adding details to store into history table
                    $history->outcome = "Success. New Transaction posted into acheck21. Executed from daily scheduler.";
                    $history->reason_representable = 0;
                    $history->status_id = $success;
                    Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction represented(created new) for transaction amount: " . $transaction->consumer_bank_posting_amount . " with id: " . $transaction->id);
                    $history->save();
                } catch (\Exception $e) {
                    //adding details to store into history table
                    $history->outcome = $e;
                    $history->status_id = $failed;
                    Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "There was a problem trying to represent transaction with id: " . $transaction->id, [EXCEPTION => $e]);
                    $history->save();
                    continue;
                }
            }
            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transactions represented successfully.");
            $this->info("Transactions represented successfully.");
        } else {
            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No transactions found to represent.");
            $this->info("No transactions found to represent.");
        }

    }
    /**
     * This function represents manually linked consumer trasnactions who have opted to pay now but waiting for webhook call
     */
    private function _postCanPayOffset()
    {
        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Posting CanPay Return Recovery transaction to acheck21 for representment");
        $transaction_sum = TransactionDetails::join('status_master', 'transaction_details.status_id', '=', 'status_master.id')->select(DB::raw('sum(transaction_details.consumer_bank_posting_amount) as sum'), DB::raw('sum(transaction_details.tip_amount) as tip_sum'))->where('transaction_details.is_represented', 1)->where('transaction_details.is_v1', 0)->where('transaction_details.transaction_ref_no', null)->where('status_master.code', APPROVED_BY_ADMIN)->first(); // sum query
        if ($transaction_sum->sum != null) {
            $amount = ($transaction_sum->sum + $transaction_sum->tip_sum);
            $params['amount'] = -$amount;
            $params['acheck_account_id'] = env('CANPAY_RETURN_RECOVERY');
            if (env('ACHECK_POSTING')) {
                //calling the factory function to create consumer transaction into acheck21
                $response = $this->transaction->createCanPayReturnTransaction($params);
                $response_decoded = json_decode($response, true);
                Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is enabled. Posting in ACHECK21.");
            } else {
                $response_decoded['documentId'] = rand(********, ********);
                Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is disabled. Continue with a random number.");
            }

            if (isset($response_decoded['documentId'])) {
                //create a new transaction
                $transaction_details = new TransactionDetails();
                $transaction_details->transaction_number = generateTransactionId();
                $transaction_details->entry_type = "Cr";
                $transaction_details->transaction_time = Carbon::now();
                $transaction_details->local_transaction_time = Carbon::now();
                $transaction_details->local_transaction_year = date("Y", strtotime($transaction_details->local_transaction_time));
                $transaction_details->local_transaction_month = date("m", strtotime($transaction_details->local_transaction_time));
                $transaction_details->local_transaction_date = date("Y-m-d", strtotime($transaction_details->local_transaction_time));
                $transaction_details->amount = $transaction_sum->sum;
                $transaction_details->tip_amount = $transaction_sum->tip_sum;
                $transaction_details->status_id = getStatus(PROCESSED_FOR_ACHECK21);
                $transaction_details->transaction_place = ACHECK21;
                $transaction_details->acheck_document_id = $response_decoded['documentId'];
                $transaction_details->is_represented = 1;
                $transaction_details->save();

                //update the status of all the represented returned transactions from returned to pending
                $transactions = TransactionDetails::join('status_master', 'transaction_details.status_id', '=', 'status_master.id')->select("transaction_details.*")->where('transaction_details.is_represented', 1)->where('transaction_details.is_v1', 0)->where('transaction_details.transaction_ref_no', null)->where('status_master.code', APPROVED_BY_ADMIN)->get();
                foreach ($transactions as $transaction) {
                    $transaction->status_id = getStatus(PENDING);
                    $transaction->save();
                }
                Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "CanPay transaction details was stored successfully for amount(without tip): " . $transaction_sum->sum);
                $this->info("CanPay transaction details was stored successfully.");
            } else {
                Log::channel('return-transaction')->error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "There was some problem trying to post transaction into Acheck21.");
                $this->info("There was some problem trying to post transaction into Acheck21.");
            }
        } else {
            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No transactions found.");
            $this->info("No transactions found.");
        }
    }
    private function _createTransaction($transaction, $doc_id)
    {
        //create a new transaction
        $transaction_details = new TransactionDetails();
        $transaction_details->transaction_number = $transaction->transaction_number;
        $transaction_details->transaction_ref_no = $transaction->id;
        $transaction_details->user_id = $transaction->user_id;
        $transaction_details->consumer_id = $transaction->consumer_id;
        $transaction_details->terminal_id = $transaction->terminal_id;
        $transaction_details->account_id = $transaction->transaction_represented;
        $transaction_details->transaction_time = Carbon::now();
        $transaction_details->local_transaction_time = Carbon::now($transaction->timezone_name);
        $transaction_details->local_transaction_year = date("Y", strtotime($transaction_details->local_transaction_time));
        $transaction_details->local_transaction_month = date("m", strtotime($transaction_details->local_transaction_time));
        $transaction_details->local_transaction_date = date("Y-m-d", strtotime($transaction_details->local_transaction_time));
        $transaction_details->timezone_id = $transaction->timezone_id;
        $transaction_details->consumer_bank_posting_amount = $transaction->consumer_bank_posting_amount;
        $transaction_details->reward_amount_used = $transaction->reward_amount_used;
        $transaction_details->reward_point_used = $transaction->reward_point_used;
        $transaction_details->return_representment_reward_deduction_amount = $transaction->return_representment_reward_deduction_amount;
        $transaction_details->amount = $transaction->amount;
        $transaction_details->tip_amount = $transaction->tip_amount;
        $transaction_details->tip_type = $transaction->tip_type;
        $transaction_details->tip_add_time = $transaction->tip_add_time;
        $transaction_details->used_qr_id = $transaction->used_qr_id;
        $transaction_details->status_id = getStatus(PROCESSED_FOR_ACHECK21);
        $transaction_details->transaction_type_id = $transaction->transaction_type_id;
        $transaction_details->transaction_place = ACHECK21;
        $transaction_details->acheck_document_id = $doc_id;
        $transaction_details->is_represented = 1;
        $transaction_details->save();
        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction details was stored successfully.");
    }
}
