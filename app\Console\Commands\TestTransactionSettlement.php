<?php
namespace App\Console\Commands;

use App\Models\TransactionDetails;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class TestTransactionSettlement extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:settletransaction {--consumer_phones=}';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will will settle all the transactions for the consumer from input';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->info("Transaction settlement process started for the consumers...");
        Log::channel('update-final-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction settlement process started for the consumers...");
        $phone_array = explode(",", $this->option('consumer_phones'));
        $pending = getStatus(PENDING);
        $success = getStatus(SUCCESS);
        foreach ($phone_array as $phone) {
            $this->info("Updating transactions for Consumer: " . $phone);
            Log::channel('update-final-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Updating transactions for Consumer: " . $phone);
            $transactions = TransactionDetails::join("users", 'users.user_id', '=', 'transaction_details.consumer_id')->select('transaction_details.*')->whereNull('transaction_details.transaction_ref_no')->where(['transaction_details.status_id' => $pending, 'transaction_details.is_v1' => 0, 'transaction_details.isCanpay' => 0, 'users.phone' => $phone])->get();
            foreach ($transactions as $transaction) {
                // update the parent transaction
                TransactionDetails::where('id', $transaction->id)->update(array('status_id' => $success));
                // created new success row
                $this->_createTransaction($transaction, $success);
            }
        }
        $this->info("Transaction settlement process completed successfully.");
    }

    private function _createTransaction($transaction, $success)
    {
        //create a new transaction
        $transaction_details = new TransactionDetails();
        $transaction_details->transaction_number = $transaction->transaction_number;
        $transaction_details->transaction_ref_no = $transaction->id;
        $transaction_details->user_id = $transaction->user_id;
        $transaction_details->consumer_id = $transaction->consumer_id;
        $transaction_details->terminal_id = $transaction->terminal_id;
        $transaction_details->transaction_time = Carbon::now();
        $transaction_details->local_transaction_time = Carbon::now($transaction->timezone_name);
        $transaction_details->local_transaction_year = date("Y", strtotime($transaction_details->local_transaction_time));
        $transaction_details->local_transaction_month = date("m", strtotime($transaction_details->local_transaction_time));
        $transaction_details->local_transaction_date = date("Y-m-d", strtotime($transaction_details->local_transaction_time));
        $transaction_details->timezone_id = $transaction->timezone_id;
        $transaction_details->amount = $transaction->amount;
        $transaction_details->tip_amount = $transaction->tip_amount;
        $transaction_details->tip_type = $transaction->tip_type;
        $transaction_details->tip_add_time = $transaction->tip_add_time;
        $transaction_details->used_qr_id = $transaction->used_qr_id;
        $transaction_details->status_id = $success;
        $transaction_details->transaction_type_id = $transaction->transaction_type_id;
        $transaction_details->save();
        Log::channel('update-final-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction details was stored successfully for parent transaction: " . $transaction->id);
    }
}
