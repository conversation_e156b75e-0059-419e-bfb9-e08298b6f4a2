<?php
namespace App\Console\Commands;

use App\Http\Clients\Acheck21HttpClient;
use App\Models\Acheck21HistoryTable;
use App\Models\TransactionDetails;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ScheduleVoidTransaction extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'post:voidtransaction';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will post all the void transactions into acheck21';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->acheck = new Acheck21HttpClient();
        $this->failed = getStatus(FAILED);
        $this->voided = getStatus(VOIDED);
        $this->process_for_acheck = getStatus(PROCESSED_FOR_ACHECK21);
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // Get the Transaction date based on EST time
        if (Carbon::now()->timezone(ACHECK21_TIMEZONE_FOR_WEBHOOK)->lt(Carbon::now()->timezone(ACHECK21_TIMEZONE_FOR_WEBHOOK)->toDateString() . " 07:00:00")) {
            $transaction_date = Carbon::now()->timezone(ACHECK21_TIMEZONE_FOR_WEBHOOK)->subDays(1)->toDateString();
        } else {
            $transaction_date = Carbon::now()->timezone(ACHECK21_TIMEZONE_FOR_WEBHOOK)->toDateString();
        }
        Log::channel('post-void-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Posting Voided transaction data to ACHECK21 for date: " . $transaction_date . " started...");
        Log::channel('post-void-transaction')->debug(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Checking webhook status for date: " . $transaction_date . "...");

        // Check if webhook has been called from ACHECK21 for the transaction date
        $checkForAcheckWebhook = getWebhookDataforSpecificDate($transaction_date);
        if (!empty($checkForAcheckWebhook) && Carbon::now()->gte(Carbon::parse($checkForAcheckWebhook->created_at)->addHour())) {
            $this->_postVoidTransaction();
        } else {
            Log::channel('post-void-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Couldn't post new transactions to acheck21. Past transactions did not get Processed To Bank.");
            $this->info("Couldn't post new transactions to acheck21. Past transactions did not get Processed To Bank.");
        }
    }
    /**
     * This function fetches bank balance for all consumers who has added bank from finicity and keeps record in the database
     */
    private function _postVoidTransaction()
    {
        $this->info("Fetching all the void transactions to post into Acheck21...");
        Log::channel('post-void-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Fetching all the voided transactions having acheck21 row.");

        $transaction_sql = "SELECT td1.transaction_ref_no AS id, td1.acheck_document_id, td1.amount, td1.transaction_number, td1.consumer_id, td1.terminal_id, td1.timezone_id, td1.tip_amount, td1.tip_type, td1.tip_add_time, td1.used_qr_id, td1.status_id, td1.transaction_type_id, rmm.acheck_account_id, tz.timezone_name
        FROM transaction_details td
        JOIN terminal_master tm ON tm.id = td.terminal_id
        JOIN merchant_stores ms ON tm.merchant_store_id = ms.id
        JOIN registered_merchant_master rmm ON rmm.id = ms.merchant_id
        JOIN timezone_masters tz ON tz.id = td.timezone_id
        LEFT JOIN transaction_details td1 on td1.id = (
           SELECT id
           FROM transaction_details td3
           WHERE td3.transaction_ref_no = td.id
           Order BY td3.created_at DESC
           LIMIT 1
        ) AND td1.`status_id` = ?
        WHERE td.transaction_ref_no IS NULL AND td.merchant_id IS NULL AND IFNULL(td.consumer_id, '') != '' AND td.`status_id` = ? AND td.is_v1 = 0 AND td.merchant_id IS NULL AND td1.id IS NOT NULL;";

        $transactions = DB::connection(MYSQL_RO)->select($transaction_sql, [$this->process_for_acheck, $this->voided]);

        Log::channel('post-void-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transactions to be voided in ACHECK21: " . count($transactions));
        $this->info("Transactions to be voided in ACHECK21: " . count($transactions));

        if (sizeof($transactions) != 0) {
            foreach ($transactions as $transaction) {
                $history = array(
                    'transaction_id' => $transaction->id,
                    'void_transaction_posting' => 1,
                    'status_id' => $this->failed,
                );
                //calling the factory function to create consumer transaction into acheck21
                try {
                    $params['acheck_account_id'] = $transaction->acheck_account_id;
                    //calling the factory function to create consumer transaction into acheck21
                    $params['acheck_document_id'] = $transaction->acheck_document_id;
                    Log::channel('post-void-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Posting cancel request for consumer void transaction to acheck21 for amount: " . $transaction->amount . " with id: " . $transaction->transaction_number);
                    if (env('ACHECK_POSTING')) {
                        $response = $this->acheck->deleteTransaction($params);
                        Log::channel('post-void-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is enabled. Posting in ACHECK21.");
                    } else {
                        $response = 204;
                        Log::channel('post-void-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is disabled. Continue with static 204 response.");
                    }
                    if ($response != 204) {
                        Acheck21HistoryTable::create($history);
                        Log::channel('post-void-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Could not void transaction for amount: " . $transaction->amount . " with transaction number: " . $transaction->transaction_number);
                    } else {
                        $this->_saveTransaction($transaction);
                        Log::channel('post-void-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction for amount: " . $transaction->amount . " with transaction number " . $transaction->transaction_number . " voided successfully: ");
                    }
                } catch (\Exception $e) {
                    Acheck21HistoryTable::create($history);
                    Log::channel('post-void-transaction')->error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Exception occured during Posting Void Transaction for Transaction ID: " . $transaction->id . " in ACHECK21. ", [EXCEPTION => $e]);
                    $this->info($e); // Exception Returned
                }
            }
            Log::channel('post-void-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transactions Voided: " . count($transactions) . ". Finished void transaction posting to ACHECK21.");
            $this->info("Transactions Voided: " . count($transactions) . ". Finished void transaction posting to ACHECK21.");
        } else {
            Log::channel('post-void-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No transactions found.");
            $this->info("No transactions found.");
        }
    }

    private function _saveTransaction($transaction)
    {
        //create a new transaction
        $transaction_details = new TransactionDetails();
        $transaction_details->transaction_number = $transaction->transaction_number;
        $transaction_details->transaction_ref_no = $transaction->id;
        $transaction_details->user_id = isset($data['user_id']) ? $data['user_id'] : null;
        $transaction_details->consumer_id = $transaction->consumer_id;
        $transaction_details->terminal_id = $transaction->terminal_id;
        $transaction_details->transaction_time = Carbon::now();
        $transaction_details->local_transaction_time = Carbon::now($transaction->timezone_name);
        $transaction_details->local_transaction_year = date("Y", strtotime($transaction_details->local_transaction_time));
        $transaction_details->local_transaction_month = date("m", strtotime($transaction_details->local_transaction_time));
        $transaction_details->local_transaction_date = date("Y-m-d", strtotime($transaction_details->local_transaction_time));
        $transaction_details->timezone_id = $transaction->timezone_id;
        $transaction_details->amount = $transaction->amount;
        $transaction_details->tip_amount = $transaction->tip_amount;
        $transaction_details->tip_type = $transaction->tip_type;
        $transaction_details->tip_add_time = $transaction->tip_add_time;
        $transaction_details->used_qr_id = $transaction->used_qr_id;
        $transaction_details->status_id = $this->voided;
        $transaction_details->transaction_type_id = $transaction->transaction_type_id;
        $transaction_details->transaction_place = ACHECK21;
        $transaction_details->save();
    }

}
