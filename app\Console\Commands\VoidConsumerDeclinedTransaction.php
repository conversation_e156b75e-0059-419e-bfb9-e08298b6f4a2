<?php
namespace App\Console\Commands;

use App\Http\Factories\Webhook\WebhookFactory;
use App\Models\StatusMaster;
use App\Models\TimezoneMaster;
use App\Models\TransactionDetails;
use App\Models\TransactionModificationHistory;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;

class VoidConsumerDeclinedTransaction extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'void:declinedtransaction';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'If transaction modification is declined by the consumer then void the transaction';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->merchantWebhook = new WebhookFactory();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Log::channel('consumer-declined-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer Declined Transaction void CRON START==========");

        $voided = StatusMaster::where('code', VOIDED)->first();
        $declined = getStatus(DECLINED);
        $settlementTimeInUTC = convertSettlementTimeToUTC();

        // Get modified not accepted transactions
        $getDeclinedTransactions = TransactionDetails::select('transaction_details.*')
            ->leftJoin('transaction_details as modified_transaction', 'modified_transaction.id', '=', 'transaction_details.change_request_transaction_ref_no')
            ->where('transaction_details.transaction_ref_no', null)->where('transaction_details.change_request', 1)->where('transaction_details.consumer_approval_for_change_request', $declined)
            ->whereRaw("IF(modified_transaction.id != '', modified_transaction.scheduled_posting_time, transaction_details.scheduled_posting_time) < '$settlementTimeInUTC'")->orderBy('transaction_details.local_transaction_time', 'DESC')->get();
        
        if(count($getDeclinedTransactions) > 0){
            Log::channel('consumer-declined-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Modification Declined Transaction Count " . count($getDeclinedTransactions) . "==========");
            foreach($getDeclinedTransactions as $transaction){
                $timezone_details = TimezoneMaster::find($transaction->timezone_id);
                $transaction_local_time = Carbon::now();
                if ($transaction->timezone_id) {
                    $transaction_local_time = Carbon::now()->timezone($timezone_details->timezone_name);
                }
                 // check modified transction and expire the modified transaction
                if($transaction->change_request_transaction_ref_no != null){
                    $m_transaction = TransactionDetails::where('id', $transaction->change_request_transaction_ref_no)->first();
                    if($m_transaction){
                        $m_transaction->status_id = $voided->id;
                        $m_transaction->voided_time = Carbon::now();
                        $m_transaction->voided_local_time = $transaction_local_time;
                        $m_transaction->save();
                    }
                }

                $LatestModificationHistory = TransactionModificationHistory::where('transaction_id', $transaction->id)->orderBy('created_at', 'DESC')->first();
                if ($LatestModificationHistory) {
                    // Add voided history
                    $newModificationHistory = $LatestModificationHistory->replicate();
                    $newModificationHistory->id = generateUUID();
                    $newModificationHistory->local_transaction_time = Carbon::now($LatestModificationHistory->timezone_name);
                    $newModificationHistory->reason_id = null;
                    $newModificationHistory->additional_reason = null;
                    $newModificationHistory->status_id = $voided->id;
                    $newModificationHistory->save();
                }

                //if modified transaction present then take modified id other wise parent transaction id
                $transactionId = $transaction->change_request_transaction_ref_no ? $transaction->change_request_transaction_ref_no : $transaction->id;

                $transaction->status_id = $voided->id;
                $transaction->voided_time = Carbon::now();
                $transaction->change_request = 0;
                $transaction->voided_local_time = $transaction_local_time;
                $transaction->save();
                //webhook call after transaction expire
                $this->merchantWebhook->modificationWebhookCall($transactionId, $voided->status);
                Log::channel('consumer-declined-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer Declined Transaction void successfully for transaction_id =  " . $transaction->id);
            }
        }else{
            Log::channel('consumer-declined-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No transactions found.");
            $this->info("No transactions found.");
        }
        Log::channel('consumer-declined-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer Declined Transaction void CRON ENDED==========");
    }


}
