<?php

use App\Models\EmailTemplate;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migration to create the 'Consumer Post-Transaction Tip Added Notification' email template.
     *
     * This function creates the email template with a predefined subject and body
     * to notify consumers when they successfully add a tip to their transaction.
     */

    public function up(): void
    {
        EmailTemplate::firstOrCreate([
                'template_name' => 'Consumer Post-Transaction Tip Added Notification',
                'template_subject' => "Thanks for Tipping! Here's Your Updated Transaction Details",
                'template_body' => '<table role="presentation" class="main"> <tr> <td class="wrapper"> <table role="presentation" border="0" cellpadding="0" cellspacing="0"> <tr> <td> <p>Hi {{$user_details->first_name." ".$user_details->middle_name." ".$user_details->last_name}},</p> <p>You have successfully added a tip to your transaction at <strong>{{$transaction_details->retailer}}</strong>.</p> <p>Below are the updated transaction details:</p> <p> <table role="presentation" cellpadding="0" cellspacing="0" style="border-collapse: collapse; width: 100%;"> <thead> <tr> <th style="padding: 8px;">Transaction No.</th> <th style="padding: 8px;">Transaction Time</th> <th style="padding: 8px;">Store</th> <th style="padding: 8px; text-align: right;">Added Tip($)</th> <th style="padding: 8px; text-align: right;">Total Amount($)</th> </tr> </thead> <tbody> <tr style="background-color:#eee;"> <td style="padding: 8px;">{{$transaction_details->transaction_number}}</td> <td style="padding: 8px;">{{$transaction_details->local_transaction_time}}</td> <td style="padding: 8px;">{{$transaction_details->retailer}}</td> <td style="padding: 8px; text-align: right;">{{ number_format($transaction_details->tip_amount, 2) }}</td> <td style="padding: 8px; text-align: right;">{{ number_format($transaction_details->consumer_bank_posting_amount, 2) }}</td> </tr> </tbody> </table> </p> <p>We appreciate your generosity.</p> <p>Best Regards,</p> <p>CanPay Team</p> </td> </tr> </table> </td> </tr> </table>'
            ]);

    }
};