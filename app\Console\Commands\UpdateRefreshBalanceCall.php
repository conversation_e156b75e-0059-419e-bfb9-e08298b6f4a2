<?php
namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class UpdateRefreshBalanceCall extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:refreshbalancecall';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will update the refresh_balance_called flag from 1 to 0 for all the consumers.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->_updateConsumers();
    }


    private function _updateConsumers()
    {
        $this->info("Fetching all the consumers to update refresh balance call...");
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "=========== UPDATE REFRESH BALANCE CALL CRON STARTED RUNNING ==========");
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Fetching all the consumers to update refresh balance call.");

        // Updating refresh_balance_called column to 0
        $consumer_role_id = getRole(CONSUMER);
        User::where('role_id', $consumer_role_id)->update(['refresh_balance_called' => 0]);

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "All consumers updated successfully.");
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "=========== UPDATE REFRESH BALANCE CALL FINISHED RUNNING ==========");
        $this->info("Consumers updated successfully.");
    }
}
