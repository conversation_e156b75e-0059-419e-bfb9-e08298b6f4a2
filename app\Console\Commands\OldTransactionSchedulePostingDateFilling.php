<?php
namespace App\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class OldTransactionSchedulePostingDateFilling extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'schedulepostingdate:datafilling {--from_date=} {--to_date=}';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Old transaction schedule_posting_date update by local_transaction_date';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $from_date = $this->option('from_date');
        $to_date = $this->option('to_date');
       //Old transaction schedule_posting_date update by local_transaction_date
       DB::statement("UPDATE transaction_details as td SET td.scheduled_posting_date = td.local_transaction_date WHERE transaction_ref_no is null AND td.scheduled_posting_date is null and td.local_transaction_date BETWEEN $from_date AND $to_date");
    }

}
