<?php

namespace App\Console\Commands;

use App\Models\Reward;
use App\Models\TransactionDetails;
use App\Models\UserCurrentRewardDetail;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UpdateCashbackPoints extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:cashbackpoints {--transaction_settlement_date=}';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will update the cashback points for transactions that has been settled today.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $today_in_est = $this->option('transaction_settlement_date') != '' ? $this->option('transaction_settlement_date') : Carbon::now()->timezone(env('TRASNACTION_SETTLEMENT_TIMEZONE'))->toDateString();

        Log::channel('update-cashback-points')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . ": Cashback points update program started for Date(EST): " . $today_in_est . "...");
        $success = getStatus(SUCCESS);
        $pending = getStatus(PENDING);
        $transaction_sql = "SELECT td.id FROM transaction_details as td JOIN transaction_details as td1 ON td.id = td1.transaction_ref_no WHERE td.status_id = ? and td1.local_transaction_date = ? AND td.cashback_points_earned > 0";
        $transactions = DB::connection(MYSQL_RO)->select($transaction_sql, [$success, $today_in_est]);
        if (!empty($transactions)) {
            Log::channel('update-cashback-points')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . ": Total number of transactions that need to be processed are: " . count($transactions));
            foreach ($transactions as $transaction) {
                $this->_updateCashbackStatus($transaction);
            }
        } else {
            Log::channel('update-cashback-points')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . ": No Transactions found to update Cashback point status.");
        }
        // For the difference in Timezone , if there is any unsettled points for settled transactions, then it will be settled by this function
        $unsettled_rewards = TransactionDetails::join(env('DB_DATABASE_REWARD_WHEEL') . '.rewards', 'rewards.transaction_id', '=', 'transaction_details.id')
            ->select('transaction_details.id')
            ->where(['transaction_details.status_id' => $success, 'rewards.status_id' => $pending, 'rewards.is_cashback_points' => 1])
            ->orderBy('rewards.created_at', 'DESC')
            ->get();
        if (!empty($unsettled_rewards)) {
            Log::channel('update-cashback-points')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . ": Total number of unsettled rewards that need to be processed are: " . count($unsettled_rewards));
            foreach ($unsettled_rewards as $unsettled_reward) {
                $this->_updateCashbackStatus($unsettled_reward);
            }
        } else {
            Log::channel('update-cashback-points')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . ": No UNsettled rewards found to update Reward Status.");
        }
    }

    /**
     * _updateCashbackStatus
     * This function will activate the Cashback points exists in this Transaction
     * @param  mixed $transaction
     * @return void
     */
    private function _updateCashbackStatus($transaction)
    {
        $pending = getStatus(PENDING);
        $active = getStatus(ACTIVE);
        $reward_inactive = getStatus(REWARD_WHEEL_INACTIVE);

        $reward_status = [$active, $reward_inactive];
        // Check if Reward Exists against this Transaction ID
        $rewards = Reward::join('user_reward_usage_history', 'rewards.id', '=', 'user_reward_usage_history.reward_id')
            ->select('rewards.*', 'user_reward_usage_history.reward_point', 'user_reward_usage_history.reward_amount', 'user_reward_usage_history.is_generic_point')
            ->where(['rewards.transaction_id' => $transaction->id, 'rewards.status_id' => $pending, 'rewards.is_cashback_points' => 1])
            ->get();
        if (!empty($rewards)) {
            foreach ($rewards as $reward) {
                // Update Reward to Active status after the Transaction is success
                $reward_update = Reward::find($reward->id);
                $reward_update->status_id = $active;
                $reward_update->save();

                // Update Reward points
                $user_current_reward_details = UserCurrentRewardDetail::where(['user_id' => $reward->user_id, 'is_generic_point' => $reward->is_generic_point])->whereNull('sponsor_link_id');
                $reward->is_generic_point == 0 ? $user_current_reward_details->where('corporate_parent_id', $reward->corporate_parent_id) : '';
                $user_current_reward_details = $user_current_reward_details->first();
                if (!empty($user_current_reward_details)) {
                    $user_current_reward_details->increment('reward_amount', $reward->reward_amount);
                    $user_current_reward_details->increment('reward_point', $reward->reward_point);
                } else {
                    $user_current_reward_details = new UserCurrentRewardDetail();
                    if ($reward->is_generic_point == 0) {
                        $user_current_reward_details->corporate_parent_id = $reward->corporate_parent_id;
                    }
                    $user_current_reward_details->user_id = $reward->user_id;
                    $user_current_reward_details->reward_amount = $reward->reward_amount;
                    $user_current_reward_details->reward_point = $reward->reward_point;
                    $user_current_reward_details->is_generic_point = $reward->is_generic_point;
                    $user_current_reward_details->save();
                }

                $this->info("Cashback point Status updated to Status ID: " . $active . " for transaction ID: " . $transaction->id);
                Log::channel('update-cashback-points')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Cashback point Status updated to Status ID: " . $active . " for transaction ID: " . $transaction->id);
            }
        } else {
            Log::channel('update-cashback-points')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . ": No Cashback point found for Transaction ID: " . $transaction->id);
        }
    }
}
