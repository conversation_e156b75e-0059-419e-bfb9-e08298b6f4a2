<?php
namespace App\Console\Commands;

use App\Http\Factories\PurchasePower\PurchasePowerFactory;
use App\Models\CustomPurchasePowerTemp;
use App\Models\User;
use App\Models\UserRole;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UpdateConsumerPurchasePower extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:purchasepower';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command updates the purchase power of all the v1 consumer whose custom pp is enabled.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->purchasePower = new PurchasePowerFactory();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->_updatePurchasePower();
    }
    /**
     * This function updates the final status for all the transactions that has been successfully settled
     */
    private function _updatePurchasePower()
    {
        Log::channel('custom-purchase-power')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Fetching all v1 consumers with total pending transaction");
        
        $this->purchasePower->getCalculatedPurchasePower(1);
        // update user table purchase power
        $this->_updatePPintoDB();
    }

    private function _updatePPintoDB()
    {
        // insert into history table
        DB::select("INSERT INTO custom_purchase_power_history SELECT CONCAT(unix_timestamp(),id), consumer_id, old_purchase_power, old_purchase_power_date, total_pending_amount, new_purchase_power, old_weekly_spending_limit, new_weekly_spending_limit, admin_control, created_at, updated_at FROM custom_purchase_power_temp");
        // Update the users table
        DB::select("UPDATE users u INNER JOIN custom_purchase_power_temp cpp ON u.user_id = cpp.consumer_id SET u.purchase_power = cpp.new_purchase_power, u.updated_at = cpp.updated_at WHERE cpp.new_purchase_power IS NOT NULL");
        Log::channel('custom-purchase-power')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Users table updated successfully.");
    }
}
