<?php
namespace App\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class TestS3Upload extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:s3upload';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will test the S3 file upload quickly';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $name = 'http://canpay-centralized-api.test/img/637960.jpg';
        $filePath = 'images/' . time(); // This must be stored in DB so that you can fetch the image from S3 later
        if (Storage::disk('s3')->put($filePath, file_get_contents($name))) { // Uploading file to S3
            $this->info("File Uploaded Successfully.");
            $disk = Storage::disk('s3');
            // Praparing a temporary URL to fetch the image from S3. We are using temporary url so that the url gets invalid after defined minutes, so that the user data got protected.
            $url = $disk->temporaryUrl($filePath, Carbon::now()->addMinutes(intval(env('S3_FILE_EXPIRY_TIME'))), []);
            $this->info("File Path ===> " . $url);
        } else {
            $this->info("File Uploaded Unsuccessful.");
        }
    }
}
