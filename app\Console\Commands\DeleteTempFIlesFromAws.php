<?php
namespace App\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class DeleteTempFIlesFromAws extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'remove:tempfile';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will delete s3 temporary folder files older than today.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->_deleteTempFiles();
    }
    
    private function _deleteTempFiles()
    {
        Log::channel('delete-temp-files')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "===========S3 temporary folder file delete start==========");
        $disk = Storage::disk('s3');
        $all_files = $disk->listContents('temporary_files', true);
        $total_file_deleted = 0;
        if(count($all_files) > 0){
            $currenTimestampt = Carbon::now()->subDays(1)->getTimestamp();
            foreach ($all_files as $file) {
                if ($file['type'] == 'file' && $file['timestamp'] < $currenTimestampt) {
                    $disk->delete($file['path']);
                    $total_file_deleted +=1;
                }
            }
        }
        Log::channel('delete-temp-files')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "===========Total ". $total_file_deleted ." files deleted from S3 temporary folder==========");
        return true;
    }
}
