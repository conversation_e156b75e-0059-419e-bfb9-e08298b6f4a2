<?php
namespace App\Console\Commands;

use App\Http\Clients\Acheck21HttpClient;
use App\Http\Factories\Transaction\TransactionFactory;
use App\Models\TransactionDetails;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PostSetCanPayOffset extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'post:canpayoffset {--transaction_ids=}';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command represents returned transaction through acheck21 which are approved by consumer.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->transaction = new TransactionFactory();
        $this->acheck = new Acheck21HttpClient();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->info("Representing return transactions...");
        $id_array = explode(",", $this->option('transaction_ids'));
        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Posting CanPay Return Recovery transaction to acheck21 for representment");
        $transaction_sum = TransactionDetails::select(DB::raw('sum(transaction_details.amount) as sum'), DB::raw('sum(transaction_details.tip_amount) as tip_sum'))->whereIn('id', $id_array)->first(); // sum query
        if ($transaction_sum->sum != null) {
            $amount = ($transaction_sum->sum + $transaction_sum->tip_sum);
            $params['amount'] = -$amount;
            $params['acheck_account_id'] = env('CANPAY_RETURN_RECOVERY');
            
            if(env('ACHECK_POSTING')){
                //calling the factory function to create consumer transaction into acheck21
                $response = $this->transaction->createCanPayReturnTransaction($params);
                $response_decoded = json_decode($response, true);
                Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is enabled. Posting in ACHECK21.");
            }else{
                $response_decoded['documentId'] = rand(********,********);
                Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is disabled. Continue with a random number.");
            }

            if (isset($response_decoded['documentId'])) {
                //create a new transaction
                $transaction_details = new TransactionDetails();
                $transaction_details->transaction_number = generateTransactionId();
                $transaction_details->entry_type = "Cr";
                $transaction_details->transaction_time = Carbon::now();
                $transaction_details->local_transaction_time = Carbon::now();
                $transaction_details->local_transaction_year = date("Y", strtotime($transaction_details->local_transaction_time));
                $transaction_details->local_transaction_month = date("m", strtotime($transaction_details->local_transaction_time));
                $transaction_details->local_transaction_date = date("Y-m-d", strtotime($transaction_details->local_transaction_time));
                $transaction_details->amount = $transaction_sum->sum;
                $transaction_details->tip_amount = $transaction_sum->tip_sum;
                $transaction_details->status_id = getStatus(PROCESSED_FOR_ACHECK21);
                $transaction_details->transaction_place = ACHECK21;
                $transaction_details->acheck_document_id = $response_decoded['documentId'];
                $transaction_details->is_represented = 1;
                $transaction_details->save();

                //update the status of all the represented returned transactions from returned to pending
                $transactions = TransactionDetails::whereIn('id', $id_array)->get();
                $pending = getStatus(PENDING);
                foreach ($transactions as $transaction) {
                    $transaction->status_id = $pending;
                    $transaction->save();
                }

                Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "CanPay transaction details was stored successfully for amount(without tip): " . $transaction_sum->sum);
                $this->info("CanPay transaction details was stored successfully.");
            } else {
                Log::channel('return-transaction')->error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "There was some problem trying to post transaction into Acheck21.");
                $this->info("There was some problem trying to post transaction into Acheck21.");
            }
        } else {
            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No transactions found.");
            $this->info("No transactions found.");
        }
    }
}
