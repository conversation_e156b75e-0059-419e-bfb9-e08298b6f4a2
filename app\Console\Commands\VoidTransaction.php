<?php

namespace App\Console\Commands;

use App\Http\Clients\Acheck21HttpClient;
use App\Http\Factories\EmailExecutor\EmailExecutorFactory;
use App\Http\Factories\Transaction\TransactionFactory;
use App\Http\Factories\Webhook\WebhookFactory;
use App\Models\Acheck21HistoryTable;
use App\Models\StatusMaster;
use App\Models\TransactionDetails;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class VoidTransaction extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'void:transaction {--transaction_ids=}';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will void Transactions in Pending and Revoked state.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->transaction = new TransactionFactory();
        $this->acheck = new Acheck21HttpClient();
        $this->merchantWebhook = new WebhookFactory();
        $this->emailexecutor = new EmailExecutorFactory();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->info("Voiding Transactions...");
        if ($this->option('transaction_ids') != '') {
            $pending = getStatus(PENDING);
            $void_revoked = getStatus(VOID_REVOKED);
            $process_for_acheck21 = getStatus(PROCESSED_FOR_ACHECK21);
            $id_arr = explode(',', $this->option('transaction_ids'));
            $in = str_repeat('?,', count($id_arr) - 1) . '?';
            $arr = [$process_for_acheck21];
            $prepared_statement_arr = array_merge($arr, $id_arr);

            Log::channel('void-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Void Transaction Started... ");
            $transaction_sql = "SELECT td.id, td1.acheck_document_id, td1.amount, td1.transaction_number, td1.consumer_id, td1.terminal_id, td1.timezone_id, td1.tip_amount, td1.tip_type, td1.tip_add_time, td1.used_qr_id, td1.status_id, td1.transaction_type_id, rmm.acheck_account_id, tz.timezone_name,td.status_id, td.change_request_transaction_ref_no, td.change_request,td.is_ecommerce
            FROM transaction_details td
            JOIN terminal_master tm ON tm.id = td.terminal_id
            JOIN merchant_stores ms ON tm.merchant_store_id = ms.id
            JOIN registered_merchant_master rmm ON rmm.id = ms.merchant_id
            JOIN timezone_masters tz ON tz.id = td.timezone_id
            LEFT JOIN transaction_details td1 FORCE INDEX(idx_trans_ref_no) ON td.id = td1.transaction_ref_no AND td1.status_id = ?
            WHERE td.transaction_ref_no IS NULL AND td.merchant_id IS NULL AND IFNULL(td.consumer_id, '') != '' AND td.is_v1 = 0 AND td.merchant_id IS NULL AND td.id IN (" . $in . ")";
            $transactions = DB::connection(MYSQL_RO)->select($transaction_sql, $prepared_statement_arr);
            if (empty($transactions)) {
                Log::channel('void-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No transactions found. Exiting...");
                return;
            }
            foreach ($transactions as $transaction) {
                if ($transaction->status_id == $pending || $transaction->status_id == $void_revoked) {
                    $not_posted = 0;
                    if ($transaction->acheck_document_id != '') {
                        if (env('ACHECK_POSTING')) {
                            $params['acheck_account_id'] = $transaction->acheck_account_id;
                            //calling the factory function to create consumer transaction into acheck21
                            $params['acheck_document_id'] = $transaction->acheck_document_id;
                            $response = $this->acheck->deleteTransaction($params);
                            Log::channel('void-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is enabled. Posting in ACHECK21.");
                        } else {
                            $response = 204;
                            Log::channel('void-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is disabled. Continue with static 204 response.");
                        }
                    } else {
                        $response = 204;
                        $not_posted = 1;
                        Log::channel('void-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction ID: " . $transaction->id . " not posted to Acheck21.");
                    }

                    if ($response != 204) {
                        $failed = getStatus(FAILED);
                        $history = array(
                            'transaction_id' => $transaction->id,
                            'void_transaction_posting' => 1,
                            'status_id' => $failed,
                        );
                        Acheck21HistoryTable::create($history);
                        Log::channel('void-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Could not void transaction for amount: " . $transaction->amount . " with Transaction ID: " . $transaction->id);
                    } else {
                        $this->voidV2Transaction($transaction, $not_posted);
                        $this->info("Transaction Voided for Transaction ID: " . $transaction->id);
                        Log::channel('void-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction for amount: " . $transaction->amount . " with Transaction ID: " . $transaction->id . " voided successfully: ");
                    }
                } else {
                    $this->info("Transaction ID: " . $transaction->id . " cannot be voided as Status ID is: " . $transaction->status_id);
                    Log::channel('void-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction ID: " . $transaction->id . " cannot be voided as Status ID is: " . $transaction->status_id);
                }
            }
        } else {
            $this->info("Transaction ID not given.");
        }
    }

    /**
     * voidV2Transaction
     * This function will void a non settled V2 transaction
     * @param  mixed $params
     * @return void
     */
    public function voidV2Transaction($transaction, $not_posted)
    {
        Log::channel('void-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Void Transaction ID: " . $transaction->id);
        DB::beginTransaction();
        try {
            $transaction_details = TransactionDetails::find($transaction->id);
            $voided_status = StatusMaster::where('code', VOIDED)->first();
            if ($transaction->status_id == $voided_status->id && $transaction->change_request == 0) {
                Log::channel('void-transaction')->error(__METHOD__ . "(Line: " . __LINE__ . ") - " . "Transaction already voided. Transaction ID: " . $transaction->id);
                DB::rollback();
            }
            $transaction_details->status_id = $voided_status->id;
            $transaction_details->voided_time = Carbon::now();
            $transaction_details->change_request = 0;
            $transaction_details->voided_local_time = Carbon::now()->timezone($transaction->timezone_name);
            $transaction_details->save();
            // check modifield transction and cancel the modified transaction
            if ($transaction->change_request_transaction_ref_no != null) {
                $m_transaction = TransactionDetails::find($transaction->change_request_transaction_ref_no);
                $m_transaction->status_id = $voided_status->id;
                $m_transaction->voided_time = Carbon::now();
                $m_transaction->voided_local_time = Carbon::now()->timezone($transaction->timezone_name);
                $m_transaction->save();
            }

            if ($not_posted == 0) {
                $this->_saveTransaction($transaction);
            }

            // Revert back the reward amount and reward amount back to the consumer as the transaction is voided
            addCreditAfterTransactionVoid($transaction);

            // Revert Back the Points Against the Transaction
            revertPointsAganistTransaction($transaction, 1);

            // Void Lottery Rewards if the User has won Lottery
            voidRevokeLotteryReward($transaction, 1);
            DB::commit();

            // Sending Transaction Activity to the consumer
            $email_params = [
                'user_id' => $transaction->consumer_id,
                'transaction_id' => $transaction->id,
            ];

            $this->emailexecutor->consumerVoidTransaction($email_params);
            if ($transaction->is_ecommerce) {
                $previousTransactionId = $transaction->change_request_transaction_ref_no ? $transaction->change_request_transaction_ref_no : $transaction->id;
                //webhook call after voided transaction
                $this->merchantWebhook->modificationWebhookCall($previousTransactionId, $voided_status->status);
            }

            Log::channel('void-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction voided successfully for transaction id: " . $transaction->id);
        } catch (\Exception $e) {
            Log::channel('void-transaction')->error(__METHOD__ . "(Line: " . __LINE__ . ") - " . "Exception while voiding Transaction ID: " . $transaction->id, ['Exception' => $e]);
            DB::rollback();
        }
    }

    private function _saveTransaction($transaction)
    {
        $voided = getStatus(VOIDED);
        //create a new transaction
        $transaction_details = new TransactionDetails();
        $transaction_details->transaction_number = $transaction->transaction_number;
        $transaction_details->transaction_ref_no = $transaction->id;
        $transaction_details->consumer_id = $transaction->consumer_id;
        $transaction_details->terminal_id = $transaction->terminal_id;
        $transaction_details->transaction_time = Carbon::now();
        $transaction_details->local_transaction_time = Carbon::now($transaction->timezone_name);
        $transaction_details->local_transaction_year = date("Y", strtotime($transaction_details->local_transaction_time));
        $transaction_details->local_transaction_month = date("m", strtotime($transaction_details->local_transaction_time));
        $transaction_details->local_transaction_date = date("Y-m-d", strtotime($transaction_details->local_transaction_time));
        $transaction_details->timezone_id = $transaction->timezone_id;
        $transaction_details->amount = $transaction->amount;
        $transaction_details->tip_amount = $transaction->tip_amount;
        $transaction_details->tip_type = $transaction->tip_type;
        $transaction_details->tip_add_time = $transaction->tip_add_time;
        $transaction_details->used_qr_id = $transaction->used_qr_id;
        $transaction_details->status_id = $voided;
        $transaction_details->transaction_type_id = $transaction->transaction_type_id;
        $transaction_details->transaction_place = ACHECK21;
        $transaction_details->save();
    }
}
