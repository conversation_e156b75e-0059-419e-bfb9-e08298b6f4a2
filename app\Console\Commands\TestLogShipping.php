<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class TestLogShipping extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:logshipping';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will print a log to test if it is being shipped to Logz.io';
    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->info("This log serves as a test for shipping to logz.io.");
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "This log serves as a test for shipping to logz.io.");
       
    }


}
