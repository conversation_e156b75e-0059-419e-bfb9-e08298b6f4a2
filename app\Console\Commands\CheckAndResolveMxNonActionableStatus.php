<?php

namespace App\Console\Commands;

use App\Http\Clients\MxHttpClient;
use App\Http\Factories\Firebase\FirebaseFactory;
use App\Http\Factories\Mx\MxFactory;
use App\Models\MxProblematicStatusMaster;
use App\Models\UserBankAccountInfo;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CheckAndResolveMxNonActionableStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'check:mx-non-actionable-status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "This command will check if consumer's non-actionable status resolved or not.";

    /**
     * MxHttpClient instance
     *
     * @var MxHttpClient
     */
    protected $mxClient;

    /**
     * MxFactory instance
     *
     * @var MxFactory
     */
    protected $mxFactory;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->mxClient = new MxHttpClient();
        $this->mxFactory = new MxFactory();
        $this->firebase = new FirebaseFactory();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Log::info('Checking started for consumer non-actionable status has been resolved or not...');

        // Fetch all bank accounts with non-actionable status
        $getMemberIdsWithNonActionableStatus = UserBankAccountInfo::where('mx_non_actionable_status_detected', '=', 1)
            ->groupBy('mx_member_guid')
            ->get();

        // Iterate through each bank detail with non-actionable status
        foreach ($getMemberIdsWithNonActionableStatus as $bank_detail) {

            $params['user_guid'] = $bank_detail->mx_consumer_id;
            $params['member_guid'] = $bank_detail->mx_member_guid;

            Log::info('Fetching member status for Consumer ID: ' . $bank_detail->user_id . ' with Member GUID: ' . $params['member_guid']);

            // Get the latest member status
            $memberStatus = $this->mxClient->getMemberStatus($params);
            $memberStatusData = json_decode($memberStatus, true);

            // Check if member data exists in the response
            if (isset($memberStatusData['member'])) {
                $memberStatus = $memberStatusData['member'];

                // Check if the member is connected and not being aggregated
                if ($memberStatus['connection_status'] == MX_CONNECTED && $memberStatus['is_being_aggregated'] == false) {

                    Log::info('Non-actionable status resolved for Consumer ID: ' . $bank_detail->user_id . ' with Member GUID: ' . $params['member_guid']);

                    // Update the user_bank_account_info table to mark as non-actionable status resolved
                    UserBankAccountInfo::where('mx_member_guid', $bank_detail->mx_member_guid)->update(['mx_non_actionable_status_detected' => 0]);

                    Log::info('Calling check balance API for Consumer ID: ' . $bank_detail->user_id . ' with Member GUID: ' . $params['member_guid']);

                    $bank_detail->mx_non_actionable_status_detected = 0; // Set it 0 as the problematic status is not there anymore
                    // Call the check balance API as the member is connected and the banking issue has been resolved
                    $this->mxFactory->checkBalance($bank_detail, $bank_detail->user_id, MX_MEMBER_STATUS_CHECK_SCHEDULER);

                    // Update the consumer as resolved in firebase
                    $data = [
                        'user_id' => $bank_detail->user_id,
                        'mx_non_actionable_status_resolved' => 1,
                    ];
                    // Update Bank Delink status in Firebase
                    $this->firebase->storeMxNonActionableStatusResolved($data);
                } else {
                    // Check if the connection status has been moved into problematic status
                    $problemStatus = MxProblematicStatusMaster::where('status_name', strtolower($memberStatus['connection_status']))->where('is_user_actionable', 1)->first();
                    if ($problemStatus) {
                        Log::info('Member status modev from non actionable to actionable status for Consumer ID: ' . $bank_detail->user_id . ' with Member GUID: ' . $params['member_guid'] . '. Connection status: ' . $memberStatus['connection_status']);
                        handleProblemStatuses($memberStatus, $bank_detail->user_id, $bank_detail, 'Check Member Status');
                        // Update Bank Delink status in Firebase
                        $data = [
                            'user_id' => $bank_detail->user_id,
                            'mx_non_actionable_status_resolved' => 1,
                        ];
                        $this->firebase->storeMxNonActionableStatusResolved($data);
                    }
                    Log::info('Member status not resolved for Consumer ID: ' . $bank_detail->user_id . ' with Member GUID: ' . $params['member_guid'] . '. Connection status: ' . $memberStatus['connection_status']);
                }
            } else {
                Log::info('Member status not found for Consumer ID: ' . $bank_detail->user_id . ' with Member GUID: ' . $params['member_guid']);
            }
        }

        Log::info('Checking ended for consumer non-actionable status has been resolved.');
    }
}
