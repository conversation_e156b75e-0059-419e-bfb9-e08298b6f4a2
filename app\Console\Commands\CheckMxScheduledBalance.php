<?php

namespace App\Console\Commands;

use App\Http\Factories\Mx\MxFactory;
use App\Models\BankAccountInfo;
use App\Models\MxCheckBalanceCallHistory;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CheckMxScheduledBalance extends Command
{
    protected $signature = 'check:mx-scheduled-balance';
    protected $description = 'Check balance for scheduled balance fetches within the last 30 minutes';

    public function __construct()
    {
        parent::__construct();
        $this->mxFactory = new MxFactory();
    }

    public function handle()
    {
        $this->info('Starting scheduled balance checks...');
        Log::channel('mx-scheduled-balance-check')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Starting scheduled balance checks...");
        $now = Carbon::now();
        $thirtyMinutesAgo = $now->copy()->subMinutes(30);

        $records = MxCheckBalanceCallHistory::where('source', SCHEDULED_BALANCE_FETCH)
            ->whereNull('completed_at')
            ->whereBetween('created_at', [$thirtyMinutesAgo, $now])
            ->get();

        $this->info('Records found: ' . $records->count());
        Log::channel('mx-scheduled-balance-check')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Records found: " . $records->count());

        foreach ($records as $record) {
            // Fetch the account details
            $account_details = BankAccountInfo::find($record->account_id);
            Log::channel('mx-scheduled-balance-check')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Balance check started for MX check balance table ID: " . $record->id . " - Account ID: " . $record->account_id . " - User ID: " . $account_details->user_id);
            $this->mxFactory->checkBalance($account_details, $account_details->user_id, SCHEDULED_BALANCE_FETCH, 1, $record);
        }

        $this->info('Scheduled balance checks completed.');
        Log::channel('mx-scheduled-balance-check')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Scheduled balance checks completed.");
    }
}
