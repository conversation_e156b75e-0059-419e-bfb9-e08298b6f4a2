<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Rename 'email' to 'recipient'
        DB::statement("ALTER TABLE `petition_share_details` CHANGE COLUMN if EXISTS `email` `recipient` VARCHAR(100) DEFAULT NULL");

        // Add 'type' column after 'consumer_id'
        DB::statement("ALTER TABLE `petition_share_details` ADD IF NOT EXISTS `type` ENUM('email','phone') NOT NULL AFTER `consumer_id`");
    
    }

};
