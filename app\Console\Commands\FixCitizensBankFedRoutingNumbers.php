<?php
namespace App\Console\Commands;

use App\Http\Clients\FedHttpClient;
use App\Models\BankAccountInfo;
use App\Models\FinancialInstitutionMaster;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class FixCitizensBankFedRoutingNumbers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:fedroutingnumbersCitizensBank';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will fix fedroutingnumbers table data for Citizens Bank.';

    private $fedClient;

    /**
     * Create a new command instance
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->fedClient = new FedHttpClient();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Log::channel('fed-routing-no')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Fixing routing numbers from Fed started for Citizens Bank...");
        $this->info("Fixing routing numbers from Fed started for Citizens Bank...");
        
        $citizens_banks = FinancialInstitutionMaster::where('bank_name', 'like', 'Citizens Bank%')->get();
        $response_routing_array = $this->fedClient->getFedwire(); // Call the client to receive response
        if ($response_routing_array && $citizens_banks) {

            foreach ($citizens_banks as $citizens_bank) {
               // Filter the $jsonData array based on the routing numbers
                $filteredData = array_filter($response_routing_array, function($item) use ($citizens_bank) {
                    return $item['routingNumber'] == $citizens_bank->routing_no;
                });
                // Reindex the array to reset keys
                $old_bank_name = $citizens_bank->bank_name;
                $filteredNewData = array_values($filteredData);
                if (!empty($filteredNewData)) {
                    $citizens_bank->bank_name = $filteredNewData[0]['customerName'];
                    $citizens_bank->is_akoya = 0;
                    $citizens_bank->is_mx = 0;
                    $citizens_bank->akoya_provider_id = null;
                    $citizens_bank->mx_institution_code = null;
                    $citizens_bank->save();
                    Log::channel('fed-routing-no')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Bank name updated successfully. ID: " . $citizens_bank->id . " Old Bank Name " . $old_bank_name . " New Bank Name " . $citizens_bank->bank_name);
                    $this->info("Bank name updated successfully. ID: " . $citizens_bank->id . " Old Bank Name " . $old_bank_name . " New Bank Name " . $citizens_bank->bank_name);
                }
            }
        }
        Log::channel('fed-routing-no')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Fixing of Bank Name completed.");
        $this->info("Fixing of Bank Name completed.");
    }
}
