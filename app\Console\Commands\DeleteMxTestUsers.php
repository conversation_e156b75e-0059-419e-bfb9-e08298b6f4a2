<?php

namespace App\Console\Commands;

use App\Http\Factories\Mx\MxFactory;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class DeleteMxTestUsers extends Command
{
    protected $signature = 'delete:mxtestusers';

    protected $description = 'Fetch and delete users from MX API';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->mxFactory = new MxFactory();
    }

    public function handle()
    {
        if (ENV('APP_ENV') == 'production') {
            $this->error("Application is in production. This command is only for sandbox environment.");
            Log::error(__METHOD__ . "(" . __LINE__ . ") - " . "Application is in production. This command is only for sandbox environment.");
            return;
        }

        $params['page'] = 1;

        while (true) {
            $response = $this->mxFactory->getUserList($params); // Fetch the user list from MX
            if (!$response) {
                $this->error("Failed to fetch users from the API. Error: " . $response->status());
                Log::error(__METHOD__ . "(" . __LINE__ . ") - " . "Failed to fetch users from the API. Error: " . $response->status());
                return;
            }

            $users = $response['users'];

            foreach ($users as $user) {
                $params['user_guid'] = $user['guid'];
                $deleteResponse = $this->mxFactory->deleteUser($params); // Delete the user from MX

                if ($deleteResponse == 204) {
                    $this->info("User with GUID " . $params['user_guid'] . " deleted successfully.");
                    Log::info(__METHOD__ . "(" . __LINE__ . ") - " . "User with GUID " . $params['user_guid'] . " deleted successfully.");
                } else {
                    $this->error("Failed to delete user with GUID " . $params['user_guid'] . ". Error: " . $deleteResponse);
                    Log::error(__METHOD__ . "(" . __LINE__ . ") - " . "Failed to delete user with GUID " . $params['user_guid'] . ". Error: " . $deleteResponse);
                }
            }

            if ($response['pagination']['current_page'] < $response['pagination']['total_pages']) {
                $params['page']++;
            } else {
                break; // All entries are loaded, exit the loop
            }
        }

        $this->info('All users deleted successfully.');
        Log::info(__METHOD__ . "(" . __LINE__ . ") - " . 'All users deleted successfully.');
    }
}
