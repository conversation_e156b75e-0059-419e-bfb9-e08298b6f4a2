<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::Statement("CREATE TABLE IF NOT EXISTS `petitions` (
            `id` varchar(40) NOT NULL,
            `petition_number` varchar(50) NOT NULL,
            `consumer_id` varchar(40) NOT NULL,
            `store_name` varchar(255) NOT NULL,
            `street_address` varchar(255) DEFAULT NULL,
            `apt_number` varchar(50) DEFAULT NULL,
            `city` varchar(50) DEFAULT NULL,
            `state` varchar(50) DEFAULT NULL,
            `zipcode` varchar(50) DEFAULT NULL,
            `primary_contact_person_name` varchar(50) DEFAULT NULL,
            `primary_contact_person_phone` varchar(50) DEFAULT NULL,
            `primary_contact_person_email` varchar(50) DEFAULT NULL,
            `secondary_contact_person_name` varchar(50) DEFAULT NULL,
            `secondary_contact_person_phone` varchar(50) DEFAULT NULL,
            `secondary_contact_person_email` varchar(50) DEFAULT NULL,
            `status_id` varchar(50) NOT NULL,
            `onboarded_date` date DEFAULT NULL,
            `merchant_store_id` varchar(50) DEFAULT NULL,
            `created_at` datetime NOT NULL,
            `updated_at` datetime DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `fk_consumer_id_petitions_idx` (`consumer_id`),
            KEY `fk_merchant_store_id_petitions_idx` (`merchant_store_id`),
            CONSTRAINT `fk_consumer_id_petitions_idx` FOREIGN KEY (`consumer_id`) REFERENCES `users` (`user_id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
            CONSTRAINT `fk_merchant_store_id_petitions_idx` FOREIGN KEY (`merchant_store_id`) REFERENCES `merchant_stores` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION
          ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci");

        DB::Statement("CREATE TABLE IF NOT EXISTS `petition_details` (
            `id` varchar(40) NOT NULL,
            `petition_id` varchar(40) NOT NULL,
            `consumer_id` varchar(40) DEFAULT NULL,
            `signed_from` varchar(40) DEFAULT NULL,
            `type` enum('mayor','crew leader','crew member') NOT NULL,
            `deleted_at` datetime DEFAULT NULL,
            `changed_at` datetime DEFAULT NULL,
            `created_at` datetime NOT NULL,
            `updated_at` datetime DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `fk_petition_details_petition_id_idx` (`petition_id`),
            KEY `fk_petition_details_consumer_id_idx` (`consumer_id`),
            KEY `fk_petition_details_signed_from_idx` (`signed_from`),
            CONSTRAINT `fk_petition_details_consumer_id_idx` FOREIGN KEY (`consumer_id`) REFERENCES `users` (`user_id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
            CONSTRAINT `fk_petition_details_petition_id_idx` FOREIGN KEY (`petition_id`) REFERENCES `petitions` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
            CONSTRAINT `fk_petition_details_signed_from_idx` FOREIGN KEY (`signed_from`) REFERENCES `users` (`user_id`) ON DELETE NO ACTION ON UPDATE NO ACTION
          ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci");

        DB::Statement("CREATE TABLE IF NOT EXISTS `petition_share_details` (
            `id` varchar(40) NOT NULL,
            `petition_id` varchar(40) NOT NULL,
            `consumer_id` varchar(40) DEFAULT NULL,
            `email` VARCHAR(40) NULL DEFAULT NULL,
            `created_at` datetime DEFAULT NULL,
            `updated_at` datetime NOT NULL,
            PRIMARY KEY (`id`),
            KEY `fk_petition_share_details_petition_id_idx` (`petition_id`),
            KEY `fk_petition_share_details_consumer_id_idx` (`consumer_id`),
            CONSTRAINT `fk_petition_share_details_consumer_id_idx` FOREIGN KEY (`consumer_id`) REFERENCES `users` (`user_id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
            CONSTRAINT `fk_petition_share_details_petition_id_idx` FOREIGN KEY (`petition_id`) REFERENCES `petitions` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION
          ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci");

        DB::Statement("ALTER TABLE `merchant_stores`
        ADD COLUMN IF NOT EXISTS `petition_id` VARCHAR(40) DEFAULT NULL AFTER `merchant_id`");

        DB::statement("CREATE TABLE IF NOT EXISTS `petition_detail_histories` (
            `id` varchar(40) NOT NULL,
            `petition_id` varchar(40) NOT NULL,
            `consumer_id` varchar(40) DEFAULT NULL,
            `type` enum('mayor','crew leader','crew member') NOT NULL,
            `action` VARCHAR(255) NOT NULL,
            `created_at` datetime NOT NULL,
            `updated_at` datetime DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `fk_petition_detail_histories_petition_id_idx` (`petition_id`),
            KEY `fk_petition_detail_histories_consumer_id_idx` (`consumer_id`),
            CONSTRAINT `fk_petition_detail_histories_consumer_id_idx` FOREIGN KEY (`consumer_id`) REFERENCES `users` (`user_id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
            CONSTRAINT `fk_petition_detail_histories_petition_id_idx` FOREIGN KEY (`petition_id`) REFERENCES `petitions` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION
          ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci");
    }
};
