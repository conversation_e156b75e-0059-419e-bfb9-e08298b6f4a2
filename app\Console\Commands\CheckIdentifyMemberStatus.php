<?php

namespace App\Console\Commands;

use App\Http\Factories\Mx\MxFactory;
use App\Models\MxIdentifyMemberCallHistory;
use App\Models\UserBankAccountInfo;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CheckIdentifyMemberStatus extends Command
{
    protected $signature = 'check:mx-identify-member-status';
    protected $description = 'Check identify member status within the last minute';

    public function __construct()
    {
        parent::__construct();
        $this->mxFactory = new MxFactory();
    }

    public function handle()
    {
        $this->info('Starting identify member checks...');
        Log::channel('mx-identify-member-status')->info("Starting identify member checks...");

        $now = Carbon::now();
        // Get the time 3 minutes ago
        $threeMinutesAgo = $now->copy()->subMinutes(3);

        $records = MxIdentifyMemberCallHistory::whereNull('completed_at')
            ->whereBetween('created_at', [$threeMinutesAgo, $now])
            ->get();
        $this->info('Records found: ' . $records->count());
        Log::channel('mx-identify-member-status')->info("Records found: " . $records->count());

        foreach ($records as $record) {
            Log::channel('mx-identify-member-status')->info("Identify member status check started for MX identify member table ID: " . $record->id . " - User ID: " . $record->consumer_id);

            try {
                // Fetch the account details
                $bank_details = UserBankAccountInfo::where(['mx_consumer_id' => $record->mx_user_id, 'mx_member_guid' => $record->mx_member_id])->first();

                if ($bank_details) {
                    $bank_details->check_member_status = 1;
                    $bank_details->skip_identify_member_call = 1; // Skip the paid identity call
                    $bank_details->record_id = $record->id;
                    $bank_details->starttime = microtime(true);

                    // Call the identify member API
                    $response = $this->mxFactory->identifyMember($bank_details);
                    $status = $response['status'];

                    if ($status === 'success') {
                        Log::channel('mx-identify-member-status')->info("Account owner details fetched successfully for Consumer ID: " . $record->consumer_id);
                    } elseif ($status === 'pending') {
                        Log::channel('mx-identify-member-status')->info("Member aggregation is still pending for Consumer ID: " . $record->consumer_id);
                    } else {
                        Log::channel('mx-identify-member-status')->error("Failed to fetch account owner details: " . $response['message'] . " for Consumer ID: " . $record->consumer_id);
                    }

                    // Update the retry count
                    $record->retry_count += 1;
                    $record->save();
                } else {
                    Log::channel('mx-identify-member-status')->warning("No bank details found for MX identify member table ID: " . $record->id . " - User ID: " . $record->consumer_id);
                }
            } catch (\Exception $e) {
                Log::channel('mx-identify-member-status')->error("Error processing MX identify member table ID: " . $record->id . " - User ID: " . $record->consumer_id . ". Error: " . $e->getMessage());
            }
        }

        $this->info('Scheduled identify member checks completed.');
        Log::channel('mx-identify-member-status')->info("Scheduled identify member checks completed.");
    }
}
