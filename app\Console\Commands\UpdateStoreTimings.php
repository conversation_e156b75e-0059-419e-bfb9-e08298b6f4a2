<?php
namespace App\Console\Commands;

use App\Http\Clients\GooglePlacesHttpClient;
use App\Models\MerchantStores;
use App\Models\MapsStoreTiming;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class UpdateStoreTimings extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:storetimings {--store_ids=}';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will update the store timings from Google Places API';
    private $chunkSize = 500;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->googleplaces = new GooglePlacesHttpClient();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Log::channel('update-store-timing')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Store timing update process started...");

        // Fetching all active stores with sync disabled falg down
        $query = MerchantStores::join('status_master', 'merchant_stores.status', '=', 'status_master.id')
        ->leftJoin('maps_store_timing', function($join) {
            $join->on('merchant_stores.id', '=', 'maps_store_timing.store_id');
        })
        ->select('merchant_stores.*')
        ->whereNull('maps_store_timing.store_id');
        if ($this->option('store_ids') != '') {
            $store_ids = explode(',', $this->option('store_ids'));
            $query->whereIn('merchant_stores.id', $store_ids);
        }
        $stores = $query->where(['status_master.code' => ACTIVE])->get();



        if(!empty($stores)){
            $queue = [];
            $store_ids = [];
            foreach($stores as $store){
                $address = $store->retailer. ' '. $store->address;
                $placeIdResponse = $this->googleplaces->findplacefromtext($address);
                $place_id_result = json_decode($placeIdResponse, true);
                if (!empty($place_id_result['candidates'])) {
                    $place_id = $place_id_result['candidates'][0]['place_id'];
                    Log::channel('update-store-timing')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Place ID ".$place_id." fetched for Store ID: ". $store->id);
                    // Update map place ID in DB
                    MerchantStores::where('id', $store->id)->update(['map_place_id' => $place_id]);

                    // Get the store timing from the place_id
                    $timingsResponse = $this->googleplaces->getTimings($place_id);
                    $timings_result = json_decode($timingsResponse, true);
                    if(!empty($timings_result['result'])){
                        Log::channel('update-store-timing')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Timing details fetched for Store ID: ". $store->id .". Preparing data for batch insert.");
                        $data = [];
                        if (isset($timings_result['result']['opening_hours'])) {
                            $week_days = [];
                            foreach($timings_result['result']['opening_hours']['periods'] as $timings){
                                try {
                                    // Prepare the data for batch insert
                                    $day_name = jddayofweek($timings['open']['day'] -1 , 1); // Get the weekday as a string from a Julian Day number
                                    // check duplicate day
                                    if (!in_array($day_name, $week_days)) {
                                        $data['id'] = generateUUID();
                                        $data['store_id'] = $store->id;
                                        $data['day'] = $day_name;
                                        $data['open_time'] = date('H:i:s', strtotime($timings['open']['time']));
                                        $data['close_time'] = date('H:i:s', strtotime($timings['close']['time']));
                                        $data['ecommerce_close_time'] = date('H:i:s', strtotime($data['close_time'].'-1 hour'));
                                        $data['created_at'] = Carbon::now();
                                        $data['updated_at'] = Carbon::now();
                                        $queue[] = $data; //Push the data in main array
                                        $store_ids[] = $store->id;
                                        $week_days[] = $day_name;
                                    }
                                } catch (\Exception $e) {
                                    Log::channel('update-store-timing')->error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured during store timing updation for Store ID: ".$store->id."", [EXCEPTION => $e]);
                                    continue;
                                }
                            }
                            if (count($queue) >= $this->chunkSize) {
                                // Delete previous data
                                MapsStoreTiming::whereIn('store_id', $store_ids)->delete();
                                // Insert data in batch mode
                                MapsStoreTiming::insert($queue);
                                $this->info("Inserted " . count($queue) . " Store timing");
                                Log::channel('update-store-timing')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . count($queue) . "  Store timing insertion completed.");
                                $queue = [];
                                $store_ids = [];
                            }
                        }
                    }else{
                        Log::channel('update-store-timing')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Timing details not found for Store ID: ". $store->id .". Skipping process for this store.");
                    }
                }else{
                    Log::channel('update-store-timing')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Place ID not found for Store ID: ". $store->id .". Skipping process for this store.");
                }

            }
            if (!empty($queue)) {
                // Delete previous data
                MapsStoreTiming::whereIn('store_id', $store_ids)->delete();
                // Insert data in batch mode
                MapsStoreTiming::insert($queue);
                $this->info("Inserted Left Over " . count($queue) . " Store timing");
                Log::channel('update-store-timing')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Loaded left over " . count($queue) . "  Store timing insertion completed.");
            }
            Log::channel('update-store-timing')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Database insertion completed. All store timings are updated successfully.");
        }
    }
}
