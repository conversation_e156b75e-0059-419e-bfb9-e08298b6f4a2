<?php

namespace App\Console\Commands;

use App\Models\TransactionDetails;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class SettleTransactionsPaidWithFullPoints extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'settle:transactionswithfullpoints';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will settle all the transactions done with full points.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // Get the last date when merchant or canpay transaction amount posted in acheck21
        $lastDateOfCanpayPosting = TransactionDetails::where('isCanpay', 1)->orderBy('local_transaction_time', 'DESC')->first();
        $transaction_date = Carbon::parse($lastDateOfCanpayPosting->created_at)->subDays(1)->toDateString();
        Log::channel('settle-transactions-with-points-only')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Scheduler for settling all the transactions done wih full points started for transaction date " . $transaction_date);
        $this->info("Scheduler for settling all the transactions done wih full points started for transaction date " . $transaction_date);

        // Get Pending and Success status
        $pending_status = getStatus(PENDING);
        $success_status = getStatus(SUCCESS);

        // Fetch all the transactions that are paid with points only
        $transactions = TransactionDetails::join('timezone_masters', 'timezone_masters.id', '=', 'transaction_details.timezone_id')->select('transaction_details.*', 'timezone_masters.timezone_name')->whereNull('transaction_details.transaction_ref_no')->where(['transaction_details.local_transaction_date' => $transaction_date, 'transaction_details.isCanpay' => 0, 'transaction_details.consumer_bank_posting_amount' => 0, 'transaction_details.status_id' => $pending_status])->where('transaction_details.reward_amount_used', '>', 0)->get();

        Log::channel('settle-transactions-with-points-only')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Total " . count($transactions) . " number of transactions will be settled.");
        $this->info("Total " . count($transactions) . " number of transactions will be settled.");

        if (sizeof($transactions) != 0) {
            foreach ($transactions as $transaction) {
                // Update the parent transaction
                TransactionDetails::where('id', $transaction->id)->update(['status_id' => $success_status]);
                // Create a new child row with success status
                $transaction_details = new TransactionDetails();
                $transaction_details->transaction_number = $transaction->transaction_number;
                $transaction_details->transaction_ref_no = $transaction->id;
                $transaction_details->user_id = $transaction->user_id;
                $transaction_details->consumer_id = $transaction->consumer_id;
                $transaction_details->terminal_id = $transaction->terminal_id;
                $transaction_details->transaction_time = Carbon::now();
                $transaction_details->local_transaction_time = Carbon::now($transaction->timezone_name);
                $transaction_details->local_transaction_year = date("Y", strtotime($transaction_details->local_transaction_time));
                $transaction_details->local_transaction_month = date("m", strtotime($transaction_details->local_transaction_time));
                $transaction_details->local_transaction_date = date("Y-m-d", strtotime($transaction_details->local_transaction_time));
                $transaction_details->timezone_id = $transaction->timezone_id;
                $transaction_details->consumer_bank_posting_amount = $transaction->consumer_bank_posting_amount;
                $transaction_details->reward_amount_used = $transaction->reward_amount_used;
                $transaction_details->reward_point_used = $transaction->reward_point_used;
                $transaction_details->return_representment_reward_deduction_amount = $transaction->return_representment_reward_deduction_amount;
                $transaction_details->amount = $transaction->amount;
                $transaction_details->tip_amount = $transaction->tip_amount;
                $transaction_details->tip_type = $transaction->tip_type;
                $transaction_details->tip_add_time = $transaction->tip_add_time;
                $transaction_details->used_qr_id = $transaction->used_qr_id;
                $transaction_details->status_id = $success_status;
                $transaction_details->transaction_type_id = $transaction->transaction_type_id;
                $transaction_details->save();
                Log::channel('settle-transactions-with-points-only')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction settled amounting " . $transaction->amount . " with transaction id " . $transaction->id);
                $this->info("Transaction settled amounting " . $transaction->amount . " with transaction id " . $transaction->id);
            }
        } else {
            Log::channel('settle-transactions-with-points-only')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No transactions found that are paid with points only for transaction date " . $transaction_date . ". Exiting...");
            $this->info("No transactions found that are paid with points only for transaction date " . $transaction_date . ". Exiting...");
        }
    }
}
