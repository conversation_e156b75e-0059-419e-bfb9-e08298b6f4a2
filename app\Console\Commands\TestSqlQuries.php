<?php
namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class TestSqlQuries extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:sql';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will test the SQL queries quickly';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $start_time = microtime(true);
        $documentId = '148442161';
        $transaction_sql = "select * from
        transaction_details td force index(idx_acheck_doc_id)
        straight_join transaction_details td1 on td1.id = td.transaction_ref_no
        where td.acheck_document_id = '" . $documentId . "'
        limit 1;";
        $transaction = DB::select($transaction_sql);
        print_r($transaction);
        $end_time = microtime(true);
        $this->info(number_format($end_time - $start_time, 3));
    }
}
