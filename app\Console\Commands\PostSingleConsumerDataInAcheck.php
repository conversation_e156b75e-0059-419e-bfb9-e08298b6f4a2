<?php

namespace App\Console\Commands;

use App\Http\Factories\Finicity\FinicityFactory;
use App\Http\Factories\Transaction\TransactionFactory;
use App\Models\Acheck21HistoryTable;
use App\Models\TransactionDetails;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Symfony\Component\Console\Question\ConfirmationQuestion;
use Symfony\Component\Console\Style\SymfonyStyle;

class PostSingleConsumerDataInAcheck extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'post:singletransactiontoacheck {--transaction_id=}';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will post a single transactions to acheck21 for further processing into the bank.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->finicityFactory = new FinicityFactory();
        $this->transaction = new TransactionFactory();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $transaction_id = $this->option('transaction_id');
        $this->_postTransaction($transaction_id);
    }
    /**
     * This function fetches bank balance for all consumers who has added bank from finicity and keeps record in the database
     */
    private function _postTransaction($transaction_id)
    {
        $this->info("Fetching the transactions to post into acheck21...");
        Log::channel('post-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "===========POST TRANSACTION TO ACHECK21 CRON STARTED RUNNING==========");

        //check if transactions before today were posted in acheck21 or not
        $transaction = TransactionDetails::join('terminal_master', 'terminal_master.id', '=', 'transaction_details.terminal_id')->join('merchant_stores', 'terminal_master.merchant_store_id', '=', 'merchant_stores.id')->join('registered_merchant_master', 'registered_merchant_master.id', '=', 'merchant_stores.merchant_id')->join('timezone_masters', 'timezone_masters.id', '=', 'transaction_details.timezone_id')->select('transaction_details.*', 'registered_merchant_master.acheck_account_id')->where('transaction_details.id', $transaction_id)->first();

        $io = new SymfonyStyle($this->input, $this->output);
        $question = new ConfirmationQuestion('Are you sure you want to proceed transaction with amount: ' . $transaction->consumer_bank_posting_amount . '?', false);
        if ($io->askQuestion($question)) {
            $this->_postTransactionIntoAcheck21($transaction, false);
            Log::channel('post-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "===========POST TRANSACTION TO ACHECK21 CRON FINISHED RUNNING==========");
            $this->info("Transaction posted successfully.");
        } else {
            $this->info('Command cancelled.');
        }
    }

    /**
     * This function calls the api that posts consumer end transactions to acheck21
     * once the response returned from acheck21 it shores a new row to transaction_details table
     */
    private function _createConsumerTransaction($transaction, $data)
    {
        //create a new transaction
        $transaction_details = new TransactionDetails();
        $transaction_details->transaction_number = $transaction->transaction_number;
        $transaction_details->transaction_ref_no = $transaction->id;
        $transaction_details->user_id = $transaction->user_id;
        $transaction_details->consumer_id = $transaction->consumer_id;
        $transaction_details->terminal_id = $transaction->terminal_id;
        $transaction_details->transaction_time = $data['transaction_time'];
        $transaction_details->local_transaction_time = $data['transaction_local_time'];
        $transaction_details->local_transaction_year = date("Y", strtotime($transaction_details->local_transaction_time));
        $transaction_details->local_transaction_month = date("m", strtotime($transaction_details->local_transaction_time));
        $transaction_details->local_transaction_date = date("Y-m-d", strtotime($transaction_details->local_transaction_time));
        $transaction_details->scheduled_posting_date = $transaction->scheduled_posting_date;
        $transaction_details->timezone_id = $transaction->timezone_id;
        $transaction_details->consumer_bank_posting_amount = $transaction->consumer_bank_posting_amount;
        $transaction_details->reward_amount_used = $transaction->reward_amount_used;
        $transaction_details->reward_point_used = $transaction->reward_point_used;
        $transaction_details->amount = $transaction->amount;
        $transaction_details->tip_amount = $transaction->tip_amount;
        $transaction_details->tip_type = $transaction->tip_type;
        $transaction_details->tip_add_time = $transaction->tip_add_time;
        $transaction_details->used_qr_id = $transaction->used_qr_id;
        $transaction_details->status_id = getStatus(PROCESSED_FOR_ACHECK21);
        $transaction_details->transaction_type_id = $transaction->transaction_type_id;
        $transaction_details->transaction_place = ACHECK21;
        $transaction_details->acheck_document_id = $data['documentId'];
        $transaction_details->save();

        Log::channel('post-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction posting data stored into database successfully.");
    }

    private function _postTransactionIntoAcheck21($transaction, $flag)
    {
        $params['amount'] = $transaction->consumer_bank_posting_amount;
        $params['consumer_id'] = $transaction->consumer_id;
        $params['acheck_account_id'] = $transaction->acheck_account_id;
        $history = array(
            'transaction_id' => $transaction->id,
            'transaction_posting' => 1,
            'status_id' => getStatus(FAILED),
        );
        //calling the factory function to create consumer transaction into acheck21
        try {
            Log::channel('post-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Posting transaction for amount: " . $params['amount']);
            Log::channel('post-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Posting transaction for client id: " . $params['acheck_account_id']);
            $params['account_id'] = $transaction->account_id;
            if (env('ACHECK_POSTING')) {
                $response = $this->transaction->createConsumerTransaction($params);
                $response_decoded = json_decode($response, true);
                Log::channel('post-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is enabled. Posting in ACHECK21.");
            } else {
                $response_decoded['documentId'] = rand(********, ********);
                Log::channel('post-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is disabled. Continue with a random number.");
            }
            //check if transaction posted successfully to acheck21
            if (isset($response_decoded['documentId'])) {
                $response_decoded['transaction_time'] = $transaction->transaction_time;
                $response_decoded['transaction_local_time'] = $transaction->local_transaction_time;
                if (!$flag) {
                    $response_decoded['transaction_time'] = Carbon::now();
                    $response_decoded['transaction_local_time'] = Carbon::now($transaction->timezone_name);
                }
                //store transaction data into database
                $this->_createConsumerTransaction($transaction, $response_decoded);
                // store success log into transaction posting history table
                $history['status_id'] = getStatus(SUCCESS);
                Acheck21HistoryTable::create($history);
            }
            Log::channel('post-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transactions posted into acheck21 successfully.");
        } catch (\Exception$e) {
            Acheck21HistoryTable::create($history);
            Log::channel('post-transaction')->error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Exception occured during Transaction", [EXCEPTION => $e]);
            $this->info($e); // Exception Returned
        }
    }
}
