<?php

namespace App\Console\Commands;

use App\Models\Reward;
use App\Models\UserCurrentRewardDetail;
use App\Models\UserRewardUsageHistory;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UpdateTransactionIDForRewards extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:transactionidforrewards';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will update the Transaction ID in the rewards table with the Primary Transaction ID.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Log::channel('update-reward-points')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . ": Fetching Rewards where child trasnaction IDs has been inserted...");
        $pending = getStatus(PENDING);
        $reward_sql = "SELECT td.transaction_ref_no, td1.status_id as transaction_status, r.* from " . env('DB_DATABASE_REWARD_WHEEL') . ".rewards r
                        join transaction_details td on r.transaction_id = td.id
                        join transaction_details td1 on td.transaction_ref_no = td1.id
                        WHERE td.transaction_ref_no is not null order by r.created_at desc";
        $rewards = DB::select($reward_sql);
        if (!empty($rewards)) {
            Log::channel('update-reward-points')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . ": Total number of rewards that need to be processed are: " . count($rewards));
            foreach ($rewards as $reward) {
                // Update the Transaction ID with the Parent Transaction ID
                $update_reward = Reward::find($reward->id);
                $update_reward->transaction_id = $reward->transaction_ref_no;
                $update_reward->save();

                // Update the User Reward Usage History table
                UserRewardUsageHistory::where('reward_id', '=', $reward->id)->update(['transaction_id' => $reward->transaction_ref_no]);

                // Check the Transaction Status
                $success = getStatus(SUCCESS);
                // If the TRansaction is Success
                if ($reward->transaction_status == $success && $reward->status_id == $pending) {
                    $this->_updateRewardPoints($reward);
                } else {
                    Log::channel('update-reward-points')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction ID: " . $reward->transaction_ref_no . " is not a success transaction. Transaction Status: " . $reward->transaction_status);
                }
            }
        } else {
            Log::channel('update-reward-points')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . ": No Rewards found to update Transaction IDs.");
        }
    }

    /**
     * _updateRewardPoints
     * Update Reward to Active if Reward exists from this Transaction
     * @param  mixed $transaction
     * @return void
     */
    private function _updateRewardPoints($reward_data)
    {
        $active = getStatus(ACTIVE);
        $reward_inactive = getStatus(REWARD_WHEEL_INACTIVE);

        $reward_status = [$active, $reward_inactive];
        // Check if Reward Exists against this Transaction ID
        $rewards = Reward::join('user_reward_usage_history', 'rewards.id', '=', 'user_reward_usage_history.reward_id')
            ->join('reward_wheels', 'reward_wheels.id', '=', 'user_reward_usage_history.reward_wheel_id')
            ->select('rewards.*', 'user_reward_usage_history.reward_point', 'user_reward_usage_history.reward_amount', 'user_reward_usage_history.is_generic_point')
            ->where(['rewards.id' => $reward_data->id])
            ->whereIn('reward_wheels.status_id', $reward_status)
            ->get();
        if (!empty($rewards)) {
            foreach ($rewards as $reward) {
                // Update Reward to Active status after the Transaction is success
                $reward_update = Reward::find($reward->id);
                $reward_update->status_id = $active;
                $reward_update->save();

                // Update Reward points
                $user_current_reward_details = UserCurrentRewardDetail::where(['user_id' => $reward->user_id, 'is_generic_point' => $reward->is_generic_point])->whereNull('sponsor_link_id')->whereNull('campaign_id');
                $reward->is_generic_point == 0 ? $user_current_reward_details->where('corporate_parent_id', $reward->corporate_parent_id) : '';
                $user_current_reward_details = $user_current_reward_details->first();
                if (!empty($user_current_reward_details)) {
                    $user_current_reward_details->increment('reward_amount', $reward->reward_amount);
                    $user_current_reward_details->increment('reward_point', $reward->reward_point);
                } else {
                    $user_current_reward_details = new UserCurrentRewardDetail();
                    if ($reward->is_generic_point == 0) {
                        $user_current_reward_details->corporate_parent_id = $reward->corporate_parent_id;
                    }
                    $user_current_reward_details->user_id = $reward->user_id;
                    $user_current_reward_details->reward_amount = $reward->reward_amount;
                    $user_current_reward_details->reward_point = $reward->reward_point;
                    $user_current_reward_details->is_generic_point = $reward->is_generic_point;
                    $user_current_reward_details->save();
                }

                $this->info("Reward Status updated to Status ID: " . $active . " for transaction ID: " . $reward_data->transaction_ref_no);
                Log::channel('update-reward-points')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Reward Status updated to Status ID: " . $active . " for transaction ID: " . $reward_data->transaction_ref_no);
            }
        } else {
            Log::channel('update-reward-points')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . ": No Reward found for Transaction ID: " . $reward_data->transaction_ref_no);
        }
    }
}
