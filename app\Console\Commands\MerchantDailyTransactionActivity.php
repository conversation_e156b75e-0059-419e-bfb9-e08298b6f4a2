<?php

namespace App\Console\Commands;

use App\Http\Controllers\ReportController;
use App\Http\Factories\EmailExecutor\EmailExecutorFactory;
use App\Models\User;
use App\Models\StoreUserTransactionActivityEmailMaps;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class MerchantDailyTransactionActivity extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'merchant:email_transactions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will email the previous day transactions to all the Corporate Parents';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->emailexecutor = new EmailExecutorFactory();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Log::channel('daily-transaction-activity')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Daily Transaction Activity Email Process Started...");
        $this->info("Processing....");
        $status_ids = getStatuses([USER_ACTIVE, USER_ACTIVE_NEW]);
        $transaction_date = date('Y-m-d', strtotime("-1 days"));
        $request = new Request([
            'from_date' => $transaction_date,
            'to_date' => $transaction_date,
            'show_void' => 0,
            'from_merchant_daily_job' => true,
            'initiated_by' => ADMIN,
        ]);
        $reportController = new ReportController($request);
        //Email Sent for all the Stores
        $corporate_parents = User::join('user_roles', 'users.role_id', '=', 'user_roles.role_id')
        ->join('corporate_parent_transaction_activity_email_maps', 'users.user_id', '=', 'corporate_parent_transaction_activity_email_maps.user_id')
        ->join('store_user_map', 'users.user_id', '=', 'store_user_map.user_id')
        ->select('users.*')
        ->addSelect(DB::raw("GROUP_CONCAT(distinct(corporate_parent_transaction_activity_email_maps.email) SEPARATOR ',') as cp_emails"))
        ->whereIn('users.status', $status_ids)
        ->where('user_roles.role_name', CORPORATE_PARENT)
        ->groupBy('users.user_id')
        ->get();
        $email_ids = [];
        foreach ($corporate_parents as $corporate_parent) {
            
            $other_emails = explode(',', $corporate_parent->cp_emails);
            foreach ($other_emails as $email) {
                $email = trim($email);
                if ($email) {
                    $email_ids[] = [
                        'email' => $email,
                        'user_id' => $corporate_parent->user_id,
                        'is_user' => false
                    ];
                }
            }

            // If Testing Mode is ON, filter only @yopmail.com and @mailinator.com emails
            if (ENV('TESTING_MODE')) {
                $email_ids = array_filter($email_ids, function ($entry) {
                    return preg_match('/@(yopmail\.com)$/i', $entry['email']);
                });
            }
        }
        
        //Email to be sent to Store managers and Accountant for their Stores only (If the Email is not in the corporate_parent_transaction_activity_email_maps table)
        $storeManagersAccountants = User::join('user_roles', 'users.role_id', '=', 'user_roles.role_id')
        ->join('store_user_map', 'users.user_id', '=', 'store_user_map.user_id')
        ->select('users.*')
        ->where('receive_daily_transaction_email','1')
        ->whereIn('users.status', $status_ids)
        ->whereIn('user_roles.role_name', [STORE_MANAGER, ACCOUNTANT, REGIONAL_MANAGER, NEW_REGIONAL_MANAGER])
        ->groupBy('users.user_id')
        ->get();
        foreach ($storeManagersAccountants as $cpUsers) {
            $storeManagersAccountantEmail = $cpUsers->email;
            if (ENV('TESTING_MODE')) {
                $storeManagersAccountantEmail = preg_match('/@(yopmail\.com)$/i', $cpUsers->email) ? $cpUsers->email : null;
            }
    
            if ($storeManagersAccountantEmail) {
                $email_ids[] = [
                    'email' => trim($storeManagersAccountantEmail),
                    'user_id' => $cpUsers->user_id,
                    'is_user' => true
                ];
            }
        }

        // Re-index array after filtering
        $email_ids = array_values($email_ids);
        // Send Email Only If There Are Valid Emails
        if (!empty($email_ids)) {
            foreach ($email_ids as $email_id) {
                try {
                    $request->merge(['merchant_daily_job_user_id' => $email_id['user_id']]);
                    $merchant_daily_transactions_array = $reportController->getMerchantLocationTransactionReport($request);
                    if (!empty((array) $merchant_daily_transactions_array)){
                        $email_params = [
                            'user_id' => $email_id['is_user'] ? $email_id['user_id'] : null,
                            'date' => $transaction_date,
                            'email' => $email_id['email'],
                            'from_merchant_daily_job' => true,
                            'merchant_daily_transactions' => $this->drawTable($merchant_daily_transactions_array)
                        ];
                        $this->emailexecutor->corporateParentDailyTransactionActivity($email_params);
                    } else {
                        Log::channel('daily-transaction-activity')->info(__METHOD__ . " (" . __LINE__ . ") - No transactions found for User ID " . $email_id['user_id'] . " to send the daily activity email to " . $email_id['email'] . ".");
                    }
                } catch (\Exception $e) {
                    Log::channel('daily-transaction-activity')->error(__METHOD__ . " (" . __LINE__ . ") - " . "Daily Transaction Activity Email Process Failed for User ID " . $email_id['user_id'] . " to send the daily activity email to " . $email_id['email'] . ". Error: " . $e->getMessage());
                }
            }
        } else {
            Log::channel('daily-transaction-activity')->info(__METHOD__ . " (" . __LINE__ . ") - No users found to receive the daily activity email.");
        }

        $email_ids = [];
        // Check if the key exists before removing
        if ($request->has('merchant_daily_job_user_id')) {
            // Remove the key by replacing the request data without it
            $request->replace($request->except(['merchant_daily_job_user_id']));
        }

        //Email to be Sent to Users at the Store Level
        $storeUsers = StoreUserTransactionActivityEmailMaps::get();

        $store_user_transaction_activity_email_ids = [];
        $unique_combinations = [];

        foreach ($storeUsers as $stUsers) {
            // Sending Daily Activity Mail to the users at the Store Level
            $store_user_transaction_activity_email = $stUsers->email;
            if (env('TESTING_MODE')) {
                $store_user_transaction_activity_email = preg_match('/@(yopmail\.com)$/i', $stUsers->email) ? $stUsers->email : null;
            }

            if ($store_user_transaction_activity_email) {
                $key = $store_user_transaction_activity_email . '_' . $stUsers->store_id;
                if (!isset($unique_combinations[$key])) {
                    $store_user_transaction_activity_email_ids[] = [
                        'email' => trim($store_user_transaction_activity_email),
                        'store_id' => $stUsers->store_id
                    ];
                    $unique_combinations[$key] = true;
                }
            }
        }

        // Send Email Only If There Are Valid Emails
        if (!empty($store_user_transaction_activity_email_ids)) {
            foreach ($store_user_transaction_activity_email_ids as $email_id) {
                try {
                    $request->merge(['store_id' => [$email_id['store_id']]]);
                    $merchant_daily_transactions_array = $reportController->getMerchantLocationTransactionReport($request);
                    if (!empty((array) $merchant_daily_transactions_array)){
                        $email_params = [
                            'store_id' => $email_id['store_id'],
                            'date' => $transaction_date,
                            'email' => $email_id['email'],
                            'from_merchant_daily_job' => true,
                            'merchant_daily_transactions' => $this->drawTable($merchant_daily_transactions_array)
                        ];
                        $this->emailexecutor->storeUserDailyTransactionActivity($email_params);
                    } else {
                        Log::channel('daily-transaction-activity')->info(__METHOD__ . " (" . __LINE__ . ") - No transactions found for store ID: " . $email_id['store_id'] . " to send the daily activity email to: " . $email_id['email'] . ".");
                    }
                } catch (\Exception $e) {
                    Log::channel('daily-transaction-activity')->error(__METHOD__ . " (" . __LINE__ . ") - " . "Daily Transaction Activity Email Process Failed for Store ID " . $email_id['store_id'] . " to send the daily activity email to " . $email_id['email'] . ". Error: " . $e->getMessage());
                }
            }
        } else {
            Log::channel('daily-transaction-activity')->info(__METHOD__ . " (" . __LINE__ . ") - No store users found to receive the daily activity email.");
        }

        $this->info("Merchants Daily Activity Email sent successfully.");
        Log::channel('daily-transaction-activity')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Merchants Daily Activity Email sent successfully");
    }

    private function drawTable($rawReport)
    {
        $reportExportArr = [];
        
        if (!empty((array) $rawReport)) {
            $strType = 1;
            $strCat = 2;
            $strTerm = 3;
            $strTrans = 4;
    
            foreach ($rawReport as $strVal) {
                if (isset($strVal->id)) {
                    $tbodyStart = [
                        'store_name' => $strVal->store_name,
                        'store_type' => '',
                        'store_category' => '',
                        'terminalID' => '',
                        'transaction_date' => '',
                        'transaction_time' => '',
                        'transaction_no' => '',
                        'total_payment' => $strVal->total_payment,
                        'base_amount' => $strVal->base_amount,
                        'tip' => $strVal->tip,
                        'trans_count' => $strVal->trans_count,
                        'transaction_status' => '',
                        'consumer_identifier' => ''
                    ];
                    
                    $reportExportArr[] = $tbodyStart;
                    foreach ($rawReport[$strType] ?? [] as $value) {
                        $tbodyStart = [
                            'store_name' => '',
                            'store_type' => $value->store_type,
                            'store_category' => '',
                            'terminalID' => '',
                            'transaction_date' => '',
                            'transaction_time' => '',
                            'transaction_no' => '',
                            'total_payment' => $value->total_payment,
                            'base_amount' => $value->base_amount,
                            'tip' => $value->tip,
                            'trans_count' => $value->trans_count,
                            'transaction_status' => '',
                            'consumer_identifier' => ''
                        ];
                        
                        $reportExportArr[] = $tbodyStart;
                        
                        foreach ($rawReport[$strCat] ?? [] as $v) {
                            if ($value->store_type == $v->store_type) {
                                foreach ($rawReport[$strTerm] ?? [] as $val) {
                                    if ($val->store_category == $v->store_category && $val->store_type == $v->store_type) {
                                        $reportData1 = [
                                            'store_name' => '',
                                            'store_type' => '',
                                            'store_category' => '',
                                            'terminalID' => $val->terminalID,
                                            'transaction_date' => '',
                                            'transaction_time' => '',
                                            'transaction_no' => '',
                                            'total_payment' => $val->total_payment,
                                            'base_amount' => $val->base_amount,
                                            'tip' => $val->tip,
                                            'trans_count' => $val->trans_count,
                                            'transaction_status' => '',
                                            'consumer_identifier' => ''
                                        ];
                                        
                                        $reportExportArr[] = $reportData1;
                                        
                                        foreach ($rawReport[$strTrans] ?? [] as $y) {
                                            if ($val->store_category == $y->store_category && $val->store_type == $y->store_type && $val->terminalID == $y->terminalID) {
                                                $reportData1 = [
                                                    'store_name' => '',
                                                    'store_type' => '',
                                                    'store_category' => '',
                                                    'terminalID' => '',
                                                    'transaction_date' => Carbon::parse($y->transaction_time)->format('m/d/Y'),
                                                    'transaction_time' => $value->ecommerce_store == 0 ? Carbon::parse($y->transaction_time)->format('h:i A') : '',
                                                    'transaction_no' => $y->transaction_no,
                                                    'total_payment' => $y->total_payment,
                                                    'base_amount' => $y->base_amount,
                                                    'tip' => $y->tip,
                                                    'trans_count' => $y->trans_count,
                                                    'transaction_status' => $y->transaction_status,
                                                    'consumer_identifier' => $y->consumer_identifier
                                                ];
                                                
                                                $reportExportArr[] = $reportData1;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    
                    $strType += 5;
                    $strCat += 5;
                    $strTerm += 5;
                    $strTrans += 5;
                }
            }
        }
        
        return $reportExportArr;
    }
    
    
}
