<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("CREATE TABLE IF NOT EXISTS `tip_configurations` (
                `id` varchar(40) NOT NULL,
                `store_id` varchar(40) DEFAULT NULL,
                `corporate_parent_id` varchar(255) DEFAULT NULL,
                `tip_options` varchar(255) NOT NULL,
                `is_enabled` TINYINT(1) NOT NULL DEFAULT 1,
                `created_at` datetime NOT NULL,
                `updated_at` datetime DEFAULT NULL,
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci
        ");
    }

};
