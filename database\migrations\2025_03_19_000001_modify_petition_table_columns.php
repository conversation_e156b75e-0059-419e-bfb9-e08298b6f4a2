<?php

use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {

        DB::Statement("ALTER TABLE `petitions` DROP COLUMN `primary_contact_person_phone`, DROP COLUMN `secondary_contact_person_phone`");
        DB::Statement("ALTER TABLE `petitions`
            ADD COLUMN IF NOT EXISTS `petition_title` VARCHAR(255) NOT NULL AFTER `petition_number`");
        DB::Statement("ALTER TABLE `petitions`
            DROP COLUMN `apt_number`
        ");
        DB::Statement("ALTER TABLE `petitions`
            <PERSON><PERSON><PERSON> COLUMN `primary_contact_person_name` `primary_contact_person_firstname` VARCHAR(50) NULL DEFAULT NULL COLLATE 'utf8mb3_general_ci' AFTER `zipcode`,
            ADD COLUMN IF NOT EXISTS `primary_contact_person_lastname` VARCHAR(50) NULL DEFAULT NULL AFTER `primary_contact_person_firstname`,
            <PERSON><PERSON><PERSON> COLUMN `secondary_contact_person_name` `secondary_contact_person_firstname` VARCHAR(50) NULL DEFAULT NULL COLLATE 'utf8mb3_general_ci' AFTER `primary_contact_person_email`,
            ADD COLUMN IF NOT EXISTS `secondary_contact_person_lastname` VARCHAR(50) NULL DEFAULT NULL AFTER `secondary_contact_person_firstname`");
    }

};
