<?php
namespace App\Console\Commands;

use App\Models\MerchantStores;
use App\Models\TerminalTransactionReport;
use App\Models\TransactionReport;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ScheduleTransactionReport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:transactionreport {--from_date=} {--to_date=} {--store_id=}';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will update transaction report for all the stores daily or in between date range';
    private $chunkSize = 2500;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        if ($this->option('from_date') != '' && $this->option('to_date') != '') {
            $from_date = $this->option('from_date');
            $to_date = $this->option('to_date');
        } else {
            $from_date = Carbon::now()->subDays(1)->toDateString();
            $to_date = Carbon::now()->subDays(1)->toDateString();
        }
        Log::channel('schedule-transaction-report')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Update transaction report started for " . $from_date . " to " . $to_date . " at UTC Time: " . Carbon::now() . "...");

        $this->info("Updating transaction report for " . $from_date . " to " . $to_date . "...");

        // Check if the transaction report already exists for the date
        $checkRecordQuery = TransactionReport::select('transaction_date')->whereRaw('transaction_date >= ? AND transaction_date <= ?', [$from_date, $to_date]);
        if ($this->option('store_id') != '') {
            $checkRecordQuery = $checkRecordQuery->where('store_id', $this->option('store_id'));
        }
        $checkRecordExists = $checkRecordQuery->orderBy('created_at', 'DESC')->first();

        if (!empty($checkRecordExists)) {
            $this->info("Transaction report already exists for the date range: " . $from_date . " - " . $to_date);
            Log::channel('schedule-transaction-report')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Transaction report already exists for the Date: " . $from_date . " - " . $to_date);
        } else {

            $status_str = SUCCESS . "," . VOIDED . "," . PENDING . "," . RETURNED;

            //fetch All Stores for the merchant
            $storesQuery = MerchantStores::on(MYSQL_RO)->join('status_master', 'merchant_stores.status', '=', 'status_master.id')->select('merchant_stores.*')->where("status_master.status", TERMINAL_ACTIVE);
            if ($this->option('store_id') != '') {
                $storesQuery = $storesQuery->where('merchant_stores.id', $this->option('store_id'));
            }
            $stores = $storesQuery->orderBy('merchant_stores.retailer', 'ASC')->get();
            $this->info("Preparing array for insertion in batch mode...");
            $final_result = [];
            $rowsCountOfTransactionReport = 0;
            $rowsCountOfTerminalReport = 0;
            for ($j = $from_date; $j <= $to_date; $j++) {
                foreach ($stores as $store) {
                    $result = [];
                    $this->info("Fetching store wise data for Store ID: " . $store->id . " and date: " . $j . "...");
                    // main query to calculate store wise transaction details on a single date
                    $store_wise_sql = "SELECT ms.retailer as store_name, ms.id as store_id, tm.id as terminal_id, REPLACE(FORMAT(SUM(if(sm.code=510,0,td.amount + td.tip_amount - td.delivery_fee)), 2), ',', '') AS total_payment, REPLACE(FORMAT(SUM(if(sm.code=510,0,td.amount - td.delivery_fee)), 2), ',', '') AS base_amount, REPLACE(FORMAT(SUM(if(sm.code=510,0,td.tip_amount)), 2), ',', '') AS tip, COUNT(*) AS trans_count, td.transaction_number as transaction_no, td.scheduled_posting_date AS transaction_date
                    FROM transaction_details AS td INNER JOIN status_master AS sm ON td.status_id = sm.id
                    INNER JOIN terminal_master AS tm ON td.terminal_id = tm.id
                    INNER JOIN merchant_stores AS ms ON ms.id = tm.merchant_store_id
                    LEFT JOIN transaction_type_master AS ttm ON td.transaction_type_id = ttm.id
                    WHERE ms.id = ? AND td.scheduled_posting_date = ? AND td.isCanpay = 0  AND td.transaction_ref_no IS NULL AND sm.code IN (" . $status_str . ") AND td.scheduled_posting_date IS NOT NULL AND IF(td.is_ecommerce = 1,td.modification_latest_row = 1,td.modification_latest_row = 0) AND sm.code != 510";
                    $store_wise_searchArray = [$store->id, $j];
                    $store_wise_sql_result = DB::connection(MYSQL_RO)->select($store_wise_sql, $store_wise_searchArray);

                    $this->info("Fetching store type wise data for Store ID: " . $store->id . " and date: " . $j . "...");
                    // main query to calculate store type wise transaction details on a single date
                    $store_type_wise_sql = "SELECT if(td.is_web=0,'Retail','Web') AS store_type, ms.id as store_id, tm.id as terminal_id, REPLACE(FORMAT(SUM(if(sm.code=510,0,td.amount + td.tip_amount - td.delivery_fee)), 2), ',', '') AS total_payment, REPLACE(FORMAT(SUM(if(sm.code=510,0,td.amount - td.delivery_fee)), 2), ',', '') AS base_amount, REPLACE(FORMAT(SUM(if(sm.code=510,0,td.tip_amount)), 2), ',', '') AS tip, COUNT(*) AS trans_count, td.transaction_number as transaction_no, td.scheduled_posting_date AS transaction_date, ms.is_ecommerce as ecommerce_store FROM transaction_details AS td INNER JOIN status_master AS sm ON td.status_id = sm.id
                    INNER JOIN terminal_master AS tm ON td.terminal_id = tm.id
                    INNER JOIN merchant_stores AS ms ON ms.id = tm.merchant_store_id
                    WHERE ms.id = ? AND td.scheduled_posting_date  = ?  AND td.isCanpay = 0  AND td.transaction_ref_no IS NULL AND sm.code IN (" . $status_str . ") AND td.scheduled_posting_date IS NOT NULL AND IF(td.is_ecommerce = 1,td.modification_latest_row = 1,td.modification_latest_row = 0) AND sm.code != 510 GROUP BY store_type";
                    $store_type_wise_searchArray = [$store->id, $j];
                    $store_type_wise_sql_result = DB::connection(MYSQL_RO)->select($store_type_wise_sql, $store_type_wise_searchArray);

                    $this->info("Fetching store terminal wise data for Store ID: " . $store->id . " and date: " . $j . "...");
                    // main query to calculate store terminal wise transaction details on a single date
                    $store_terminal_wise_sql = "SELECT if(td.is_web=0,'Retail','Web') AS stw_store_type, ttm.type AS stw_store_category, tm.terminal_name AS terminalID, ms.id as store_id, tm.id as terminal_id, REPLACE(FORMAT(SUM(if(sm.code=510,0,td.amount + td.tip_amount - td.delivery_fee)), 2), ',', '') AS total_payment, REPLACE(FORMAT(SUM(if(sm.code=510,0,td.amount - td.delivery_fee)), 2), ',', '') AS base_amount, REPLACE(FORMAT(SUM(if(sm.code=510,0,td.tip_amount)), 2), ',', '') AS tip, COUNT(*) AS trans_count, td.pos_web_identifier,td.transaction_number as transaction_no, td.scheduled_posting_date AS transaction_date, td.local_transaction_date as local_transaction_date
                    FROM transaction_details AS td INNER JOIN status_master AS sm ON td.status_id = sm.id
                    INNER JOIN terminal_master AS tm ON td.terminal_id = tm.id
                    INNER JOIN merchant_stores AS ms ON ms.id = tm.merchant_store_id
                    LEFT JOIN transaction_type_master AS ttm ON td.transaction_type_id = ttm.id
                    WHERE ms.id = ? AND td.scheduled_posting_date = ?  AND td.isCanpay = 0  AND td.transaction_ref_no IS NULL AND sm.code IN (" . $status_str . ") AND td.scheduled_posting_date IS NOT NULL AND IF(td.is_ecommerce = 1,td.modification_latest_row = 1,td.modification_latest_row = 0) AND sm.code != 510 GROUP BY stw_store_type, stw_store_category, terminalID";
                    $store_terminal_wise_searchArray = [$store->id, $j];
                    $store_terminal_wise_sql_result = DB::connection(MYSQL_RO)->select($store_terminal_wise_sql, $store_terminal_wise_searchArray);

                    $this->info("Fetching transaction details for Store ID: " . $store->id . " and date: " . $j . "...");
                    // main query to calculate store terminal wise transaction details on a single date
                    $transaction_details_sql = "SELECT if(td.is_web=0,'Retail','Web') AS tds_store_type, ms.id as store_id, tm.id as terminal_id, tm.terminal_name AS tds_terminalID, ttm.type AS td_store_category, td.local_transaction_date as local_transaction_date, td.scheduled_posting_date AS transaction_date, IF(td.is_ecommerce = 0, td.local_transaction_time, td.scheduled_posting_date) as transaction_time, DATE_ADD(td.local_transaction_date, INTERVAL 1 DAY) AS posting_date, REPLACE(FORMAT((td.amount + td.tip_amount - td.delivery_fee), 2), ',', '') AS total_payment, REPLACE(FORMAT(td.amount - td.delivery_fee, 2), ',', '') AS base_amount, REPLACE(FORMAT(td.tip_amount, 2), ',', '') AS tip, '1' AS trans_count, CASE WHEN td.is_v1 = 1 THEN 'Settled' WHEN sm.code = " . VOIDED . " THEN sm.status WHEN td.local_transaction_date < CURDATE() THEN 'Settled' ELSE 'Approved' END AS transaction_status, u.user_identifier, c.user_identifier AS consumer_identifier, td.pos_web_identifier, u.user_identifier AS employee_id, CASE WHEN td1.id IS NOT NULL THEN td1.transaction_number ELSE td.transaction_number END as transaction_no
                    FROM transaction_details AS td FORCE INDEX(idx_scheduled_posting_date) INNER JOIN terminal_master AS tm ON td.terminal_id = tm.id
                    LEFT JOIN transaction_details td1 ON td.id = td1.change_request_transaction_ref_no
                    INNER JOIN merchant_stores AS ms ON ms.id = tm.merchant_store_id
                    LEFT JOIN transaction_type_master AS ttm ON td.transaction_type_id = ttm.id
                    INNER JOIN status_master AS sm ON td.status_id = sm.id
                    LEFT JOIN users AS u ON IF(td1.id IS NOT NULL, td1.user_id = u.user_id, td.user_id = u.user_id)
                    LEFT JOIN users AS c ON IF(td1.id IS NOT NULL, td1.consumer_id = c.user_id, td.consumer_id = c.user_id)
                    WHERE ms.id = ? AND td.scheduled_posting_date = ?  AND td.isCanpay = 0  AND td.transaction_ref_no IS NULL AND sm.code IN (" . $status_str . ") AND td.scheduled_posting_date IS NOT NULL AND sm.code != 510 AND IF(td.is_ecommerce = 1,td.modification_latest_row = 1,td.modification_latest_row = 0) ORDER BY ttm.type, tds_store_type, td.transaction_time";
                    $transaction_details_searchArray = [$store->id, $j];
                    $transaction_details_sql_result = DB::connection(MYSQL_RO)->select($transaction_details_sql, $transaction_details_searchArray);

                    if (count($transaction_details_sql_result) > 0) {
                        array_push($result, $store_wise_sql_result);
                        array_push($result, $store_type_wise_sql_result);
                        array_push($result, $store_terminal_wise_sql_result);
                        array_push($result, $transaction_details_sql_result);
                    }
                    if (!empty($result)) {
                        $final_result = [];
                        array_push($final_result, $result);
                        Log::channel('schedule-transaction-report')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Reports data for store id " . $store->id . " is: " . json_encode($final_result) . ".");
                        $totalTransactionReport = [];
                        $totalTerminalReport = [];
                        foreach ($final_result[0] as $key => $value) {
                            foreach ($value as $k => $val) {
                                $parentReport = [];
                                $childReport = [];
                                $parentReport['store_id'] = $val->store_id;
                                $parentReport['transaction_date'] = $val->transaction_date;
                                $parentReport['total_transaction_amount'] = $val->total_payment;
                                $parentReport['total_base_amount'] = $val->base_amount;
                                $parentReport['total_tip_amount'] = $val->tip;
                                $parentReport['transaction_count'] = $val->trans_count;
                                $parentReport['created_at'] = Carbon::now()->format('Y-m-d H:i:s');

                                // define null value for bulk insert
                                $parentReport['store_name'] = null;
                                $parentReport['store_type'] = null;
                                $parentReport['store_category'] = null;
                                $parentReport['terminal_id'] = null;
                                $parentReport['terminal_name'] = null;

                                if (isset($val->store_name)) {
                                    $parentReport['store_name'] = $val->store_name;
                                    array_push($totalTransactionReport, $parentReport);
                                } elseif (isset($val->store_type)) {
                                    $parentReport['store_type'] = $val->store_type;
                                    array_push($totalTransactionReport, $parentReport);
                                } elseif (isset($val->terminalID)) {
                                    $parentReport['store_type'] = $val->stw_store_type;
                                    $parentReport['store_category'] = $val->stw_store_category;
                                    $parentReport['terminal_id'] = $val->terminal_id;
                                    $parentReport['terminal_name'] = $val->terminalID;
                                    array_push($totalTransactionReport, $parentReport);
                                } else {
                                    $childReport['store_id'] = $val->store_id;
                                    $childReport['terminal_id'] = $val->terminal_id;
                                    $childReport['terminal_name'] = $val->tds_terminalID;
                                    $childReport['store_category'] = $val->td_store_category;
                                    $childReport['store_type'] = $val->tds_store_type;
                                    $childReport['transaction_number'] = $val->transaction_no;
                                    $childReport['transaction_date'] = $val->transaction_date;
                                    $childReport['local_transaction_date'] = $val->local_transaction_date;
                                    $childReport['transaction_time'] = $val->transaction_time;
                                    $childReport['transaction_status'] = $val->transaction_status;
                                    $childReport['total_transaction_amount'] = $val->total_payment;
                                    $childReport['total_base_amount'] = $val->base_amount;
                                    $childReport['total_tip_amount'] = $val->tip;
                                    $childReport['transaction_count'] = $val->trans_count;
                                    $childReport['consumer_identifier'] = $val->consumer_identifier;
                                    $childReport['employee_identifier'] = $val->employee_id;
                                    $childReport['created_at'] = Carbon::now()->format('Y-m-d H:i:s');
                                    array_push($totalTerminalReport, $childReport);
                                }
                                if (count($totalTransactionReport) == $this->chunkSize) {
                                    TransactionReport::insert($totalTransactionReport);
                                    $rowsCountOfTransactionReport = $this->chunkSize + $rowsCountOfTransactionReport;
                                    $totalTransactionReport = [];
                                    $this->info("Inserted " . $this->chunkSize . " rows for Transaction Report.");
                                }
                                if (count($totalTerminalReport) == $this->chunkSize) {
                                    TerminalTransactionReport::insert($totalTerminalReport);
                                    $rowsCountOfTerminalReport = $this->chunkSize + $rowsCountOfTerminalReport;
                                    $totalTerminalReport = [];
                                    $this->info("Inserted " . $this->chunkSize . " rows for Terminal Transactions Report.");
                                }
                            }

                        }

                        if (!empty($totalTransactionReport)) {
                            TransactionReport::insert($totalTransactionReport);
                            $rowsCountOfTransactionReport = count($totalTransactionReport) + $rowsCountOfTransactionReport;
                            $this->info("Inserted " . count($totalTransactionReport) . " rows for Transaction Report.");
                        }
                        if (!empty($totalTerminalReport)) {
                            TerminalTransactionReport::insert($totalTerminalReport);
                            $rowsCountOfTerminalReport = count($totalTerminalReport) + $rowsCountOfTerminalReport;
                            $this->info("Inserted " . count($totalTerminalReport) . " rows for Terminal Transactions Report.");
                        }
                    }
                }
            }
            Log::channel('schedule-transaction-report')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Total rows inserted for Transaction Report is: " . $rowsCountOfTransactionReport . ".");
            Log::channel('schedule-transaction-report')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Total rows inserted for Terminal Transactions Report is: " . $rowsCountOfTerminalReport . ".");
        }
        Log::channel('schedule-transaction-report')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Update Transaction report finished for " . $from_date . " to " . $to_date . " at UTC Time: " . Carbon::now() . ".");
    }
}
