<?php
namespace App\Console\Commands;

use App\Models\MerchantStores;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class UpdateGenericIdentifierForAllStores extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:store-generic-identifier';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will update the generic identifier for all stores';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Generic identifier update process started...");

        $active_status = getStatus(ACTIVE);
        // Fetching all active stores with sync disabled falg down
        $sql = "SELECT
            merchant_stores.id as store_id,
            rw.use_as_generic_points AS is_rw_generic,
            mscp.use_as_generic_points AS is_cashback_generic
        FROM
            merchant_stores
        LEFT JOIN
            " . env('DB_DATABASE_REWARD_WHEEL') . ".reward_store_maps rsm ON rsm.store_id = merchant_stores.id
        LEFT JOIN
            " . env('DB_DATABASE_REWARD_WHEEL') . ".reward_wheels rw ON rw.id = rsm.reward_wheel_id AND rw.status_id = ?
        LEFT JOIN
            " . env('DB_DATABASE_REWARD_WHEEL') . ".merchant_store_cashback_maps mscm ON mscm.store_id = merchant_stores.id
        LEFT JOIN
            " . env('DB_DATABASE_REWARD_WHEEL') . ".merchant_store_cashback_programs mscp ON mscp.id = mscm.merchant_store_cashback_program_id AND mscp.status_id = ?
        WHERE
            merchant_stores.map_viewable = 0
            AND merchant_stores.status = ?
        GROUP BY
            merchant_stores.id";

        $stores = DB::select($sql, [$active_status, $active_status, $active_status]);

        $storeIdsToUpdateAsNonGeneric = [];
        $storeIdsToUpdateAsGeneric = [];

        foreach ($stores as $store) {
            // check if the store is non generic then we need to update the store
            if ($store->is_cashback_generic === 0 || $store->is_rw_generic === 0) {
                $storeIdsToUpdateAsNonGeneric[] = $store->store_id;
            } else {
                $storeIdsToUpdateAsGeneric[] = $store->store_id;
            }
        }

        // Update the 'is_generic' flag in bulk for all relevant stores
        if (!empty($storeIdsToUpdateAsNonGeneric)) {
            MerchantStores::whereIn('id', $storeIdsToUpdateAsNonGeneric)->update(['is_generic' => 0]);
        }
        if (!empty($storeIdsToUpdateAsGeneric)) {
            MerchantStores::whereIn('id', $storeIdsToUpdateAsGeneric)->update(['is_generic' => 1]);
        }


    }
}
