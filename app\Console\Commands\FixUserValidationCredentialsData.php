<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\UserValidationCredentials;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FixUserValidationCredentialsData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:uservalidationdata';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix users missing data in the user_validation_credentials table';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $user_active = getStatus(USER_ACTIVE);
        $user_active_new = getStatus(USER_ACTIVE_NEW);
        $active_status_array = [$user_active, $user_active_new];
        // If users and user_validation_credentials table phone no do not match then update user_validation_credentials table phone number
        $this->info("============================================Update Process Start==============================================");
        $this->info("Update user_validation_credentials table phone number start...");
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Update user_validation_credentials table phone number start...");

        $phone_not_mathced_users = User::select('users.user_id','uvc.id as user_validation_id', 'users.phone as user_table_phone', 'uvc.phone as validation_table_phone')
        ->join('user_validation_credentials as uvc', function ($join) {
            $join->on('uvc.user_id', '=', 'users.user_id')
                ->on('uvc.phone', '!=', 'users.phone');
        })
        ->whereIn('users.status', $active_status_array)
        ->get();
        if (count($phone_not_mathced_users) > 0) {
            $current_time = Carbon::now();
            $this->info(count($phone_not_mathced_users) . " users found for fix user_validation_credentials table phone number.");
            foreach ($phone_not_mathced_users as $phone_not_mathced_user) {
                $user_validation = UserValidationCredentials::find($phone_not_mathced_user->user_validation_id);
                if ($user_validation) {
                    // Phone number updated successfully
                    $user_validation->phone = $phone_not_mathced_user->user_table_phone;
                    $user_validation->save();

                    $activity = [];
                    $activity['id'] = generateUUID();
                    $activity['user_id'] = $phone_not_mathced_user->user_id;
                    $activity['user_validation_credentials_id'] = $phone_not_mathced_user->user_validation_id;
                    $activity['user_table_phone'] = $phone_not_mathced_user->user_table_phone;
                    $activity['validation_table_phone'] = $phone_not_mathced_user->validation_table_phone;
                    $activity['event_type'] = 'update';
                    $activity['created_at'] = $current_time;
                    $activity['updated_at'] = $current_time;
                    // Save history information
                    DB::table('temp_user_validation_credential_history')->insert($activity);

                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "User validation credentials table phone number updated for Consumer ID: " . $phone_not_mathced_user->user_id . " and Validation ID: " . $phone_not_mathced_user->user_validation_id);
                    $this->info("User validation credentials table phone number updated for Consumer ID: " . $phone_not_mathced_user->user_id . " and Validation ID: " . $phone_not_mathced_user->user_validation_id);
                }
            }
        } else {
            $this->info("No users found for update user_validation_credentials table phone number");
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No users found for update user_validation_credentials table phone number");
        }
        $this->info("==============================================Update Process End==============================================");
        
        
        $this->info("============================================Insert Process Start==============================================");
        // If users table data does not exist in this user_validation_credentials table then insert missing data in user_validation_credentials table
        $this->info("Insert missing data in user_validation_credentials table start...");
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Insert missing data in user_validation_credentials table start...");
        // Get consumer role
        $role_id = getRole(CONSUMER);
        // Get missing data
        $missing_user_validation_users = User::select('users.user_id', 'users.phone', 'users.email', 'uvc.id as user_validation_id', 'users.phone as user_table_phone', 'uvc.phone as validation_table_phone')
        ->leftJoin('user_validation_credentials as uvc', function ($join) {
            $join->on('uvc.user_id', '=', 'users.user_id');
        })
        ->whereIn('users.status', $active_status_array)
        ->where('users.role_id', '=', $role_id)
        ->whereNotNull('users.password')
        ->whereNull('uvc.id')
        ->get();
        if (count($missing_user_validation_users) > 0) {
            $current_time = Carbon::now();
            $this->info(count($missing_user_validation_users) . " users found for insert data in this user_validation_credentials table.");
            foreach ($missing_user_validation_users as $missing_user_validation_user) {
                $user_validation_credential = UserValidationCredentials::where('phone', $missing_user_validation_user->user_table_phone)->whereNull('user_id')->orderby('created_at', 'desc')->first();
                // check if any phone number is already exists with null user_id 
                if ($user_validation_credential) {
                    $user_validation_credential->user_id = $missing_user_validation_user->user_id;
                    $user_validation_credential->save();
                    $user_validation_id = $user_validation_credential->id;
                } else {
                    // Save data in User Validation Credential table
                    $user_validation_credential = new UserValidationCredentials();
                    $user_validation_credential->phone = $missing_user_validation_user->phone;
                    $user_validation_credential->email = $missing_user_validation_user->email;
                    $user_validation_credential->user_id = $missing_user_validation_user->user_id;
                    $user_validation_credential->registration_complete = 1;
                    $user_validation_credential->save();
                    $user_validation_id = $user_validation_credential->id;
                }
                $activity = [];
                $activity['id'] = generateUUID();
                $activity['user_id'] = $missing_user_validation_user->user_id;
                $activity['user_validation_credentials_id'] = $user_validation_id;
                $activity['user_table_phone'] = $missing_user_validation_user->user_table_phone;
                $activity['validation_table_phone'] = $missing_user_validation_user->validation_table_phone;
                $activity['event_type'] = 'insert';
                $activity['created_at'] = $current_time;
                $activity['updated_at'] = $current_time;
                // Save history information
                DB::table('temp_user_validation_credential_history')->insert($activity);

                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Missing data inserted in this user_validation_credentials table for Consumer ID: " . $missing_user_validation_user->user_id . " and Validation ID: " . $user_validation_id);
                $this->info("Missing data inserted in this user_validation_credentials table for Consumer ID: " . $missing_user_validation_user->user_id . " and Validation ID: " . $user_validation_id);
            }
        } else {
            $this->info("No users found for insert data in this user_validation_credentials table");
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No users found for insert data in this user_validation_credentials table");
        }
        $this->info("==============================================Insert Process End==============================================");
    }

}
