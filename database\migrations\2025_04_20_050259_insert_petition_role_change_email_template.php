<?php

use App\Models\EmailTemplate;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        EmailTemplate::where('template_name', 'Petition Role Change Notification')->delete();
        EmailTemplate::firstOrCreate(['template_name' => 'Petition Role Change Notification', 'template_subject' => 'Your Petition Role Has Been Updated', 'template_body' => '<table role="presentation" class="main"> <tr> <td class="wrapper"> <table role="presentation" border="0" cellpadding="0" cellspacing="0"> <tr> <td> <p>Hi {{$user_details->first_name . " " . $user_details->middle_name . " " . $user_details->last_name}},</p> <p>Your role in the petition <strong>{{$store_name}}</strong> has been updated.</p> <p>New Role: <strong>{{$role_name}}</strong></p> <p>Best Regards,</p> <p>CanPay Team</p> </td> </tr> </table> </td> </tr> </table>']);
        
        EmailTemplate::where('template_name', 'Petition Created Notification to Store')->delete();
        EmailTemplate::firstOrCreate([
            'template_name' => 'Petition Created Notification to Store',
            'template_subject' => 'A Petition Has Been Created for Your Store',
            'template_body' => '<table role="presentation" class="main"> <tr> <td class="wrapper"> <table role="presentation" border="0" cellpadding="0" cellspacing="0"> <tr> <td> <p>Hi {{$name}},</p> <p>A new petition has been created by a consumer regarding your store <strong>{{$store_name}}</strong>.</p> <p>You can view the petition using the link below:</p> <p><a href="{{$petition_url}}" target="_blank">View Petition</a></p> <p>We recommend reviewing the petition and considering engagement where appropriate.</p> <p>Best Regards,</p> <p>CanPay Team</p> </td> </tr> </table> </td> </tr> </table>'
        ]);
    }
};
