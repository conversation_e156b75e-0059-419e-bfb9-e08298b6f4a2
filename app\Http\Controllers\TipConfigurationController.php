<?php

namespace App\Http\Controllers;

use App\Models\MerchantStores;
use App\Models\TipConfiguration;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class TipConfigurationController extends Controller
{
    /**
     * __construct
     * Constructor for the TipConfigurationController
     */
    public function __construct()
    {
        // Initialize any dependencies here
    }

    /**
     * getTipConfigurations
     * This Function will get all tip configurations for a store based on the logged-in user's permissions
     * Prioritizes configurations where corporate_parent_id matches the logged-in user's ID
     * @param  mixed $request
     *
     * @return void
     */
    public function getTipConfigurations(Request $request)
    {
        try {
            // Columns defined for Sorting
            $columns = array(
                0 => 'retailer',
            );

            $user_details = Auth::user(); // Fetching Logged In User Details

            $cpConfig = TipConfiguration::where('corporate_parent_id', $user_details->user_id)->first();
            // Main Query
            $query = TipConfiguration::leftJoin('store_user_map', 'tip_configurations.store_id', '=', 'store_user_map.store_id')
            ->leftJoin('merchant_stores', 'tip_configurations.store_id', '=', 'merchant_stores.id')
            ->where(function ($q) use ($user_details) {
                $q->where('store_user_map.user_id', $user_details->user_id)
                    ->orWhere('corporate_parent_id', $user_details->user_id);
            })->select('tip_configurations.*', 'merchant_stores.retailer',
                DB::raw("CASE WHEN corporate_parent_id = '{$user_details->user_id}' THEN 1 ELSE 0 END as is_parent"))
            ->groupBy('tip_configurations.id');

            //Count Query
            $queryCount = TipConfiguration::leftJoin('store_user_map', 'tip_configurations.store_id', '=', 'store_user_map.store_id')
            ->leftJoin('merchant_stores', 'tip_configurations.store_id', '=', 'merchant_stores.id')
            ->where(function ($q) use ($user_details) {
                $q->where('store_user_map.user_id', $user_details->user_id)
                    ->orWhere('corporate_parent_id', $user_details->user_id);
            })->selectRaw('COUNT(DISTINCT tip_configurations.id) as total_count');
            $totalData = $queryCount->first()->total_count; // Getting total no of records

            $totalFiltered = $totalData;
            $limit = intval($request->input('length'));
            $start = intval($request->input('start'));
            $order = $columns[$request->input('order.0.column')];
            $dir = $request->input('order.0.dir');

            // Always prioritize configurations where corporate_parent_id matches the logged-in user's ID
            if (empty($request->input('search.value')) && empty($order) && empty($dir)) {
                $stores = $query->offset($start)
                    ->limit(intval($limit))
                    ->orderBy('is_parent', 'DESC') // First sort by is_parent to prioritize parent configs
                    ->orderBy('tip_configurations.created_at', 'DESC')
                    ->get();
            } else if (empty($request->input('search.value'))) {
                $stores = $query->offset($start)
                    ->limit(intval($limit))
                    ->orderBy('is_parent', 'DESC') // First sort by is_parent to prioritize parent configs
                    ->orderBy($order, $dir)
                    ->get();
            } else {
                $search = $request->input('search.value');

                // For parent configurations (corporate_parent_id = user_id), include them regardless of search
                $search_query = $query->where(function ($q) use ($search, $user_details) {
                    $q->where('corporate_parent_id', $user_details->user_id) // Always include parent configs
                      ->orWhere('merchant_stores.retailer', 'LIKE', "%{$search}%"); // Apply search only to non-parent configs
                });

                $search_query_count = $queryCount->where(function ($q) use ($search, $user_details) {
                    $q->where('corporate_parent_id', $user_details->user_id)
                      ->orWhere('merchant_stores.retailer', 'LIKE', "%{$search}%");
                });

                $totalFiltered = $search_query_count->first()->total_count;

                $stores = $search_query->offset($start)
                    ->limit(intval($limit))
                    ->orderBy('is_parent', 'DESC') // First sort by is_parent to prioritize parent configs
                    ->orderBy($order, $dir)
                    ->get();
            }

            $data = array();
            if (!empty($stores)) {
                // Creating array to show the values in frontend
                foreach ($stores as $store) {
                    $nestedData['retailer'] = $store->retailer ? $store->retailer : 'Global Configuration';
                    $nestedData['tip_options'] = json_decode($store->tip_options);
                    $nestedData['is_enabled'] = $store->is_enabled;
                    $nestedData['is_store_specific'] = $store->is_store_specific ?? 1; // Default to 1 for backward compatibility
                    $nestedData['store_id'] = $store->store_id;
                    $nestedData['is_parent'] = $store->is_parent;
                    $nestedData['id'] = $store->id;
                    $nestedData['corporate_parent_id'] = $store->corporate_parent_id;
                    $nestedData['is_parent'] = ($store->corporate_parent_id == $user_details->user_id) ? true : false;
                    $nestedData['created_at'] = date('m-d-Y h:i A', strtotime($store->created_at));
                    $data[] = $nestedData;
                }
            }

            // Drawing the Datatable
            $json_data = array(
                "draw" => intval($request->input('draw')),
                "recordsTotal" => intval($totalData),
                "recordsFiltered" => intval($totalFiltered),
                "data" => $data,
                "cpConfig" => $cpConfig ? 1 : 0,
            );

            Log::info(__METHOD__ . " - Tip configurations fetched successfully for User ID: " . $user_details->user_id);
            echo json_encode($json_data); // Returning the data

        } catch (\Exception $e) {
            Log::error(__METHOD__ . " - Exception while fetching tip configurations.", [EXCEPTION => $e]);
            return response()->json([
                'draw' => intval($request->get('draw')),
                'recordsTotal' => 0,
                'recordsFiltered' => 0,
                'data' => [],
                'error' => trans('message.tip_configurations_fetch_error')
            ], 500);
        }
    }


    /**
     * createTipConfiguration
     * This Function will create a new tip configuration
     * If is_store_specific=1, store_id is required and the configuration is associated with a specific store
     * If is_store_specific=0, store_id is not required and the configuration is associated only with the corporate parent ID
     * @param  mixed $request
     *
     * @return void
     */
    public function createTipConfiguration(Request $request)
    {
        // Validate common required fields
        $this->validate($request, [
            'tip_options' => VALIDATION_REQUIRED,
        ]);

        // If is_store_specific is 1, validate store_id
        if ($request->get('is_store_specific') == 1) {
            $this->validate($request, [
                'store_id' => VALIDATION_REQUIRED,
            ]);
        }

        $user_details = Auth::user(); // Fetching Logged In User Details

        DB::beginTransaction();
        try {
            // Check if a tip configuration already exists based on is_store_specific
            if ($request->get('is_store_specific') == 1) {
                // For store-specific configurations, check if one already exists for this store
                $existingConfig = TipConfiguration::where('store_id', $request->get('store_id'))->first();

                if ($existingConfig) {
                    $message = trans('message.tip_configuration_already_exists');
                    return renderResponse(FAIL, $message, null);
                }

                // Check if store exists
                $store = MerchantStores::find($request->get('store_id'));

                if (!$store) {
                    $message = trans('message.store_not_found');
                    return renderResponse(FAIL, $message, null);
                }

                // Check if user has access to the store using store_user_map
                $hasAccess = DB::table('store_user_map')
                    ->where('user_id', $user_details->user_id)
                    ->where('store_id', $request->get('store_id'))
                    ->exists();
            } else {
                // For corporate-level configurations, check if one already exists for this corporate parent
                $existingConfig = TipConfiguration::where('corporate_parent_id', $user_details->user_id)
                    ->whereNull('store_id')
                    ->first();

                if ($existingConfig) {
                    $message = trans('message.tip_configuration_already_exists');
                    return renderResponse(FAIL, $message, null);
                }
            }

            $tipConfiguration = new TipConfiguration();
            $tipConfiguration->tip_options = json_encode($request->get('tip_options'));
            $tipConfiguration->is_enabled = 1;

            // Set appropriate fields based on is_store_specific
            if ($request->get('is_store_specific') == 1) {
                // Check if user has access to the store
                if (!$hasAccess) {
                    $message = trans('message.store_access_authorization_failure');
                    Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "User ID: " . $user_details->user_id . " attempted to access store ID: " . $request->get('store_id') . " without permission");
                    return renderResponse(FAIL, $message, null);
                }

                $tipConfiguration->store_id = $request->get('store_id');
                $tipConfiguration->corporate_parent_id = null;
                $logMessage = "Tip configuration created successfully for store ID: " . $request->get('store_id') . " by User ID: " . $user_details->user_id;
            } else {
                // If is_store_specific is 0, set store_id to null
                $tipConfiguration->corporate_parent_id = $user_details->user_id;
                $tipConfiguration->store_id = null;
                $logMessage = "Corporate-level tip configuration created successfully by User ID: " . $user_details->user_id;
            }

            $tipConfiguration->save();

            DB::commit();

            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . $logMessage);
            $message = trans('message.tip_configuration_creation_success');
            return renderResponse(SUCCESS, $message, $tipConfiguration);
        } catch (\Exception $e) {
            DB::rollback();
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occurred during Tip Configuration Creation" . ".", [EXCEPTION => $e]);
            $message = trans('message.tip_configuration_creation_error');
            return renderResponse(FAIL, $message, null);
        }
    }

    /**
     * updateTipConfiguration
     * This Function will update an existing tip configuration
     * @param  mixed $request
     *
     * @return void
     */
    public function updateTipConfiguration(Request $request)
    {
        $this->validate($request, [
            'id' => VALIDATION_REQUIRED,
            'tip_options' => VALIDATION_REQUIRED,
        ]);

        $user_details = Auth::user(); // Fetching Logged In User Details

        DB::beginTransaction();
        try {
            $tipConfiguration = TipConfiguration::find($request->get('id'));

            if (!$tipConfiguration) {
                $message = trans('message.tip_configuration_not_found');
                return renderResponse(FAIL, $message, null);
            }

            // Check if user has access to this tip configuration
            // User can update if they are the corporate_parent_id or if they have access to the store
            $hasAccess = ($tipConfiguration->corporate_parent_id == $user_details->user_id) ||
                         DB::table('store_user_map')
                            ->where('user_id', $user_details->user_id)
                            ->where('store_id', $tipConfiguration->store_id)
                            ->exists();

            if (!$hasAccess) {
                $message = trans('message.unauthorized_access');
                Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "User ID: " . $user_details->user_id . " attempted to update tip configuration ID: " . $request->get('id') . " without permission");
                return renderResponse(FAIL, $message, null);
            }

            $tipConfiguration->tip_options = json_encode($request->get('tip_options'));
            $tipConfiguration->save();

            DB::commit();

            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Tip configuration updated successfully for ID: " . $request->get('id') . " by User ID: " . $user_details->user_id);
            $message = trans('message.tip_configuration_update_success');
            return renderResponse(SUCCESS, $message, $tipConfiguration);
        } catch (\Exception $e) {
            DB::rollback();
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occurred during Tip Configuration Update" . ".", [EXCEPTION => $e]);
            $message = trans('message.tip_configuration_update_error');
            return renderResponse(FAIL, $message, null);
        }
    }

    /**
     * updateTipConfigStatus
     * This Function will update the status (is_enabled) of a tip configuration
     * @param  mixed $request
     *
     * @return void
     */
    public function updateTipConfigStatus(Request $request)
    {
        $this->validate($request, [
            'id' => VALIDATION_REQUIRED,
            'is_enabled' => VALIDATION_REQUIRED,
        ]);

        $user_details = Auth::user(); // Fetching Logged In User Details

        DB::beginTransaction();
        try {
            $tipConfiguration = TipConfiguration::find($request->get('id'));

            if (!$tipConfiguration) {
                $message = trans('message.tip_configuration_not_found');
                return renderResponse(FAIL, $message, null);
            }

            // Check if user has access to this tip configuration
            // User can update if they are the corporate_parent_id or if they have access to the store
            $hasAccess = ($tipConfiguration->corporate_parent_id == $user_details->user_id) ||
                         DB::table('store_user_map')
                            ->where('user_id', $user_details->user_id)
                            ->where('store_id', $tipConfiguration->store_id)
                            ->exists();

            if (!$hasAccess) {
                $message = trans('message.unauthorized_access');
                Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "User ID: " . $user_details->user_id . " attempted to update status of tip configuration ID: " . $request->get('id') . " without permission");
                return renderResponse(FAIL, $message, null);
            }

            // Update only the is_enabled field
            $tipConfiguration->is_enabled = $request->get('is_enabled');
            $tipConfiguration->save();

            DB::commit();

            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Tip configuration status updated successfully for ID: " . $request->get('id') . " by User ID: " . $user_details->user_id);
            $message = trans('message.tip_configuration_status_update_success');
            return renderResponse(SUCCESS, $message, $tipConfiguration);
        } catch (\Exception $e) {
            DB::rollback();
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occurred during Tip Configuration Status Update" . ".", [EXCEPTION => $e]);
            $message = trans('message.tip_configuration_status_update_error');
            return renderResponse(FAIL, $message, null);
        }
    }

    /**
     * deleteTipConfiguration
     * This Function will delete a tip configuration
     * @param  mixed $request
     *
     * @return void
     */
    public function deleteTipConfiguration(Request $request)
    {
        $this->validate($request, [
            'id' => VALIDATION_REQUIRED,
        ]);

        $user_details = Auth::user(); // Fetching Logged In User Details

        DB::beginTransaction();
        try {
            $tipConfiguration = TipConfiguration::find($request->get('id'));

            if (!$tipConfiguration) {
                $message = trans('message.tip_configuration_not_found');
                return renderResponse(FAIL, $message, null);
            }

            // Check if user has access to this tip configuration
            // User can delete if they are the corporate_parent_id or if they have access to the store
            $hasAccess = ($tipConfiguration->corporate_parent_id == $user_details->user_id) ||
                         DB::table('store_user_map')
                            ->where('user_id', $user_details->user_id)
                            ->where('store_id', $tipConfiguration->store_id)
                            ->exists();

            if (!$hasAccess) {
                $message = trans('message.unauthorized_access');
                Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "User ID: " . $user_details->user_id . " attempted to delete tip configuration ID: " . $request->get('id') . " without permission");
                return renderResponse(FAIL, $message, null);
            }

            $tipConfiguration->delete();

            DB::commit();

            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Tip configuration deleted successfully for ID: " . $request->get('id') . " by User ID: " . $user_details->user_id);
            $message = trans('message.tip_configuration_delete_success');
            return renderResponse(SUCCESS, $message, null);
        } catch (\Exception $e) {
            DB::rollback();
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occurred during Tip Configuration Deletion" . ".", [EXCEPTION => $e]);
            $message = trans('message.tip_configuration_delete_error');
            return renderResponse(FAIL, $message, null);
        }
    }

        /**
     *
     * getAllApiKeys
     * Listing page for Store API Keys with Merchant ID and Store ID along with Server Side Pagination in Datatable
     * @param  mixed $request
     * @return void
     */
    public function getStoreApiKeys(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Store API keys search started...");
        // Validating input request
        $this->validate($request, [
            'store_id' => VALIDATION_REQUIRED,
        ]);

        //Search with in Corpoarate Parents
        $stores_api_keys = $this->_getStoreApiKeySearch($request);

        $message = trans('message.store_api_key_fetch_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Store API Keys List fetched successfully.");
        return renderResponse(SUCCESS, $message, $stores_api_keys);
    }

    /**
     * _getCorporateParentSearch
     * Fetch the Corporate Parents
     * @param  mixed $searchArray
     * @return void
     */
    private function _getStoreApiKeySearch($request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Corporate Parent Search Started.");

        $sql = "SELECT `registered_merchant_master`.`merchant_id` AS `merchantID`, `merchant_stores`.`store_id`, `merchant_stores`.`retailer`, `merchant_api_key_maps`.`app_key`, `merchant_api_key_maps`.`api_secret`
        FROM `registered_merchant_master`
        INNER JOIN `merchant_stores` ON `registered_merchant_master`.`id` = `merchant_stores`.`merchant_id`
        INNER JOIN `merchant_api_key_maps` ON `merchant_stores`.`id` = `merchant_api_key_maps`.`store_id` WHERE 1  ";

        $searchStr = [];
        if (trim($request['store_id'])) {
            $sql .= " AND merchant_stores.id = ? ";
            array_push($searchStr, $request['store_id']);
        }
        $sql .= " order by `merchant_stores`.`retailer` LIMIT 100";
        $apiKeys = DB::connection(MYSQL_RO)->Select($sql, $searchStr);

        $apikeysArray = [];
        if (!empty($apiKeys)) {
            foreach ($apiKeys as $keys) {
                $data = [];
                $data['merchantID'] = $keys->merchantID;
                $data['store_id'] = $keys->store_id;
                $data['retailer'] = $keys->retailer;
                $data['app_key'] = $keys->app_key;
                $data['api_secret'] = $keys->api_secret;

                array_push($apikeysArray, $data);
            }
        }

        return $apikeysArray;
    }
}
