<?php

namespace App\Console\Commands;

use App\Models\MerchantStores;
use App\Models\SettlementFeesReport;
use App\Models\TerminalMaster;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class GenerateStoreSettlementReport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:storesettlementreport {--from_date=} {--to_date=} {--store_id=}';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command generate settlement report for a particular store for a particular date.';
    private $chunkSize = 500;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        if ($this->option('from_date') != '' && $this->option('to_date') != '' && $this->option('store_id') != '') {
            $from_date = $this->option('from_date');
            $to_date = $this->option('to_date');
        } else {
            $from_date = Carbon::now()->subDays(1)->toDateString();
            $to_date = Carbon::now()->subDays(1)->toDateString();
        }
        $store_id = $this->option('store_id');
        Log::channel('settlement-report')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Update settlement report started for " . $from_date . " to " . $to_date . " at UTC Time: " . Carbon::now() . " and Store ID: " . $store_id . "...");

        $this->info("Updating settlement report for " . $from_date . " to " . $to_date . " for Store ID: " . $store_id . "...");

        // Check if the Settlement record already exists for the date
        $checkRecordExists = SettlementFeesReport::select('sales_date', 'id')->whereRaw('sales_date >= ? AND sales_date <= ?', [$from_date, $to_date])->where('store_id', $store_id)->orderBy('created_at', 'DESC')->first();

        if (!empty($checkRecordExists)) {
            $this->info("Record already exists for the Date range: " . $from_date . " - " . $to_date . " and Store ID: " . $store_id . ". Need to delete the record first.");

            // Delete the existing record
            SettlementFeesReport::find($checkRecordExists->id)->delete();

            Log::channel('settlement-report')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Record already exists for the Date: " . $from_date . " - " . $to_date . " and Store ID: " . $store_id . ". Need to delete the record first.");
        }
        //Get Status Pending and Success
        $pending_status_id = getStatus(PENDING);
        $success_status_id = getStatus(SUCCESS);
        $return_status_id = getStatus(RETURNED);

        $status_str = "'" . $pending_status_id . "','" . $success_status_id . "','" . $return_status_id . "'";

        //fetch All Stores for the merchant
        $stores = MerchantStores::join('status_master', 'merchant_stores.status', '=', 'status_master.id')->select('merchant_stores.*')->where(["status_master.status" => TERMINAL_ACTIVE, 'merchant_stores.id' => $store_id])->orderBy('merchant_stores.retailer', 'ASC')->get();

        $this->info("Preparing array for insertion in batch mode...");
        $current_time = Carbon::now();
        $result = [];
        for ($j = $from_date; $j <= $to_date; $j++) {
            foreach ($stores as $valStores) {
                $this->info("Fetching data for Store ID: " . $valStores->id . " and date: " . $j . "...");

                $terminals = TerminalMaster::join('merchant_stores', 'merchant_stores.id', '=', 'terminal_master.merchant_store_id')->select(DB::raw("GROUP_CONCAT(CONCAT('''', terminal_master.id, '''' )) as terminal_ids"))->where('merchant_stores.id', $valStores->id)->first();

                // Fetching Transaction fees
                $transaction_fees = DB::connection(MYSQL_RO)->select("SELECT rmm.* from registered_merchant_master as rmm INNER JOIN merchant_stores as ms On ms.merchant_id = rmm.id WHERE ms.id = ?", [$valStores->id]);
                $retail_vol_fees = $transaction_fees[0]->volume_value;
                $retail_trans_fees = $transaction_fees[0]->per_transaction_value;
                $web_vol_fees = $transaction_fees[0]->volume_value;
                $web_trans_fees = $transaction_fees[0]->per_transaction_value;

                $v2_retail_sql = "WITH recursive all_dates(dt) AS (
                    SELECT ? dt
                    UNION ALL
                    SELECT dt + interval 1 day FROM all_dates WHERE dt + interval 1 DAY <= ?)";

                $searchArray = [$j, $j];

                if ($terminals->terminal_ids != '') {
                    $v2_retail_sql .= ", combined_data AS (
                        SELECT dt, td.id AS transaction_id,
                            amount + tip_amount - delivery_fee AS volume,
                            0 AS reward_amount,
                            cashback_amount_earned,
                            'transaction' AS source,
                            merchant_fees_waived,
                            merchant_fee_discount_id,
                            merchant_fee_discount_basis_point,
                            SUM(trd.reward_amount) as merchant_reward_amount_used,
                            td.consumer_bank_posting_amount,
                            rmm.volume_value
                        FROM all_dates AS d
                        LEFT JOIN transaction_details AS td ON td.scheduled_posting_date = d.dt AND td.status_id IN ($status_str) AND td.is_v1 = 0 AND td.transaction_ref_no IS NULL AND td.isCanpay = 0 AND td.terminal_id IN (" . $terminals->terminal_ids . ")
                        LEFT JOIN terminal_master tm ON tm.id = td.terminal_id
                        LEFT JOIN merchant_stores ms ON tm.merchant_store_id = ms.id
                        LEFT JOIN registered_merchant_master rmm ON rmm.id = ms.merchant_id
                        LEFT JOIN transaction_reward_details trd ON trd.transaction_id = td.id AND trd.points_type = 'merchant_points'
                        WHERE td.id IS NOT NULL
                        GROUP BY td.id
                        UNION
                        SELECT dt, mrh.id AS reward_id,
                            0 AS volume,
                            mrh.reward_amount,
                            0 AS cashback_amount_earned,
                            'reward' AS source,
                            0 AS merchant_fees_waived,
                            NULL AS merchant_fee_discount_id,
                            NULL AS merchant_fee_discount_basis_point,
                            NULL AS merchant_reward_amount_used,
                            NULL AS consumer_bank_posting_amount,
                            NULL AS volume_value
                        FROM all_dates AS d
                        LEFT JOIN ach_merchant_reward_history AS mrh
                        ON mrh.scheduled_posting_date = d.dt
                        AND mrh.is_voided = 0
                        AND mrh.terminal_id IN (" . $terminals->terminal_ids . ")
                    )
                    SELECT
                        d.dt AS sales_date,
                        IFNULL(SUM(volume), 0) AS v2_retail_volume,
                        " . $retail_vol_fees . " AS v2_retail_vol_rate,
                        IFNULL((SUM(volume) / 100) * " . $retail_vol_fees . ", 0) AS v2_retail_vol_fee,
                        COUNT(CASE WHEN source = 'transaction' THEN volume ELSE NULL END) AS v2_retail_no_of_trans,
                        " . $retail_trans_fees . " AS v2_retail_fee_rate,
                        IFNULL(COUNT(CASE WHEN source = 'transaction' THEN volume ELSE NULL END) * " . $retail_trans_fees . ", 0) AS v2_retail_trans_fee,
                        IFNULL(((SUM(volume) / 100) * " . $retail_vol_fees . " + COUNT(CASE WHEN source = 'transaction' THEN volume ELSE NULL END) * " . $retail_trans_fees . "), 0) AS v2_retail_sub_total_fees,
                        IFNULL(
                            SUM(
                                CASE
                                    WHEN merchant_fee_discount_id IS NOT NULL THEN
                                        ROUND(volume * (IFNULL(merchant_fee_discount_basis_point, 0) / 100), 2)
                                    ELSE 0
                                END
                            ) +
                            SUM(
                                CASE
                                    WHEN COALESCE(merchant_reward_amount_used, 0) > 0 THEN
                                        ROUND(COALESCE(merchant_reward_amount_used, 0) * (volume_value - IFNULL(merchant_fee_discount_basis_point, 0)) / 100, 2)
                                    ELSE 0
                                END
                            ) +
                            SUM(
                                CASE
                                    WHEN consumer_bank_posting_amount = 0 AND COALESCE(merchant_reward_amount_used, 0) > 0 AND volume = COALESCE(merchant_reward_amount_used, 0)
                                    THEN $retail_trans_fees ELSE 0
                                END
                            ),
                            0
                        ) AS v2_retail_sub_total_fees_waived,
                        IFNULL(SUM(reward_amount), 0) AS v2_merchant_reward_volume,
                        0 AS is_web,
                        IFNULL(SUM(cashback_amount_earned), 0) AS v2_retail_cashback_volume
                    FROM all_dates AS d
                    LEFT JOIN combined_data AS cd ON cd.dt = d.dt";
                } else {
                    $v2_retail_sql .= " SELECT d.dt AS sales_date,
                        0 AS v2_retail_volume,
                        " . $retail_vol_fees . " AS v2_retail_vol_rate,
                        0 AS v2_retail_vol_fee,
                        0 AS v2_retail_no_of_trans,
                        " . $retail_trans_fees . " AS v2_retail_fee_rate,
                        0 AS v2_retail_trans_fee,
                        0 AS v2_retail_sub_total_fees,
                        0 AS v2_retail_sub_total_fees_waived,
                        0 as is_web,
                        0 AS v2_merchant_reward_volume,
                        0 AS v2_retail_cashback_volume
                    FROM all_dates AS d";
                }

                $v2_retail_sql .= " GROUP BY d.dt DESC ";
                $v2_retail = DB::connection(MYSQL_RO)->select($v2_retail_sql, $searchArray);

                $i = 0;
                foreach ($v2_retail as $value) {
                    $totalVolume = $v2_retail[$i]->v2_retail_volume;
                    $totalFees = $v2_retail[$i]->v2_retail_sub_total_fees;
                    $activity = [];
                    $activity['id'] = generateUUID();
                    $activity['sales_date'] = date('Y-m-d', strtotime($v2_retail[$i]->sales_date));
                    $activity['store_id'] = $valStores->id;
                    $activity['merchant_store_id'] = $valStores->store_id;
                    if ($v2_retail[$i]->v2_retail_no_of_trans > 0) {
                        $activity['total_volume'] = $totalVolume > 0 ? $totalVolume : "Pending";
                    } else {
                        $activity['total_volume'] = "0.00";
                    }

                    $activity['total_fees'] = round_up($totalFees, 2);
                    $activity['v1_deposit'] = 0.00;
                    $activity['v1_fees'] = 0.00;
                    $activity['v2_deposit'] = $v2_retail[$i]->v2_retail_volume;
                    $activity['v2_fees'] = round_up($v2_retail[$i]->v2_retail_sub_total_fees, 2);
                    $activity['v1_volume'] = 0.00;
                    $activity['v1_vol_rate'] = $v2_retail[$i]->sales_date == null ? "" : $retail_vol_fees;
                    $activity['v1_vol_fee'] = 0.00;
                    $activity['v1_no_of_trans'] = 0;
                    $activity['v1_fee_rate'] = $v2_retail[$i]->sales_date == null ? "" : $retail_trans_fees;
                    $activity['v1_trans_fee'] = 0.00;
                    $activity['v1_sub_total_fees'] = 0.00;
                    $activity['v2_retail_volume'] = $v2_retail[$i]->v2_retail_volume;
                    $activity['v2_retail_cashback_volume'] = $v2_retail[$i]->v2_retail_cashback_volume;
                    $activity['v2_merchant_reward_volume'] = $v2_retail[$i]->v2_merchant_reward_volume;
                    $activity['v2_retail_sub_total_fees_waived'] = $v2_retail[$i]->v2_retail_sub_total_fees_waived;
                    $activity['v2_retail_vol_rate'] = $v2_retail[$i]->sales_date == null ? "" : $v2_retail[$i]->v2_retail_vol_rate;
                    $activity['v2_retail_vol_fee'] = round_up($v2_retail[$i]->v2_retail_vol_fee, 2);
                    $activity['v2_retail_no_of_trans'] = $v2_retail[$i]->v2_retail_no_of_trans;
                    $activity['v2_retail_fee_rate'] = $v2_retail[$i]->sales_date == null ? "" : $v2_retail[$i]->v2_retail_fee_rate;
                    $activity['v2_retail_trans_fee'] = $v2_retail[$i]->v2_retail_trans_fee;
                    $activity['v2_retail_sub_total_fees'] = round_up($v2_retail[$i]->v2_retail_sub_total_fees, 2);
                    $activity['created_at'] = $current_time;
                    $activity['updated_at'] = $current_time;

                    $i++;
                    array_push($result, $activity);

                    if (count($result) == $this->chunkSize) {
                        SettlementFeesReport::insert($result); // Insert into settlement_fees_reports
                        $this->info("Inserted " . count($result) . " rows for Settlement Report.");
                        Log::channel('settlement-report')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . count($result) . " rows inserted for Settlement Report.");
                        $result = [];
                    }
                }
            }
        }
        if (!empty($result)) { // Processing the remaining rows
            SettlementFeesReport::insert($result); // Insert into settlement_fees_reports
            $this->info("Inserted Left Over" . count($result) . "  rows for Settlement Report.");
            Log::channel('settlement-report')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Inserted left over " . count($result) . " rows for Settlement Report.");
        }
        Log::channel('settlement-report')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Update settlement report finished for " . $from_date . " to " . $to_date . " at UTC Time: " . Carbon::now() . " and Store ID: " . $store_id);
    }
}
