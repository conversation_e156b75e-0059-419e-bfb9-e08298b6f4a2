<?php
namespace App\Console\Commands;

use App\Models\AllStoreTransactionReportSearchHistory;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DeleteTransactionReportSearchHistoryOfAllStore extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'delete:transactionreportsearchhistory';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will delete 3 months or more older transaction report search history records.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->info("Deleting the transaction report search history record before 3 months...");
        Log::channel('delete-transaction-report-history')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Deleting the transaction report search history record before 3 months.");
        $threeMonthsAgo = Carbon::now()->subMonths(3)->format('Y-m-d H:i:s');
        $disk = Storage::disk('s3');
        // fetch the 3 months or more older transaction report search history records
        $records = AllStoreTransactionReportSearchHistory::whereDate('created_at', '<=', $threeMonthsAgo)->get();
        if(count($records) > 0) {
            foreach ($records as $record) {
                $record->is_deleted = 1;
                $record->save();
                $disk->delete($record->file_url);
            }
        }
        Log::channel('delete-transaction-report-history')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction report search history records of older than 3 months or more, is deleted successfully.");
        $this->info("Transaction report search history records of older than 3 months or more, is deleted successfully.");
    }
}
