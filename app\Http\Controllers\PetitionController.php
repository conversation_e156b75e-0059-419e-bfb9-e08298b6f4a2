<?php

namespace App\Http\Controllers;

use App\Http\Factories\EmailExecutor\EmailExecutorFactory;
use App\Models\MerchantStores;
use App\Models\Petition;
use App\Models\CrewTipHistory;
use App\Models\PetitionDetail;
use App\Models\PetitionDetailHistory;
use App\Models\PetitionShareDetail;
use App\Models\UserLifetimeReward;
use App\Models\UserRewardUsageHistory;
use Carbon\Carbon;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PetitionController extends Controller
{
    protected $emailexecutor;
    protected $client;
    protected $placesApiUrl;
    protected $apiKey;

    /**
     * Create a new controller instance.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return void
     */
    public function __construct(Request $request)
    {
        $this->emailexecutor = new EmailExecutorFactory();
        $this->client       = new Client();                                                 // Initialize Guzzle Client
        $this->placesApiUrl = "https://maps.googleapis.com/maps/api/place/textsearch/json"; // API URL
        $this->apiKey       = env('GOOGLE_PLACES_API_KEY');                                 // Store API key in .env
    }

    private function _petitionExistsCheck($request)
    {

        $awaiting = getStatus(AWAITING_ADMIN_APPROVAL);
        $rejected = getStatus(REJECTED);
        $existingPetition = Petition::where('store_name', $request->get('store_name'))
            ->where('street_address', $request->get('street_address'))
            ->where('city', $request->get('city'))
            ->where('state', $request->get('state'))
            ->where('zipcode', $request->get('zipcode'))
            ->first();

        if ($existingPetition) {
            // Check if the petition is awaiting admin approval
            if ($existingPetition->status_id == $awaiting) {
                Log::info(__METHOD__ . " (" . __LINE__ . ") - Petition already exists and is awaiting admin approval.");
                $message = trans('message.petition_already_exists_in_awaiting_approval'); // Message for petitions awaiting approval
                return ['status' => FAIL, 'message' => $message, 'data' => null];
            } else if ($existingPetition->status_id == $rejected) {
                Log::info(__METHOD__ . " (" . __LINE__ . ") - Petition already exists but was rejected by admin.");
                $message = trans('message.petition_already_rejected'); // New message key for rejected petitions
                return ['status' => FAIL, 'message' => $message, 'data' => null];
            }
            Log::info(__METHOD__ . " (" . __LINE__ . ") - Petition already exists.");

            $message = trans('message.petition_already_exists');
            return ['status' => 598, 'message' => $message, 'data' => $existingPetition->id];
        }
        $message = trans('message.petition_not_found');
        return ['status' => SUCCESS, 'message' => $message, 'data' => null];
    }
    
    public function petitionExistsCheck(Request $request)
    {
        // Validating input requests
        $this->validate($request, [
            'store_name'                   => VALIDATION_REQUIRED,
            'street_address'               => VALIDATION_REQUIRED,
            'city'                         => VALIDATION_REQUIRED,
            'state'                        => VALIDATION_REQUIRED,
            'zipcode'                      => VALIDATION_REQUIRED
        ]);
        $petitionExits =  $this->_petitionExistsCheck($request);
        return renderResponse($petitionExits['status'], $petitionExits['message'], $petitionExits['data']);
    }
    /**
     * This function is used to create a petition.
     * It validates the input requests and then inserts the data into the petitions and petition_details table.
     * If the insertion is successful, it commits the transaction and returns a success response.
     * If the insertion fails, it rolls back the transaction and returns a failure response.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createPetition(Request $request)
    {

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Create Petition process started...");

        // Validating input requests
        $this->validate($request, [
            'store_name'                   => VALIDATION_REQUIRED,
            'street_address'               => VALIDATION_REQUIRED,
            'city'                         => VALIDATION_REQUIRED,
            'state'                        => VALIDATION_REQUIRED,
            'zipcode'                      => VALIDATION_REQUIRED,
            'primary_contact_person_firstname' => VALIDATION_REQUIRED,
            'primary_contact_person_lastname'  => VALIDATION_REQUIRED,
            'primary_contact_person_email' => VALIDATION_REQUIRED,
            'primary_contact_person_title' => VALIDATION_REQUIRED,
            'status_id'                    => VALIDATION_REQUIRED,
        ]);

        $rejected = getStatus(REJECTED);
        $awaiting = getStatus(AWAITING_ADMIN_APPROVAL);
        $status_id = $request->get('status_id');
        if ($status_id == "ACTIVE_CODE") {
            $status_id = getStatus(ACTIVE_CODE);
        } else if ($status_id == 'PENDING') {
            $status_id = getStatus(PENDING);
        }
        
        $petitionExits =  $this->_petitionExistsCheck($request);
        if ($petitionExits['status'] != SUCCESS) {
            return renderResponse($petitionExits['status'], $petitionExits['message'], $petitionExits['data']);
        }

        $user = Auth::user();
        // Check the count of signed petitions by this consumer
        $signedCount = PetitionDetail::join('petitions', 'petitions.id', '=', 'petition_details.petition_id')->where('petition_details.consumer_id', $user->user_id)->where('petitions.status_id', '!=', $rejected)->whereNull('petition_details.deleted_at')->count();

        if ($signedCount >= env('MAX_PETITION_SIGN_ALLOWED')) {
            Log::info(__METHOD__ . "(" . __LINE__ . ") - " . 'Consumer has already signed 5 petitions.', [
                'consumer_id'  => $user->user_id,
                'signed_count' => $signedCount,
            ]);

            $message = "You’ve reached the limit for signing petitions.";
            return renderResponse(FAIL, $message, null);
        }

        // Fetch all stores from the merchant_stores table
        $existingStores = MerchantStores::join('status_master', 'merchant_stores.status', '=', 'status_master.id')->where('status_master.code', ACTIVE)->select('merchant_stores.*')->get();
        // Iterate over each store to find a matching store
        foreach ($existingStores as $store) {
            // Calculate name and address match percentage
            $storeNameMatch = calculateMatchPercentage($request->get('store_name'), $store->retailer);
            $addressMatch = calculateMatchPercentage(
                $request->get('street_address') . ' ' . $request->get('city') . ' ' . $request->get('state') . ' ' . $request->get('zipcode'),
                $store->address . ' ' . $store->city . ' ' . $store->state . ' ' . $store->zip
            );

            // Combined match percentage (weighted more towards address)
            $overallMatchPercentage = ($addressMatch * 0.6) + ($storeNameMatch * 0.4);

            // If the overall match percentage is greater than 60, set status to awaiting approval
            if ($overallMatchPercentage > 60) {
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Matching store found with Store ID: " . $store->id . " with overall match percentage: " . $overallMatchPercentage . ". Setting petition status to Awaiting Admin Approval.");
                $status_id = $awaiting;
                break;
            }
        }

        DB::beginTransaction();
        try {
            // Prepare the data for the petitions table
            $petitionData = [
                'petition_number'                  => strtoupper(substr(md5(uniqid()), 0, 8)),
                'consumer_id'                      => $user->user_id,
                'store_name'                       => $request->get('store_name'),
                'street_address'                   => $request->get('street_address'),
                'city'                             => $request->get('city'),
                'state'                            => $request->get('state'),
                'zipcode'                          => $request->get('zipcode'),
                'lat'                              => $request->get('lat'),
                'long'                             => $request->get('long'),
                'primary_contact_person_firstname' => ucfirst($request->get('primary_contact_person_firstname')),
                'primary_contact_person_lastname'  => ucfirst($request->get('primary_contact_person_lastname')),
                'primary_contact_person_email'     => $request->get('primary_contact_person_email'),
                'primary_contact_person_title'     => $request->get('primary_contact_person_title'),
                'status_id'                        => $status_id,
                'created_at'                       => Carbon::now(),
                'updated_at'                       => Carbon::now(),
            ];

            // Insert into the petitions table
            $petition = Petition::create($petitionData);
            $referral_code = generateUniqueReferralCode();
            // Prepare the data for the petition_details table
            $petitionDetailData = [
                'petition_id'   => $petition->id,
                'consumer_id'   => $user->user_id,
                'type'          => $request->get('type'),
                'referral_code' => $referral_code,
                'created_at'    => Carbon::now(),
                'updated_at'    => Carbon::now(),
            ];

            // Insert into the petition_details table
            $petitionDetail = PetitionDetail::create($petitionDetailData);
            
            // Reward the consumer
            rewardConsumerForPetition($petitionDetail, 'signed');

            $emailParams = [
                'name' => "{$request->get('primary_contact_person_firstname')} {$request->get('primary_contact_person_lastname')}",
                'email' => $request->get('primary_contact_person_email'),
                'petition_url' => ENV('CONSUMER_APP_URL') . 'petition/'.base64_encode($petition->id),
            ];
            $code = createShortUrl($emailParams['petition_url']);
            if ($code) {
                $emailParams['petition_url'] = ENV('CONSUMER_APP_URL') . 's/'.$code;
            }
            $this->emailexecutor->sendPetetionCreatedNotificationToStore($emailParams);
            DB::commit();

            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Petition created successfully.");
            $message = trans('message.petition_created_success');
            return renderResponse(SUCCESS, $message, $petitionDetailData);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while creating petition.", [EXCEPTION => $e]);

            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Failed to create Petition.");
            $message = trans('message.falied_to_create_petition');
            return renderResponse(FAIL, $message, null);
        }
    }

    /**
     * Fetches petitions for the authenticated user or top 5 pending petitions with most signatures
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function petitionLists(Request $request)
    {
        Log::info(__METHOD__ . "(" . __LINE__ . ") - " . "Fetching petitions...");
        // Validating input requests
        $this->validate($request, [
            'status' => VALIDATION_REQUIRED,
            'top_five'  => VALIDATION_NULLABLE . '|' . VALIDATION_BOOLEAN,
        ]);

        $user     = Auth::user();
        $status = $request->get('status');
        $topFive  = $request->get('top_five', false); // Default to false if not provided
        $rewardWheelDb = env('DB_DATABASE_REWARD_WHEEL');
        $rejectedStatus = getStatus(REJECTED);

        // Check the count of signed petitions by this consumer
        $consumer_active_petition_count = PetitionDetail::join('petitions', 'petitions.id', '=', 'petition_details.petition_id')->where('petition_details.consumer_id', $user->user_id)->where('petitions.status_id', '!=', $rejectedStatus)->whereNull('petition_details.deleted_at')->count();

        Log::info(__METHOD__ . "(" . __LINE__ . ") - " . "Fetching petitions for user_id: {$user->id}, status_id: {$status}, top_five: {$topFive}");

        try {
            $pending = getStatus(PENDING);
            $active = getStatus(ACTIVE_CODE);
            if ($status == 'active') {
                $statusId = $active;
            } else if ($status == 'pending') {
                $statusId = $pending;
            }
            if ($topFive) {
                // pull lat/long (nullable)
                $lat = $request->get('lat');
                $long = $request->get('long');    
                // build base select
                $select = [
                    'petitions.*',
                    'mypd.type as type',
                    'sm.code as status_code',
                ];

                $lat_long = false;
                // if lat/long provided, add distance formula
                if (
                    ! is_null($lat) && ! is_null($long)
                    && strlen(trim($lat)) > 0
                    && strlen(trim($long)) > 0
                ) {
                    $lat_long = true;
                }

                if ($lat_long) {
                    $lat    = (float) $lat;
                    $long    = (float) $long;
                    $radius = 6371; // km

                    $select[] = DB::raw("(
                        {$radius} * acos(
                            cos(radians({$lat}))
                        * cos(radians(petitions.lat))
                        * cos(radians(petitions.long) - radians({$long}))
                        + sin(radians({$lat}))
                        * sin(radians(petitions.lat))
                        )
                    ) AS distance_km");
                }
                // Get current page and per page from request
                $page = $request->input('page', 1);
                $perPage = $request->input('per_page', 10);
                $offset = ($page - 1) * $perPage; // Calculate the offset


                // Base query
                $query = Petition::select($select)
                    ->leftJoin('petition_details as pd', function ($join) {
                        $join->on('petitions.id', '=', 'pd.petition_id')
                            ->whereNull('pd.deleted_at');
                    })
                    ->join('status_master as sm', 'petitions.status_id', '=', 'sm.id')
                    ->leftJoin('petition_details as mypd', function ($query) use ($user) {
                        $query->on('petitions.id', '=', 'mypd.petition_id')
                            ->whereNull('mypd.deleted_at')
                            ->where('mypd.consumer_id', $user->user_id);
                    })
                    ->where('petitions.status_id', '!=', $rejectedStatus)
                    ->whereNull('petitions.onboarded_date')
                    ->groupBy('petitions.id');

                    // Clone for total count
                    $totalCountQuery = (clone $query)->get();
                    $totalCount = $totalCountQuery->count();
                    // apply distance ordering only if lat/long provided
                    if ($lat_long) {
                        $query->orderBy('distance_km', 'asc');
                    } else {
                        $query->orderBy('petitions.created_at', 'desc');
                    }
                    // Apply limit/offset
                    $data = $query
                        ->withCount('petitionDetails as signed_users_count')
                        ->skip($offset)     // OFFSET
                        ->take($perPage)    // LIMIT
                        ->get();

                    $petitions = [
                        'data' => $data,
                        'current_page' => $page,
                        'per_page' => $perPage,
                        'total' => $totalCount,
                        'total_pages' => ceil($totalCount / $perPage),
                        'consumer_active_petition_count' => $consumer_active_petition_count
                    ];

                    $message = trans('message.top_5_pending_petitions_fetched');
                    return renderResponse(SUCCESS, $message, $petitions);
            } else {
                // Fetch petitions for the authenticated user
                $petitions = Petition::select(
                        'petitions.*', 'mypd.type as type',
                        'pd.created_at as signed_on',
                        DB::raw('0 as show_accordian_address'),
                        'sm.code as status_code',
                        DB::raw('COALESCE(SUM(usage.reward_point), 0) as total_reward_points'),
                        DB::raw('COALESCE(SUM(CASE WHEN usage.points_type = "' . REWARD_POINTS . '" THEN 1 ELSE 0 END), 0) as total_reward_count')
                    )
                    ->join('status_master as sm', 'petitions.status_id', '=', 'sm.id')
                    ->join('petition_details as pd', function ($join) {
                        $join->on('petitions.id', '=', 'pd.petition_id')
                            ->whereNull('pd.deleted_at');
                    })
                    ->leftJoin('petition_details as mypd', function ($query) use ($user) {
                        $query->on('petitions.id', '=', 'mypd.petition_id')->whereNull('mypd.deleted_at')->where('mypd.consumer_id', $user->user_id);
                    })
                    ->leftjoin("$rewardWheelDb.user_reward_usage_history as usage", function ($join) use ($user) {
                        $join->on('pd.petition_id', '=', 'usage.petition_id')
                            ->where('usage.user_id', '=', $user->user_id)->where('usage.entry_type', '=', CREDIT)->whereIn('usage.points_type', [REWARD_POINTS, PETITION_POINTS]);
                    })
                    ->where('pd.consumer_id', $user->user_id)
                    ->when(true, function ($query) use ($statusId, $active) {
                        return $statusId == $active
                            ? $query->whereNotNull('petitions.onboarded_date')
                            : $query->whereNull('petitions.onboarded_date');
                    })

                    // ->where('petitions.status_id', $statusId)
                    ->where('petitions.status_id', '!=', $rejectedStatus)
                    ->groupBy('petitions.id')
                    ->orderByDesc('mypd.created_at')
                    ->withCount('petitionDetails as signed_users_count')
                    ->withCount([
                        'petitionRewards as unclaimed_rewards_count' => function ($query) use ($user) {
                            $query->where('consumer_id', $user->user_id)->where('is_expired', 0)
                                  ->whereNull('claimed_at');
                        }
                    ])
                    ->get();

                $message = trans('message.pending_petitions_fetched');
                // Log petition fetch count
                Log::info(__METHOD__ . "(" . __LINE__ . ") - " . $message, ['consumer_id' => $user->user_id ?? null, 'count' => $petitions->count()]);
    
                return renderResponse(SUCCESS, $message, $petitions);
            }

        } catch (\Exception $e) {
            // Log any error that occurs during the query execution
            Log::error(__METHOD__ . "(" . __LINE__ . ") - " . 'Error fetching petitions', ['consumer_id' => $user->user_id ?? null, 'error' => $e->getMessage()]);
            $message = trans('message.failed_to_fetch_petitions');
            return renderResponse(FAIL, $message, null);
        }
    }

    /**
     * Share a petition with multiple email addresses.
     *
     * Validates the provided email addresses and petition ID, then creates
     * petition share records for each valid email address. The emails can be
     * provided as a comma-separated string.
     *
     * @param \Illuminate\Http\Request $request The request object containing
     *                                          'email' and 'petition_id'
     * @return \Illuminate\Http\Response A response indicating the success or failure
     *                                   of sharing the petition
     */
    public function petitionShare(Request $request)
    {
        Log::info(__METHOD__ . "(" . __LINE__ . ") - Petition share initiated...");

        $type = $request->input('type');

        $rules = [
            'type'        => [VALIDATION_REQUIRED],
            'petition_id' => [VALIDATION_REQUIRED],
        ];

        if ($type === 'email') {
            $rules['email'] = [
                VALIDATION_REQUIRED,
                'string',
                function ($attribute, $value, $fail) {
                    $emails = explode(',', $value);
                    foreach ($emails as $email) {
                        $trimmedEmail = trim($email);
                        if (!filter_var($trimmedEmail, FILTER_VALIDATE_EMAIL)) {
                            $fail("The email {$trimmedEmail} is not a valid email address.");
                        }
                    }
                },
            ];
        } elseif ($type === 'phone') {
            $rules['email'] = [
                VALIDATION_REQUIRED,
                'string',
                function ($attribute, $value, $fail) {
                    $phones = explode(',', $value);
                    foreach ($phones as $phone) {
                        $trimmedPhone = preg_replace('/\D/', '', $phone);
                        if (!preg_match('/^[0-9]{10,15}$/', $trimmedPhone)) {
                            $fail("The phone number {$phone} is not valid. It must be 10-15 digits.");
                        }
                    }
                },
            ];
        }

        $this->validate($request, $rules);

        $user = Auth::user();
        $recipients = explode(',', $request->get('email')); // Shared input for email or phone

        foreach ($recipients as $recipient) {
            $trimmedRecipient = trim($recipient);

            PetitionShareDetail::create([
                'consumer_id' => $user->user_id,
                'petition_id' => $request->petition_id,
                'recipient'   => $trimmedRecipient,
                'type'        => $type,
            ]);
        }

        Log::info(__METHOD__ . "(" . __LINE__ . ") - Petition shared successfully.");

        $message = trans('message.petition_shared_success');
        return renderResponse(SUCCESS, $message, null);
    }


    /**
     * Sign a petition by a consumer.
     *
     * This method allows a consumer to sign a petition. It validates the input,
     * checks if the consumer has not exceeded the maximum allowed petition signs,
     * and records the signature in the database.
     *
     * @param \Illuminate\Http\Request $request The request object containing
     *                                          'petition_id', 'signed_from', and 'type'
     * @return \Illuminate\Http\Response A response indicating the success or failure
     *                                   of signing the petition
     */
    public function petitionSign(Request $request)
    {
        Log::info(__METHOD__ . "(" . __LINE__ . ") - " . 'Petition sign initiated...');

        // Validating input requests
        $this->validate($request, [
            'petition_id' => VALIDATION_REQUIRED,
        ]);

        $user = Auth::user();

        try {
            $rejected = getStatus(REJECTED);
            $signedFrom = $request->get('signed_from');
            $petitionId = $request->get('petition_id');
            // Check the count of signed petitions by this consumer
            $signedCount = PetitionDetail::join('petitions', 'petitions.id', '=', 'petition_details.petition_id')->where('petition_details.consumer_id', $user->user_id)->where('petitions.status_id', '!=', $rejected)->whereNull('petition_details.deleted_at')->count();

            if ($signedCount >= env('MAX_PETITION_SIGN_ALLOWED')) {
                Log::info(__METHOD__ . "(" . __LINE__ . ") - " . 'Consumer has already signed ' . env('MAX_PETITION_SIGN_ALLOWED') . ' petitions.', [
                    'consumer_id'  => $user->user_id,
                    'signed_count' => $signedCount,
                ]);

                $message = "You’ve reached the limit for signing petitions.";
                return renderResponse(FAIL, $message, null);
            }
            // Check the total number of consumers who have signed this petition
            $signedUserCount = PetitionDetail::where('petition_id', $petitionId)
            ->whereNull('deleted_at')
            ->count();

            if ($signedUserCount >= env('MAX_USERS_ALLOWED_TO_SIGN_THIS_PETITION')) {
                Log::info(__METHOD__ . "(" . __LINE__ . ") - Petition has already reached its signing limit of " . env('MAX_USERS_ALLOWED_TO_SIGN_THIS_PETITION') . '.', [
                    'petition_id'       => $petitionId,
                    'signed_user_count' => $signedUserCount,
                ]);

                $message = "This petition has already reached its signing limit of " . env('MAX_USERS_ALLOWED_TO_SIGN_THIS_PETITION') . " users.";
                return renderResponse(FAIL, $message, null);
            }

            // ❗ Check if this consumer is the mayor of this petition
            $petition = PetitionDetail::where('petition_id', $petitionId)
                ->where('consumer_id', $user->user_id)
                ->whereNull('deleted_at')
                ->first();

            if ($petition && $petition->type === MAYOR) {
                Log::info(__METHOD__ . "(" . __LINE__ . ") - " . 'Consumer is the mayor of this petition and cannot sign it.', [
                    'consumer_id' => $user->user_id,
                    'petition_id' => $petitionId,
                ]);

                $message = 'You are the mayor of this petition, you cannot sign it.';
                return renderResponse(FAIL, $message, null);
            }

            // ❗ Check if this consumer already signed this petition
            $alreadySigned = PetitionDetail::where('petition_id', $petitionId)
                ->where('consumer_id', $user->user_id)
                ->whereNull('deleted_at')
                ->exists();
            
            if ($alreadySigned) {
                Log::info(__METHOD__ . "(" . __LINE__ . ") - " . 'Consumer already signed this petition.', [
                    'consumer_id' => $user->user_id,
                    'petition_id' => $petitionId,
                ]);

                $message = trans('message.petition_already_signed');
                return renderResponse(FAIL, $message, null);
            }
            // Generate a unique referral code for the consumer for this petition
            $deletedSigned = PetitionDetail::where('petition_id', $petitionId)
                ->where('consumer_id', $user->user_id)
                ->whereNotNull('referral_code')
                ->first();

            if ($deletedSigned) {
                $referral_code = $deletedSigned->referral_code;
            } else {
                $referral_code = generateUniqueReferralCode();
            }

            // Get number of current signers for the given petition
            $totalSigners = PetitionDetail::where('petition_id', $petitionId)->whereNull('deleted_at')->count();

            // Determine the type based on number of signers
            if ($totalSigners == 0) {
                $type = MAYOR;
            } elseif ($totalSigners < 6) {
                $type = CREW_LEADER;
            } else {
                $type = CREW_MEMBER;
            }
            // Insert data into the PetitionDetail model
            $petitionDetail                 = new PetitionDetail();
            $petitionDetail->petition_id    = $petitionId;
            $petitionDetail->referral_code  = $referral_code;
            $petitionDetail->consumer_id    = $user->user_id;
            $petitionDetail->signed_from    = ($signedFrom && $signedFrom != $user->user_id) ? $signedFrom : null;
            $petitionDetail->type           = $type;
            $petitionDetail->save();

            // Reward the consumer
            rewardConsumerForPetition($petitionDetail, 'signed');

            Log::info(__METHOD__ . "(" . __LINE__ . ") - " . 'Petition signed successfully.');

            $message = trans('message.petition_signed_success');
            return renderResponse(SUCCESS, $message, $petitionDetail);
        } catch (\Exception $e) {
            Log::info(__METHOD__ . "(" . __LINE__ . ") - " . 'Failed to sign the petition.', [
                'consumer_id' => $user->user_id,
                'error'       => $e->getMessage(),
            ]);

            $message = trans('message.failed_to_create_petition');
            return renderResponse(FAIL, $message, $e->getMessage());
        }
    }

    /**
     * Add additional contact details to a petition
     *
     * Validates and updates a petition with secondary contact information including
     * name, email and phone number. Only allows updating existing petitions.
     *
     * @param \Illuminate\Http\Request $request The request containing petition_id and
     *                                         secondary contact details
     * @return \Illuminate\Http\Response Response indicating success or failure of
     *                                    adding the additional contact
     */
    public function addAdditionalContact(Request $request)
    {
        Log::info(__METHOD__ . "(" . __LINE__ . ") - " . 'Additional Contact addition initiated...');

        // Validating input requests
        $this->validate($request, [
            'petition_id'                        => VALIDATION_REQUIRED . '|exists:petitions,id',
            'secondary_contact_person_firstname' => VALIDATION_REQUIRED . '|' . VALIDATION_STRING . '|max:255',
            'secondary_contact_person_lastname'  => VALIDATION_REQUIRED . '|' . VALIDATION_STRING . '|max:255',
            'secondary_contact_person_email'     => VALIDATION_REQUIRED . '|' . VALIDATION_EMAIL . '|max:255',
            'secondary_contact_person_title'     => VALIDATION_REQUIRED . '|' . VALIDATION_STRING . '|max:255',
        ]);
        try {
            $user = Auth::user();

            // Fetch the petition
            $petition = Petition::find($request->petition_id);

            if (! $petition) {
                Log::error(__METHOD__ . "(" . __LINE__ . ") - " . 'Petition not found.');
                return renderResponse(PAGE_NOT_FOUND, 'Petition not found.', null);
            }

            // Update the petition with additional contact details
            $petition->update([
                'secondary_contact_person_firstname' => ucfirst($request->secondary_contact_person_firstname),
                'secondary_contact_person_lastname'  => ucfirst($request->secondary_contact_person_lastname),
                'secondary_contact_person_email'     => $request->secondary_contact_person_email,
                'secondary_contact_person_title'     => $request->secondary_contact_person_title,
            ]);
            $emailParams = [
                'name' => "{$request->secondary_contact_person_firstname} {$request->secondary_contact_person_lastname}",
                'email' => $request->secondary_contact_person_email,
                'petition_url' => ENV('CONSUMER_APP_URL') . 'petition/'.base64_encode($petition->id),
            ];
            $code = createShortUrl($emailParams['petition_url']);
            if ($code) {
                $emailParams['petition_url'] = ENV('CONSUMER_APP_URL') . 's/'.$code;
            }
            $this->emailexecutor->sendPetetionCreatedNotificationToStore($emailParams);

            Log::info(__METHOD__ . "(" . __LINE__ . ") - " . 'Additional Contact successfully added.');
            $message = trans('message.additional_contact_added_success');

            return renderResponse(SUCCESS, $message, null);
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . __LINE__ . ") - " . 'Error adding additional contact: ' . $e->getMessage());
            return renderResponse(FAIL, 'An error occurred while adding the additional contact.', $e->getMessage());
        }
    }

    /**
     * Search for stores and active/provisioned petitions by store name
     *
     * Validates the provided store name, page and per_page, then
     * fetches the matching active stores and active/provisioned petitions.
     * Merges the results, formats them and applies pagination. The
     * response includes the paginated results and pagination data.
     *
     * @param \Illuminate\Http\Request $request The request object containing
     *                                          'store_name', 'page' and 'per_page'.
     * @return \Illuminate\Http\Response A response indicating the success or failure
     *                                   of the store search, containing the paginated
     *                                   results and pagination data.
     */
    public function searchStore(Request $request)
    {
        $this->validate($request, [
            'store_name' => VALIDATION_REQUIRED . '|' . VALIDATION_STRING,
            'page'       => VALIDATION_NULLABLE . '|' . VALIDATION_INTEGER . '|min:1',
            'per_page'   => VALIDATION_NULLABLE . '|' . VALIDATION_INTEGER . '|min:1|max:50',
        ]);

        $storeName = $request->get('store_name');
        $perPage   = $request->get('per_page', 10);
        $page      = $request->get('page', 1);
        $user      = Auth::user();
        Log::info(__METHOD__ . " - Searching for store", ['store_name' => $storeName, 'page' => $page, 'per_page' => $perPage]);

        try {
            $stores = MerchantStores::select('merchant_stores.id', 'merchant_stores.retailer as store_name', 'merchant_stores.address as street_address', 'merchant_stores.city', 'merchant_stores.state', 'merchant_stores.zip as zipcode', 'status_master.status as store_status', 'merchant_stores.created_at')
                ->join('status_master', 'merchant_stores.status', '=', 'status_master.id')
                ->where('merchant_stores.retailer', 'LIKE', "%$storeName%")
                ->where('status_master.code', ACTIVE)
                ->get();

            $petitions = Petition::select('petitions.id', 'mypd.type', 'petitions.store_name', 'petitions.street_address', 'petitions.city', 'petitions.state', 'petitions.zipcode', 'status_master.code as petition_status_code', 'petitions.created_at', DB::raw('COUNT(petition_details.id) as signer_count', 'mypd.type'))
                ->leftJoin('petition_details', function ($query) use ($user) {
                    $query->on('petitions.id', '=', 'petition_details.petition_id')->whereNull('petition_details.deleted_at');
                })
                ->leftJoin('petition_details as mypd', function ($query) use ($user) {
                    $query->on('petitions.id', '=', 'mypd.petition_id')->where('mypd.consumer_id', $user->user_id)->whereNull('mypd.deleted_at');
                })
                ->join('status_master', 'petitions.status_id', '=', 'status_master.id')
                ->where('petitions.store_name', 'LIKE', "%$storeName%")
                ->whereIn('status_master.code', [PENDING, ACTIVE, PROVISIONED, AWAITING_ADMIN_APPROVAL])
                ->whereNull('petitions.merchant_store_id')
                ->groupBy('petitions.id')
                ->orderByRaw("CASE 
                    WHEN status_master.code = '".PENDING."' THEN 1 
                    WHEN status_master.code = '".ACTIVE."' THEN 2 
                    WHEN status_master.code = '".PROVISIONED."' THEN 3 
                    WHEN status_master.code = '".AWAITING_ADMIN_APPROVAL."' THEN 4 
                    ELSE 5 
                  END")
                ->get();
            $hasActiveStoreFound = false;
            $responseData = [];

            foreach ($petitions as $petition) {
                // CanPay have registers merchants before onboarding different merchant through the crew.
                // We've already fetched the active store from the Merchant Store table.
                // However, the petition table may still contain stores with statuses like ACTIVE, PROVISIONED, or AWAITING_ADMIN_APPROVAL.
                // Therefore, if any store in the petition table has one of these statuses, we prioritize the data from the Merchant Store table.
                // This is necessary due to the status_code precedence defined above and keeping the status consistent.
                if(($petition->petition_status_code == PROVISIONED || $petition->petition_status_code == ACTIVE || $petition->petition_status_code == AWAITING_ADMIN_APPROVAL) && !$hasActiveStoreFound) {
                    foreach ($stores as $store) {
                        $formattedAddress = trim("{$store->street_address}, {$store->city}, {$store->state} {$store->zipcode}", ", ");
                        $responseData[]   = [
                            'id' => $store->id,
                            'store_name' => $store->store_name,
                            'address' => $formattedAddress,
                            'accept_canpay' => 1,
                            'petition_status_code' => null,
                            'total_signer' => null,
                            'pending_petition' => 0,
                            'in_discussion' => 0,
                            'in_admin_approval' => 0,
                            'created_at' => Carbon::parse($store->created_at)->format('Y-m-d H:i:s'),
                            'street_address' => $store->street_address,
                            'city' => $store->city,
                            'state' => $store->state,
                            'zipcode' => $store->zipcode
                        ];
                        $hasActiveStoreFound = true;
                    }                   
                }
                $formattedAddress = trim(($petition->apt_number ? "{$petition->apt_number}, " : "") . "{$petition->street_address}, {$petition->city}, {$petition->state} {$petition->zipcode}", ", ");
                $responseData[]   = [
                    'id' => $petition->id,
                    'store_name' => $petition->store_name,
                    'address' => $formattedAddress,
                    'accept_canpay' => 0,
                    'petition_status_code' => $petition->petition_status_code,
                    'pending_petition' => ($petition->petition_status_code == PENDING) ? 1 : 0,
                    'in_discussion' => ($petition->petition_status_code == PROVISIONED) ? 1 : 0,
                    'in_admin_approval' => ($petition->petition_status_code == AWAITING_ADMIN_APPROVAL) ? 1 : 0,
                    'total_signer' => $petition->signer_count,
                    'created_at' => Carbon::parse($petition->created_at)->format('Y-m-d H:i:s'),
                    'street_address' => $petition->street_address,
                    'city' => $petition->city,
                    'state' => $petition->state,
                    'zipcode' => $petition->zipcode,
                    'type' => $petition->type
                ];
            }
            // if the petition table is empty then directly add the data from the merchant table.
            if(!$hasActiveStoreFound){
                    foreach ($stores as $store) {
                        $formattedAddress = trim("{$store->street_address}, {$store->city}, {$store->state} {$store->zipcode}", ", ");
                        $responseData[]   = [
                            'id' => $store->id,
                            'store_name' => $store->store_name,
                            'address' => $formattedAddress,
                            'accept_canpay' => 1,
                            'petition_status_code' => null,
                            'total_signer' => null,
                            'pending_petition' => 0,
                            'in_discussion' => 0,
                            'in_admin_approval' => 0,
                            'created_at' => Carbon::parse($store->created_at)->format('Y-m-d H:i:s'),
                            'street_address' => $store->street_address,
                            'city' => $store->city,
                            'state' => $store->state,
                            'zipcode' => $store->zipcode
                        ];
                        $hasActiveStoreFound = true;
                    }                     
            }

            $totalResults = count($responseData);

            $paginatedResults = new LengthAwarePaginator(array_slice($responseData, ($page - 1) * $perPage, $perPage), $totalResults, $perPage, $page, ['path' => url('/consumer/search-store')]);
            
            Log::info(__METHOD__ . " - Store search completed", ['store_name' => $storeName, 'total_results' => $totalResults, 'current_page' => $page]);

            return response()->json(['status' => SUCCESS, 'message' => trans('message.store_search_completed'), 'data' => $paginatedResults->items(), 'pagination' => ['current_page' => $paginatedResults->currentPage(), 'per_page' => $paginatedResults->perPage(), 'total_results' => $paginatedResults->total(), 'last_page' => $paginatedResults->lastPage(), 'next_page_url' => $paginatedResults->nextPageUrl()]]);
        } catch (\Exception $e) {
            Log::error(__METHOD__ . " - Error searching for store", ['store_name' => $storeName, 'error' => $e->getMessage()]);
            return renderResponse(FAIL, trans('message.failed_to_search_store'), null);
        }
    }

    /**
     * Search for store suggestions using the Google Places API.
     *
     * Validates the provided store name and sends a request to the Google
     * Places API to fetch store suggestions based on the name. The function
     * logs the initiation of the API request and handles the response by
     * extracting and formatting the store suggestions. If the API request
     * fails or returns an error, an appropriate error response is returned.
     *
     * @param \Illuminate\Http\Request $request The request object containing
     *                                          'store_name'.
     * @return \Illuminate\Http\Response A response containing the status,
     *                                   message, and store suggestion data
     *                                   or an error message.
     */
    public function searchPlaces(Request $request)
    {
        // Validate input
        $this->validate($request, [
            'store_name' => VALIDATION_REQUIRED . '|' . VALIDATION_STRING,
        ]);

        $storeName = $request->input('store_name');

        Log::info(__METHOD__ . " - Searching Google Places API", ['store_name' => $storeName]);

        try {
            // Make Google Places API request using Guzzle
            $response = $this->client->request('GET', $this->placesApiUrl, [
                'query' => [
                    'query' => $storeName,
                    'key'   => $this->apiKey,
                ],
            ]);

            // Decode JSON response
            $places = json_decode($response->getBody(), true);

            // Check if API returned results
            if (! isset($places['status']) || $places['status'] !== 'OK') {
                $message = trans('message.failed_to_fetch_store_suggestions');
                Log::error(__METHOD__ . " - Failed to fetch store suggestions from Google Places", ['store_name' => $storeName, 'error' => $places['status'] ?? 'UNKNOWN_ERROR']);
                return renderResponse(FAIL, $message, $places['status'] ?? 'UNKNOWN_ERROR');
            }

            // Extract & format store suggestions
            $formattedResults = [];
            foreach ($places['results'] as $place) {
                // Split the formatted address
                $addressComponents = explode(',', $place['formatted_address'] ?? '');
                $streetAddress     = trim($addressComponents[0] ?? null);
                $city              = trim($addressComponents[1] ?? null);
                $stateZip          = explode(' ', trim($addressComponents[2] ?? ''));
                $state             = $stateZip[0] ?? null;
                $zipcode           = $stateZip[1] ?? null;
                $aptNumber         = null; // Apt number not available from Google Places

                // Construct formatted address
                $formattedAddress = trim(($aptNumber ? "{$aptNumber}, " : "") . "{$streetAddress}, {$city}, {$state} {$zipcode}", ", ");

                // Check if the store is already registered in the database
                $existingStore = MerchantStores::select('merchant_stores.id', 'merchant_stores.retailer as store_name', 'merchant_stores.address as street_address', 'merchant_stores.city', 'merchant_stores.state', 'merchant_stores.zip as zipcode', 'status_master.status as store_status', 'merchant_stores.created_at')
                    ->join('status_master', 'merchant_stores.status', '=', 'status_master.id')
                    ->where('merchant_stores.retailer', 'LIKE', "%{$place['name']}%")
                    ->where('address', 'LIKE', "%{$streetAddress}%")
                    ->where('city', 'LIKE', "%{$city}%")
                    ->where('state', 'LIKE', "%{$state}%")
                    ->where('zip', 'LIKE', "%{$zipcode}%")
                    ->where('status_master.code', ACTIVE)
                    ->first();

                $acceptCanpay = $existingStore ? 1 : 0;
                $pendingPetition = 0;
                $inDiscussion = 0;
                $type = "None";
                $user = Auth::user();
                if (empty($existingStore)) {
                    // Fetch petition status dynamically
                    $petition = Petition::select('status_master.status as petition_status','mypd.type')
                        ->join('petition_details', 'petitions.id', '=', 'petition_details.petition_id')
                        ->leftJoin('petition_details as mypd', function ($query) use ($user) {
                            $query->on('petitions.id', '=', 'mypd.petition_id')->where('mypd.consumer_id', $user->user_id);
                        })
                        ->join('status_master', 'petitions.status_id', '=', 'status_master.id')
                        ->where('petitions.store_name', 'LIKE', "%{$place['name']}%")
                        ->where('petitions.street_address', 'LIKE', "%{$streetAddress}%")
                        ->where('petitions.city', 'LIKE', "%{$city}%")
                        ->where('petitions.state', 'LIKE', "%{$state}%")
                        ->where('petitions.zipcode', 'LIKE', "%{$zipcode}%")
                        ->groupBy('petitions.id')
                        ->first();
                    if ($petition) {
                        $pendingPetition = ($petition->petition_status === PENDING) ? 1 : 0;
                        $inDiscussion = ($petition->petition_status === PROVISIONED) ? 1 : 0;
                        $type = $petition->type;
                    }
                }

                $formattedResults[] = [
                    'store_name'        => $place['name'] ?? null,
                    'formatted_address' => $formattedAddress,
                    'street_address'    => $streetAddress,
                    'apt_number'        => $aptNumber,
                    'city'              => $city,
                    'state'             => $state,
                    'zipcode'           => $zipcode,
                    'place_id'          => $place['place_id'] ?? null,
                    'latitude'          => $place['geometry']['location']['lat'] ?? null,
                    'longitude'         => $place['geometry']['location']['lng'] ?? null,
                    'accept_canpay'     => $acceptCanpay,
                    'pending_petition'  => $pendingPetition,
                    'in_discussion'     => $inDiscussion,
                    'total_signer'      => null,
                    'type'              => $type,
                ];
            }

            Log::info(__METHOD__ . " - Store suggestions fetched successfully", ['store_name' => $storeName]);
            $message = trans('message.store_suggestions_fetched');
            return renderResponse(SUCCESS, $message, $formattedResults);
        } catch (RequestException $e) {
            Log::error(__METHOD__ . " - Error fetching store suggestions", ['store_name' => $storeName, 'error' => $e->getMessage()]);
            $message = trans('message.failed_to_fetch_store_suggestions');
            return renderResponse(FAIL, $message, $e->getMessage());
        }
    }


    /**
     * Retrieves the petition statistics including the number of pending petitions for the logged-in user,
     * total pending petitions, total signers, total stores that accept CanPay, and total lifetime rewards.
     * The statistics are logged and returned in the response.
     *
     * @param \Illuminate\Http\Request $request The request object.
     * @return \Illuminate\Http\Response A response containing the status, message, and petition statistics
     *                                   or an error message.
     */
    public function getPetitionStatistics(Request $request)
    {
        try {
            $user = Auth::user();

            Log::info(__METHOD__ . " - Fetching petition statistics...", ['user_id' => $user->user_id]);
            $type = $request->input('type');
            // Get Pending Petitions for Logged-in User
            $pendingStatus        = getStatus(PENDING);
            $rejectStatus = getStatus(REJECTED);
            $consumerTotalPetitionsCount = Petition::join('petition_details as pd', 'petitions.id', '=', 'pd.petition_id')->whereNull('pd.deleted_at')->where('pd.consumer_id', $user->user_id)->where('petitions.status_id', '!=', $rejectStatus)->count();

            if ($type === 'consumer_active_petition_count') {
                $data = [
                    'consumer_active_petition_count' => $consumerTotalPetitionsCount,
                ];
            } else {
                // Get Total Pending Petitions Irrespective of User
                $totalPendingCount = Petition::where('status_id', $pendingStatus)->count();

                // Get Total Signers from petition_details where deleted_at is NULL
                $totalSigners = PetitionDetail::whereNull('deleted_at')->count();

                // Get Total Stores that Accept CanPay and onboarded via CanPay Crew Program (petition_id is not null and status = active)
                $activeStatus            = getStatus(ACTIVE);
                $totalAcceptCanPayStores = MerchantStores::whereNotNull('petition_id')->where('status', $activeStatus)->count();

                // Get Lifetime Rewards obtained from CanPay Crew
                $lifetimeRewards = UserRewardUsageHistory::join('rewards', 'rewards.id', '=', 'user_reward_usage_history.reward_id')->whereNotNull('rewards.petition_id')->where(['entry_type' => CREDIT])->whereIn('user_reward_usage_history.points_type', [REWARD_POINTS, PETITION_POINTS])
                    ->where(
                        'rewards.status_id',
                        $activeStatus
                    )->sum('reward_point');

                $totalPetitionsThreshold    = (int) env('BRAND_NEW_MIN_TOTAL_STORE_PETITIONS', 0);
                $totalSignersThreshold      = (int) env('BRAND_NEW_MIN_TOTAL_SIGNERS', 0);
                $totalAcceptStoresThreshold = (int) env('BRAND_NEW_MIN_ACCEPT_CANPAY_STORES', 0);
                $lifetimeRewardsThreshold   = (int) env('BRAND_NEW_MIN_LIFETIME_REWARDS', 0);
                
                
                $data = [
                    'consumer_active_petition_count'           => $consumerTotalPetitionsCount,
                    'total_store_petition_count'               => number_format($totalPendingCount, 0),
                    'total_signers'                            => number_format($totalSigners, 0),
                    'total_accept_canpay_stores_via_crew'      => number_format($totalAcceptCanPayStores, 0),
                    'lifetime_rewards_from_crew'               => humanReadableNumber($lifetimeRewards),
                
                    // 'show_brand_new_*' fields - TRUE when the value is LESS than the threshold
                    'show_brand_new_total_store_petition_count'  => $totalPendingCount < $totalPetitionsThreshold,
                    'show_brand_new_total_signers'               => $totalSigners < $totalSignersThreshold,
                    'show_brand_new_total_accept_canpay_stores'  => $totalAcceptCanPayStores < $totalAcceptStoresThreshold,
                    'show_brand_new_lifetime_rewards_from_crew'  => $lifetimeRewards < $lifetimeRewardsThreshold,
                ];
                
            }

            Log::info(__METHOD__ . " - Petition statistics fetched.", $data);
            $message = trans('message.petition_statistics_fetched');
            return renderResponse(SUCCESS, $message, $data);
        } catch (\Exception $e) {
            Log::error(__METHOD__ . " - Error fetching petition statistics", ['user_id' => Auth::id(), 'error' => $e->getMessage()]);
            $message = trans('message.failed_to_fetch_petition_statistics');
            return renderResponse(FAIL, $message, $e->getMessage());
        }
    }

    /**
     * Update primary or secondary contact details for a petition.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function editContact(Request $request)
    {
        // Validate input
        $this->validate($request, [
            'petition_id' => VALIDATION_REQUIRED,
            'primary' => VALIDATION_REQUIRED,
            'firstname' => VALIDATION_REQUIRED,
            'lastname' => VALIDATION_REQUIRED,
            'email' => VALIDATION_REQUIRED . '|email',
        ]);

        Log::info(__METHOD__ . " - Petition Contact Edit initiated...", ['petition_id' => $request->petition_id]);

        try {
            // Fetch petition
            $petition = Petition::find($request->petition_id);

            if (!$petition) {
                Log::info(__METHOD__ . " - Petition not found", ['petition_id' => $request->petition_id]);
                $message = trans('message.petition_not_found');
                return renderResponse(FAIL, $message, NULL);
            }

            // Update contact details based on primary flag
            if ($request->primary == '1') {
                $petition->primary_contact_person_firstname = ucfirst($request->firstname);
                $petition->primary_contact_person_lastname  = ucfirst($request->lastname);
                $petition->primary_contact_person_email     = $request->email;
                $petition->primary_contact_person_title     = $request->title;
                $petition->primary_contact_last_updated_at  = Carbon::now();

            } else {
                $petition->secondary_contact_person_firstname = ucfirst($request->firstname);
                $petition->secondary_contact_person_lastname  = ucfirst($request->lastname);
                $petition->secondary_contact_person_email     = $request->email;
                $petition->secondary_contact_person_title     = $request->title;
                $petition->secondary_contact_last_updated_at  = Carbon::now();
            }

            $petition->save();
            $emailParams = [
                'name' => "{$request->firstname} {$request->lastname}",
                'email' => $request->email,
                'petition_url' => ENV('CONSUMER_APP_URL') . 'petition/'.base64_encode($petition->id),
            ];
            $code = createShortUrl($emailParams['petition_url']);
            if ($code) {
                $emailParams['petition_url'] = ENV('CONSUMER_APP_URL') . 's/'.$code;
            }
            $this->emailexecutor->sendPetetionCreatedNotificationToStore($emailParams);
            
            Log::info(__METHOD__ . " - Contact details updated successfully", ['petition_id' => $petition->petition_id]);
            $message = trans('message.petition_contact_details_updated');
            return renderResponse(SUCCESS, $message, NULL);
        } catch (\Exception $e) {
            Log::error(__METHOD__ . " - Error updating contact details", [
                'error' => $e->getMessage(),
                'petition_id' => $request->id
            ]);

            $message = trans('message.petition_contact_details_update_failed');
            return renderResponse(FAIL, $message, NULL);
        }
    }

    public function fetchContact(Request $request)
    {
        // Validate input
        $this->validate($request, [
            'petition_id' => VALIDATION_REQUIRED,
        ]);

        Log::info(__METHOD__ . " - Fetch Contact API initiated...", ['petition_id' => $request->petition_id]);

        // Fetch petition
        $petition = Petition::find($request->petition_id);

        Log::info(__METHOD__ . " - Contact details fetched for petition.", ['petition_id' => $petition->petition_id]);
        $message = trans('message.petition_contact_details_fetched');
        return renderResponse(SUCCESS, $message, $petition);
    }

    public function signedUsersForPetition(Request $request)
    {
        // Validate input
        $this->validate($request, [
            'petition_id' => VALIDATION_REQUIRED
        ]);
        Log::info(__METHOD__ . " - Fetching signed users for petition...", ['petition_id' => $request->petition_id]);
        $userId = $request->get('user_id');
        $petitionId = $request->petition_id;
        // default assumption that user has not signed.
        $userHasSigned = false;
        if(!is_null($userId)){
        // Check if user has signed the petition
            $userHasSigned = PetitionDetail::where('petition_id', $petitionId)
                ->where('consumer_id', $userId)
                ->whereNull('deleted_at')
                ->exists();
        }
 

        // Get all consumer_ids that $userId has tipped (only if user signed)
        $tippedReceiverIds = [];
        if ($userHasSigned) {
            $tippedReceiverIds = CrewTipHistory::where('petition_id', $petitionId)
                ->where('tip_from', $userId)
                ->where('is_deleted', 0)
                ->pluck('tip_to')
                ->toArray();
        }

        // Get tip counts per receiver (total tips received per signer)
        $tipCounts = CrewTipHistory::where('petition_id', $petitionId)
            ->where('is_deleted', 0)
            ->selectRaw('tip_to, COUNT(*) as total')
            ->groupBy('tip_to')
            ->pluck('total', 'tip_to')
            ->toArray();

        $petition = Petition::find($petitionId);

        $totalSigners = PetitionDetail::join('users as u', 'petition_details.consumer_id', '=', 'u.user_id')
            ->where('petition_details.petition_id', $petitionId)
            ->whereNull('petition_details.deleted_at')
            ->orderByRaw("
                CASE 
                    WHEN petition_details.type = 'mayor' THEN 0 
                    ELSE 1 
                END,
                petition_details.created_at DESC
            ")
            ->selectRaw("
                petition_details.consumer_id,
                petition_details.type,
                u.state,
                CONCAT(u.first_name, ' ', LEFT(u.last_name, 1), '.') AS name
            ")
            ->get()
            ->map(function ($row) use ($petition, $userId, $tippedReceiverIds, $userHasSigned, $tipCounts) {
                $consumerId = $row->consumer_id;

                if(is_null($userId) || !$userHasSigned){
                    return [
                            'name' => $row->name,
                            'state' => $row->state,
                            'type' => $row->type,
                            'consumer_id' => $consumerId,
                            'allow_tipping' => null,
                            'total_points' => null,
                            'is_loading' => false,
                        ];
                }

                // if the sote is onboarded then we will not allow tipping
                if($petition->onboarded_date){
                    return [
                            'name' => $row->name,
                            'state' => $row->state,
                            'type' => $row->type,
                            'consumer_id' => $consumerId,
                            'allow_tipping' => 0,
                            'total_points' => isset($tipCounts[$consumerId]) ? $tipCounts[$consumerId] * env('TIP_REWARD_POINT') : 0,
                             'is_loading' => false,
                        ];
                    }

                if($consumerId == $userId){
                    return [
                            'name' => $row->name,
                            'state' => $row->state,
                            'type' => $row->type,
                            'consumer_id' => $consumerId,
                            'allow_tipping' => 0,
                            'total_points' => isset($tipCounts[$consumerId]) ? $tipCounts[$consumerId] * env('TIP_REWARD_POINT') : 0,
                             'is_loading' => false,
                        ];
                }

                return [
                    'name' => $row->name,
                    'state' => $row->state,
                    'type' => $row->type,
                    'consumer_id' => $consumerId,
                    'allow_tipping' => !in_array($consumerId, $tippedReceiverIds)?1:0,
                    'total_points' => isset($tipCounts[$consumerId]) ? $tipCounts[$consumerId] * env('TIP_REWARD_POINT') : 0,
                     'is_loading' => false,
                ];
            })
            ->values()
            ->toArray();

        Log::info(__METHOD__ . " - Signed users fetched successfully", ['petition_id' => $request->petition_id]);
        $message = trans('message.petition_signed_users_fetched');
        return renderResponse(SUCCESS, $message, $totalSigners);
    }

    /**
     * Fetches petition details including the petition itself and its last 5 signers
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPetitionDetails(Request $request)
    {
        $this->validate($request, [
            'petition_id' => VALIDATION_REQUIRED,
        ]);

        $petitionId = $request->petition_id;
        Log::info(__METHOD__ . " - Start fetching petition details", ['petition_id' => $petitionId]);

        $petition = Petition::select('petitions.*', 'sm.code as status_code')->join('status_master as sm', 'petitions.status_id', '=', 'sm.id')->where('petitions.id', $petitionId)->first();

        if (!$petition) {
            Log::warning(__METHOD__ . " - Petition not found", ['petition_id' => $petitionId]);
            $message = trans('message.petition_not_found');
            return renderResponse(FAIL, $message, null);
        }

        $signers = PetitionDetail::join('users as u', 'petition_details.consumer_id', '=', 'u.user_id')
                ->where('petition_details.petition_id', $petitionId)
                ->whereNull('petition_details.deleted_at')
                ->orderByDesc('petition_details.created_at')
                ->selectRaw("
                CASE 
                    WHEN petition_details.type = '".MAYOR."' THEN CONCAT(u.first_name, ' ', u.last_name)
                    ELSE CONCAT(u.first_name, ' ', LEFT(u.last_name, 1), '.')
                END AS name,
                u.state,
                petition_details.type,
                petition_details.consumer_id
            ")
            ->get();

        // Split signers
        $mayor = $signers->firstWhere('type', MAYOR);
        $latestSigners = $signers->where('type', '!=', MAYOR)->take(5)->values();
        $totalSigneers = $signers->count();
        $petition["total_signer"] = $totalSigneers;
        $data = [
            'petition' => $petition,
            'petition_details' => $latestSigners,
            'mayor' => $mayor,
            'total_signers' => $totalSigneers,
        ];

        Log::info(__METHOD__ . " - Petition details fetched successfully", ['petition_id' => $petitionId]);

        $message = trans('message.petition_details_fetched');
        return renderResponse(SUCCESS, $message, $data);
    }

    /**
     * Withdraw a consumer from a petition.
     *
     * This method allows a consumer to withdraw from a petition they have signed.
     * It validates the input, checks the existence of the petition and the consumer's
     * record, and performs a soft delete on the consumer's petition detail record. 
     * Additionally, it updates the petition detail history and applies role upgrade logic 
     * to promote other consumers if necessary.
     *
     * @param \Illuminate\Http\Request $request The request object containing 'petition_id'.
     * @return \Illuminate\Http\Response A response indicating the success or failure of
     *                                   the withdrawal process.
     */

    public function withdrawFromPetition(Request $request)
    {
        $this->validate($request, [
            'petition_id' => VALIDATION_REQUIRED,
        ]);

        $petitionId = $request->petition_id;
        $consumerId = Auth::user()->user_id;

        Log::info(__METHOD__ . ' - Withdrawal process started', [
            'petition_id' => $petitionId,
            'consumer_id' => $consumerId
        ]);

        $petition = Petition::find($petitionId);

        if (!$petition) {
            Log::warning(__METHOD__ . ' - Petition not found', ['petition_id' => $petitionId]);
            return renderResponse(FAIL, trans('message.petition_not_found'), null);
        }
        // ❗ Check if already onboarded
        if (!is_null($petition->onboarded_date)) {
            Log::info(__METHOD__ . ' - Petition already onboarded. Withdrawal not allowed.', [
                'petition_id' => $petitionId,
                'consumer_id' => $consumerId,
                'onboarded_date' => $petition->onboarded_date,
            ]);

            return renderResponse(FAIL, trans('message.cannot_withdraw_onboarded_petition'), null);
        }

        $detail = PetitionDetail::where('petition_id', $petitionId)
            ->where('consumer_id', $consumerId)
            ->whereNull('deleted_at')
            ->first();
            // update the Crew tip History and put is deleted flag as 1.
            CrewTipHistory::where('petition_id', $petitionId)
                ->where('is_deleted', 0)
                ->where(function ($query) use ($consumerId) {
                    $query->where('tip_from', $consumerId)
                        ->orWhere('tip_to', $consumerId);
                })
                ->update(['is_deleted' => 1]);
            // 
            rewardConsumerForPetition($detail, 'withdrew_from_petition',true);
        if (!$detail) {
            Log::warning(__METHOD__ . ' - Petition detail not found for consumer', [
                'petition_id' => $petitionId,
                'consumer_id' => $consumerId
            ]);
            return renderResponse(FAIL, trans('message.petition_not_found'), null);
        }

        $currentType = $detail->type;
        $now = Carbon::now();

        // Soft delete the consumer's record
        $detail->deleted_at = $now;
        $detail->save();

        // Delete Reward the consumer
        rewardConsumerForPetition($detail, 'withdrew');

        // History entry
        $historyRecords = [[
            'id' => generateUUID(),
            'petition_id' => $petitionId,
            'consumer_id' => $consumerId,
            'type' => $currentType,
            'action' => 'withdraw',
            'created_at' => $now,
            'updated_at' => $now,
        ]];
    
        $emailsToSend = [];

        // Role upgrade logic
        if ($currentType === CREW_LEADER || $currentType === MAYOR) {
            // Step 1: Find next user for upgrade
            $nextCrew = PetitionDetail::where('petition_id', $petitionId)
                ->where('type', CREW_MEMBER)
                ->whereNull('deleted_at')
                ->orderBy('created_at')
                ->first();

            if ($nextCrew) {
                $nextCrew->type = CREW_LEADER;
                $nextCrew->changed_at = $now;
                $nextCrew->save();
                // Update Reward the consumer
                rewardConsumerForPetition($nextCrew, 'role-upgraded');

                $historyRecords[] = [
                    'id' => generateUUID(),
                    'petition_id' => $petitionId,
                    'consumer_id' => $nextCrew->consumer_id,
                    'type' => CREW_LEADER,
                    'action' => 'promoted from ' .CREW_MEMBER,
                    'created_at' => $now,
                    'updated_at' => $now,
                ];
                $emailsToSend[] = [
                    'user_id' => $nextCrew->consumer_id,
                    'store_name' => "{$petition->store_name} - {$petition->city}",
                    'role_name' => CREW_LEADER,
                ];
            }

            if ($currentType === MAYOR) {
                // Step 2: Promote a CREW_MEMBER to mayor
                $nextLeader = PetitionDetail::where('petition_id', $petitionId)
                    ->where('type', CREW_LEADER)
                    ->whereNull('deleted_at')
                    ->orderBy('created_at')
                    ->first();

                if ($nextLeader) {
                    $nextLeader->type = MAYOR;
                    $nextLeader->changed_at = $now;
                    $nextLeader->save();

                    // Update the petition's consumer_id to the new mayor
                    $petition->consumer_id = $nextLeader->consumer_id;
                    $petition->save();

                    // Update Reward the consumer
                    rewardConsumerForPetition($nextLeader, 'role-upgraded');

                    $historyRecords[] = [
                        'id' => generateUUID(),
                        'petition_id' => $petitionId,
                        'consumer_id' => $nextLeader->consumer_id,
                        'type' => MAYOR,
                        'action' => 'promoted from ' . CREW_LEADER,
                        'created_at' => $now,
                        'updated_at' => $now,
                    ];
    
                    $emailsToSend[] = [
                        'user_id' => $nextLeader->consumer_id,
                        'store_name' => "{$petition->store_name} - {$petition->city}",
                        'role_name' => MAYOR,
                    ];
                } else {
                    // No crew leader available to promote to mayor, set petition consumer_id to null
                    $petition->consumer_id = null;
                    $petition->save();
                }
            }
        }
        // Bulk insert history
        PetitionDetailHistory::insert($historyRecords);

        // Send emails after DB updates
        foreach ($emailsToSend as $emailParams) {
            $this->emailexecutor->sendPetetionRoleChangeEmail($emailParams);
        }
        Log::info(__METHOD__ . ' - Withdrawal process completed', [
            'petition_id' => $petitionId,
            'consumer_id' => $consumerId,
            'previous_type' => $currentType
        ]);

        return renderResponse(SUCCESS, trans('message.withdraw_successful'), null);
    }

    
    /**
     * Test API for match percentage calculation
     * Note: This API is only for testing
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function testMatchPercentage(Request $request) {
         // Calculate name and address match percentage
         $storeNameMatch = calculateMatchPercentage($request->get('petition_store_name'), $request->get('store_name'));
         $addressMatch = calculateMatchPercentage(
             $request->get('petition_street_address') . ' ' . $request->get('petition_city') . ' ' . $request->get('petition_state') . ' ' . $request->get('petition_zipcode'),
             $request->get('street_address') . ' ' . $request->get('city') . ' ' . $request->get('state') . ' ' . $request->get('zipcode')
         );
         // Combined match percentage (weighted more towards address)
         $overallMatchPercentage = ($addressMatch * 0.6) + ($storeNameMatch * 0.4);
         return renderResponse(SUCCESS, '', $overallMatchPercentage);
    }

    public function tipCrewMember(Request $request)
    {
        // Validate request input
        $this->validate($request, [
            'petition_id' => VALIDATION_REQUIRED,
            'tip_from'    => VALIDATION_REQUIRED,
            'type'        => VALIDATION_REQUIRED
        ]);

        $petitionId = $request->petition_id;
        $tipTo = $request->has('tip_to') ? $request->tip_to : null;
        $tipFrom = $request->tip_from;
        $type = $request->type;
        Log::info(__METHOD__ . " - Starting crew tip submission", [
            'petition_id' => $petitionId,
            'tip_from' => $tipFrom,
            'tip_to' => $tipTo,
            'type' => $type
        ]);

        try {
            // Ensure the petition exists
                $petition = Petition::find($petitionId);
                if (!$petition) {
                    Log::warning(__METHOD__ . " - Petition not found", ['petition_id' => $petitionId]);
                    return renderResponse(FAIL, trans('message.petition_not_found'), null);
                }
            // check both the user have signed the petition
                $tipperExists = true;
                $recipientExists = true;
                $tipperExists = PetitionDetail::where('petition_id', $petitionId)->where('consumer_id', $tipFrom)->exists();
                if($tipTo != null)    
                    $recipientExists = PetitionDetail::where('petition_id', $petitionId)->where('consumer_id', $tipTo)->exists();

                if (!$tipperExists || !$recipientExists) {
                    Log::warning(__METHOD__ . " - Invalid tipper or recipient", [
                        'tip_from_valid' => $tipperExists,
                        'tip_to_valid' => $recipientExists,
                    ]);
                    return renderResponse(FAIL, trans('message.invalid_tip_participants'), null);
                }
                if($type == SINGLE_TIP){
                    // check if user has previously tipped the person.
                        $isConsumerAlreadyTipped = CrewTipHistory::where('petition_id', $petitionId)->where('tip_from', $tipFrom)->where('tip_to', $tipTo)->where('is_deleted', 0)->exists();
                        if ($isConsumerAlreadyTipped) {
                            Log::warning(__METHOD__ . " - Consumer already tipped", [
                                'tip_from' => $tipFrom,
                                'tip_to' => $tipTo,
                            ]);
                            return renderResponse(FAIL, trans('message.consumer_already_tipped'), null);
                        }
                        if($tipFrom == $tipTo){
                            Log::warning(__METHOD__ . " - Consumer cannot tip themselves", [
                                'tip_from' => $tipFrom,
                                'tip_to' => $tipTo,
                            ]);
                            return renderResponse(FAIL, trans('message.consumer_cannot_tip_themselves'), null);
                        }
                    // reason for tip_to in below where is because the current logged in user is giving tip to other signers 
                    // for the same petition.
                        $detail = PetitionDetail::where('petition_id', $petitionId)->where('consumer_id', $tipTo)->first();
                        $reward_data = rewardConsumerForPetition($detail, 'consumer_gave_tip',true);
                    // Create new crew tip record
                        $crewTip = new CrewTipHistory([
                            'petition_id' => $petitionId,
                            'tip_from' => $tipFrom,
                            'tip_to' => $tipTo,
                            'reward_id' => $reward_data->id,
                            'tip_reward_amount' => ENV('TIP_REWARD_POINT') * env('EXCHANGE_RATE'),
                            'tip_reward_point' => ENV('TIP_REWARD_POINT')
                        ]);
                        $crewTip->save();
                        Log::info(__METHOD__ . " - Crew tip submitted successfully", ['tip_id' => $crewTip->id]);
                        return renderResponse(SUCCESS, trans('message.crew_tip_success'), $crewTip);
                }else if( $type == TIP_ALL){
                    $signers = PetitionDetail::from('petition_details as pd')
                        ->leftJoin('crew_tip_history as cth', function ($join) use ($petitionId, $tipFrom) {
                            $join->on('pd.consumer_id', '=', 'cth.tip_to')
                                ->where('cth.petition_id', '=', $petitionId)
                                ->where('cth.tip_from', '=', $tipFrom)
                                ->where('cth.is_deleted', '=', 0);
                        })
                        ->where('pd.petition_id', $petitionId)
                        ->whereNull('pd.deleted_at')
                        ->whereNull('cth.id') // Only include those NOT tipped yet
                        ->where('pd.consumer_id', '!=', $tipFrom) // Prevent self-tip
                        ->get(['pd.*']);
                    foreach($signers as $signer){
                        $tipTo = $signer->consumer_id;
                        $reward_data = rewardConsumerForPetition($signer, 'consumer_gave_tip',true);
                        // Create new crew tip record
                        $crewTip = new CrewTipHistory([
                            'petition_id' => $petitionId,
                            'tip_from' => $tipFrom,
                            'tip_to' => $tipTo,
                            'reward_id' => $reward_data->id,
                            'tip_reward_amount' => ENV('TIP_REWARD_POINT') * env('EXCHANGE_RATE'),
                            'tip_reward_point' => ENV('TIP_REWARD_POINT')
                        ]);
                        $crewTip->save();
                    }
                    return renderResponse(SUCCESS, trans('message.crew_tip_success'), null);
            }
 
        } catch (\Exception $e) {
            \Log::error(__METHOD__ . " - Error submitting crew tip", ['error' => $e->getMessage()]);
            return renderResponse(FAIL, trans('message.crew_tip_failed'), null);
        }
    }


}
