<?php
namespace App\Console\Commands;

use App\Models\RewardWheelSegmentMaster;
use App\Models\UserRole;
use Illuminate\Console\Command;

class TestDbConnections extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:connections';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will test the all the Database connections';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->info("Connection checking for " . MYSQL . " started....");

        UserRole::on(MYSQL)->count();

        $this->info("Connection successful for " . MYSQL);

        $this->info("Connection checking for " . MYSQL_RO . " started....");

        UserRole::on(MYSQL_RO)->count();

        $this->info("Connection successful for " . MYSQL_RO);

        $this->info("Connection checking for " . MYSQL_REWARD_WHEEL . " started....");

        RewardWheelSegmentMaster::on(MYSQL_REWARD_WHEEL)->count();

        $this->info("Connection successful for " . MYSQL_REWARD_WHEEL);

    }
}
