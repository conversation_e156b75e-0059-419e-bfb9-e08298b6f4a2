<?php

namespace App\Console\Commands;

use App\Models\TransactionReleaseDate;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class UpdateTransactionReleaseDates extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:transactionreleasedates {--date=}';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command updates the transaction release dates in Transaction release dates table.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Log::info('Release date update started...');
        $this->info('Release date update started...');

        try {
            $pending = getStatus(PENDING);
            $transactions = TransactionReleaseDate::join('transaction_details', 'transaction_release_dates.transaction_id', '=', 'transaction_details.id')
                ->join('user_bank_account_info', 'user_bank_account_info.id', '=', 'transaction_details.account_id')
                ->select('transaction_release_dates.*', 'transaction_details.scheduled_posting_date', 'transaction_details.consumer_id', 'transaction_details.account_id', 'transaction_details.amount', 'transaction_details.tip_amount')
                ->selectRaw('if(user_bank_account_info.account_id IS NOT NULL,1,0) AS user_bank_link_type')
                ->where('transaction_release_dates.release_date', '>=', $this->option('date'))
                ->where('transaction_details.status_id', $pending)
                ->whereNull('transaction_details.transaction_ref_no')
                ->get();

            Log::info("Found " . $transactions->count() . " transactions to process.");
            $this->info("Found " . $transactions->count() . " transactions to process.");

            foreach ($transactions as $val_transaction) {
                if ($val_transaction->user_bank_link_type == 1) {
                    $transaction_amount = $val_transaction->amount + $val_transaction->tip_amount;
                    [$releaseDate, $isCheckOnLogin] = getDirectTransactionConditions($val_transaction->scheduled_posting_date, $val_transaction->consumer_id, $val_transaction->account_id, $transaction_amount, $val_transaction->transaction_id);
                    if (!$releaseDate) {
                        $releaseDate = getTransactionReleaseDate($val_transaction->scheduled_posting_date, $val_transaction->transaction_id);
                    }
                } else {
                    $releaseDate = getTransactionReleaseDate($val_transaction->scheduled_posting_date, $val_transaction->transaction_id);
                }

                TransactionReleaseDate::where('transaction_id', $val_transaction->transaction_id)->update(['release_date' => $releaseDate, 'bank_link_type' => $val_transaction->user_bank_link_type]);

                Log::info("Updated transaction ID " . $val_transaction->transaction_id . " with new release date " . $releaseDate . " and Bank link type " . $val_transaction->user_bank_link_type);
                $this->info("Updated transaction ID " . $val_transaction->transaction_id . " with new release date " . $releaseDate . " and Bank link type " . $val_transaction->user_bank_link_type);
            }

            Log::info('Release date update completed successfully.');
            $this->info('Release date update completed successfully.');
        } catch (\Exception $e) {
            Log::error('Error updating transaction release dates: ' . $e->getMessage());
            $this->error('Error updating transaction release dates: ' . $e->getMessage());
        }
    }
}
