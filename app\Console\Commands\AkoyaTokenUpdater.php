<?php

namespace App\Console\Commands;

use App\Http\Factories\Akoya\AkoyaFactory;
use App\Models\AkoyaRefreshToken;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class AkoyaTokenUpdater extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:akoyatokens';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will check if the consumer token has been expired or not. If expired then renew the token based on refresh token.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->akoyaFactory = new AkoyaFactory();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Process started for updating tokens of Akoya consumers...");
        $this->info("Process started for updating tokens of Akoya consumers...");

        $currentTime = Carbon::now();

        // Fetch those records those are going to expire in the next hour or has already been expired
        $expiredTokens = AkoyaRefreshToken::where(function ($query) use ($currentTime) {
            $query->where('token_expiration_time', '<=', $currentTime)
                ->orWhere('token_expiration_time', '<=', $currentTime->copy()->addHour())
                ->orWhereNull('token_expiration_time');
        })->where('error', 0)->get();
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Total tokens need to be updated are: " . count($expiredTokens));
        $this->info("Total tokens need to be updated are: " . count($expiredTokens));
        if (count($expiredTokens) > 0) {
            $success_updated_count = $failed_update_count = 0;
            foreach ($expiredTokens as $token) {
                try {
                    $force_refresh['token'] = $token;
                    $this->akoyaFactory->refreshToken($token->consumer_id, $force_refresh, $token->institution_id); // Refresh the token
                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Token updated for Consumer: " . $token->consumer_id);
                    $this->info("Token updated for Consumer: " . $token->consumer_id);
                    $success_updated_count++;
                } catch (\Exception $e) {
                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Token update failed for Consumer: " . $token->consumer_id, [EXCEPTION => $e]);
                    $this->info("Token update failed for Consumer: " . $token->consumer_id);
                    $failed_update_count++;
                }
            }

            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Total tokens updated are: " . $success_updated_count);
            $this->info("Total tokens updated are: " . $success_updated_count);

            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Total tokens update failed are: " . $failed_update_count);
            $this->info("Total tokens update failed are: " . $failed_update_count);
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Token updation process completed successfully.");
            $this->info("Token updation process completed successfully.");
        } else {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No tokens found to update.");
            $this->info("No tokens found to update.");
        }
    }

}
