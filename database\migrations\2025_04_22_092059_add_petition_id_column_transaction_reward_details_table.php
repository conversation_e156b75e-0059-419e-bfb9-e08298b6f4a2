<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("ALTER TABLE `transaction_reward_details` ADD COLUMN IF NOT EXISTS `petition_id` VARCHAR(40) COLLATE 'utf8mb3_general_ci' AFTER `campaign_id`;");
    }

};
