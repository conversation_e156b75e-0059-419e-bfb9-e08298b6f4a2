<?php
namespace App\Console\Commands;

use App\Models\TransactionDetails;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;

class UpdateFinalTransactionStatusSet extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:transactionset {--transaction_ids=}';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command updates the final status for selected transactions that has been successfully settled.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->_updateTransactionStatus();
    }
    /**
     * This function updates the final status for all the transactions that has been successfully settled
     */
    private function _updateTransactionStatus()
    {
        Log::channel('update-final-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Fetching all the transactions selected transactions...");
        $this->info("Fetching transactions...");
        $id_array = explode(",", $this->option('transaction_ids'));
        $transactions = TransactionDetails::whereIn('id', $id_array)->get();
        $success = getStatus(SUCCESS);
        if (sizeof($transactions) != 0) {
            foreach ($transactions as $transaction) {
                // update the parent transaction
                TransactionDetails::where('id', $transaction->id)->update(array('status_id' => $success));
                // created new success row
                $this->_createTransaction($transaction, $success);
            }
            Log::channel('update-final-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transactions updated successfully.");
            $this->info("Transactions updated successfully.");
        } else {
            Log::channel('update-final-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No transactions found to update.");
            $this->info("No transactions found to update.");
        }

    }
    private function _createTransaction($transaction, $success)
    {
        //create a new transaction
        $transaction_details = new TransactionDetails();
        $transaction_details->transaction_number = $transaction->transaction_number;
        $transaction_details->transaction_ref_no = $transaction->id;
        $transaction_details->user_id = $transaction->user_id;
        $transaction_details->consumer_id = $transaction->consumer_id;
        $transaction_details->terminal_id = $transaction->terminal_id;
        $transaction_details->transaction_time = Carbon::now();
        $transaction_details->local_transaction_time = Carbon::now($transaction->timezone_name);
        $transaction_details->local_transaction_year = date("Y", strtotime($transaction_details->local_transaction_time));
        $transaction_details->local_transaction_month = date("m", strtotime($transaction_details->local_transaction_time));
        $transaction_details->local_transaction_date = date("Y-m-d", strtotime($transaction_details->local_transaction_time));
        $transaction_details->timezone_id = $transaction->timezone_id;
        $transaction_details->consumer_bank_posting_amount = $transaction->consumer_bank_posting_amount;
        $transaction_details->reward_amount_used = $transaction->reward_amount_used;
        $transaction_details->reward_point_used = $transaction->reward_point_used;
        $transaction_details->return_representment_reward_deduction_amount = $transaction->return_representment_reward_deduction_amount;
        $transaction_details->amount = $transaction->amount;
        $transaction_details->tip_amount = $transaction->tip_amount;
        $transaction_details->tip_type = $transaction->tip_type;
        $transaction_details->tip_add_time = $transaction->tip_add_time;
        $transaction_details->used_qr_id = $transaction->used_qr_id;
        $transaction_details->status_id = $success;
        $transaction_details->transaction_type_id = $transaction->transaction_type_id;
        $transaction_details->save();
        Log::channel('update-final-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction details was stored successfully for parent transaction: " . $transaction->id);
    }

}
