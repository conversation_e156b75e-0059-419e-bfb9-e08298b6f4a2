<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class InsertConsumersIntoUsersCopy extends Command
{
    protected $signature = 'insert:consumers_into_users_copy';
    protected $description = 'Insert consumers with non-null passwords and specified role_id into users_copy table with their risk scores';

    public function handle()
    {
        $role_id = getRole(CONSUMER); // Adjust this as needed

        // Process consumers in batches of 1000 (adjust batch size if necessary)
        DB::table('users')
            ->whereNotNull('password')
            ->where('role_id', $role_id)
            ->orderBy('created_at') // Sort by created_at
            ->chunk(1000, function ($consumers) {
                Log::info('Processing batch with ' . count($consumers) . ' consumers');
                $this->info('Processing batch with ' . count($consumers) . ' consumers');

                foreach ($consumers as $consumer) {
                    $risk_score = $this->getRiskScore($consumer->phone);
                    $this->info('Risk Score for ' . $consumer->phone . ' is: ' . $risk_score);
                    Log::info('Risk Score for ' . $consumer->phone . ' is: ' . $risk_score);

                    // Insert into users_copy
                    DB::table('users_copy')->insert([
                        'user_id' => $consumer->user_id,
                        'username' => $consumer->username,
                        'user_identifier' => $consumer->user_identifier,
                        'ach_identifier' => $consumer->ach_identifier,
                        'email' => $consumer->email,
                        'phone' => $consumer->phone,
                        'first_name' => $consumer->first_name,
                        'middle_name' => $consumer->middle_name,
                        'last_name' => $consumer->last_name,
                        'suffix' => $consumer->suffix,
                        'date_of_birth' => $consumer->date_of_birth,
                        'street_address' => $consumer->street_address,
                        'apt_number' => $consumer->apt_number,
                        'city' => $consumer->city,
                        'state' => $consumer->state,
                        'zipcode' => $consumer->zipcode,
                        'status' => $consumer->status,
                        'password' => $consumer->password,
                        'login_pin' => $consumer->login_pin,
                        'temp_password' => $consumer->temp_password,
                        'temp_password_expiry_time' => $consumer->temp_password_expiry_time,
                        'pin' => $consumer->pin,
                        'purchase_power' => $consumer->purchase_power,
                        'risk_score' => $risk_score,
                        'new_risk_score' => null,
                        'do_not_update' => 0,
                        'role_id' => $consumer->role_id,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            });

        $this->info('All consumers processed and inserted into users_copy table successfully.');
        Log::info('All consumers processed and inserted into users_copy table successfully.');
    }

    /**
     * Get the risk score for a consumer by phone.
     */
    private function getRiskScore($phone)
    {
        $sql = "WITH combined_results AS (
                    SELECT
                        CASE
                        WHEN ROUND(cppdt.risk_score, 2) = ROUND(cppdt.risk_score)
                        THEN CAST(ROUND(cppdt.risk_score) AS VARCHAR(255))
                        ELSE CAST(cppdt.risk_score AS VARCHAR(255)) END AS risk_score,
                        cppdt.created_at
                    FROM registration_session_details rsd
                    JOIN consumer_purchase_power_decision_table cppdt ON rsd.id = cppdt.consumer_id
                    WHERE rsd.phone = ? AND cppdt.risk_score IS NOT NULL
                    UNION ALL
                    SELECT
                        CASE
                        WHEN ROUND(cppdt.risk_score, 2) = ROUND(cppdt.risk_score)
                        THEN CAST(ROUND(cppdt.risk_score) AS VARCHAR(255))
                        ELSE CAST(cppdt.risk_score AS VARCHAR(255)) END AS risk_score,
                        cppdt.created_at
                    FROM users u
                    JOIN consumer_purchase_power_decision_table cppdt ON u.user_id = cppdt.consumer_id
                    WHERE u.phone = ? AND cppdt.risk_score IS NOT NULL
                )
                SELECT risk_score
                FROM combined_results
                ORDER BY created_at DESC
                LIMIT 1";

        $result = DB::select($sql, [$phone, $phone]);
        return $result[0]->risk_score ?? null;
    }
}
