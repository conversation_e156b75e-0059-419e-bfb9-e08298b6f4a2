<?php

use App\Models\EmailTemplate;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        EmailTemplate::firstOrCreate([
            'template_name'    => 'Petition Rejected - Notification',
            'template_subject' => 'Your Petition {{$petition_title}} Has Been Rejected',
            'template_body'    => '
                Hi {{$first_name}},<br><br>

                We wanted to let you know that your petition titled <strong>{{$petition_title}}</strong> has been <strong>rejected</strong> after review.<br><br>

                <strong>Reason for Rejection:</strong><br>
                {{$rejection_reason}}<br><br>

                We truly appreciate your effort in initiating this petition. While this one did not meet the required criteria, you\'re welcome to revise and resubmit if applicable.<br><br>

                If you have any questions or need clarification, feel free to contact our support team or your CanPay representative.<br><br>

                Thank you for being an active member of the CanPay Crew.<br><br>

                Best regards,<br>
                <strong>The CanPay Team</strong>
            ',
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
