<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        DB::statement("CREATE TABLE IF NOT EXISTS `crew_tip_history` (
            `id` varchar(40) NOT NULL,
            `petition_id` varchar(40) NOT NULL,
            `reward_id` varchar(40) NOT NULL,
            `tip_from` varchar(40) NOT NULL,
            `tip_to` varchar(40) NOT NULL,
            `tip_reward_amount` DECIMAL(10, 4) NOT NULL,
            `tip_reward_point` varchar(40) NOT NULL,
            `is_deleted` TINYINT(1) DEFAULT 0,
            `created_at` datetime NOT NULL,
            `updated_at` datetime NOT NULL,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8;");
    }
    
};
