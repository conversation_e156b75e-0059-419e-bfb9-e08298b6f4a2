<?php
namespace App\Console\Commands;

use App\Models\ConsumerSessionManagement;
use App\Models\PaymentPinHistory;
use App\Models\PaymentPinMaster;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class RemoveExpiredPaymentPins extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'remove:expiredpaymentpins';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will remove all the expired payment pins and move them to history table';
    private $chunkSize = 500;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

        Log::channel('move-expired-payment-pins')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Removing of Expired payment pins cron started.");
        // Fetch all the expired payment pins
        $this->info("Fetching all the Expired Payment Pins...");
        $expiredPins = ConsumerSessionManagement::whereRaw('token_expiry_time < now()')->get();
        if (!empty($expiredPins)) {
            $current_time = Carbon::now();
            $consumer_session_management_array = [];
            $pin_array = [];
            $id_array = [];
            $this->info("Preparing array for removing the payment pins in batch mode...");
            foreach ($expiredPins as $pin) {
                $queue = [];
                $queue['id'] = generateUUID();
                $queue['consumer_session_management_id'] = $pin->id;
                $queue['user_id'] = $pin->user_id;
                $queue['qr_url'] = $pin->qr_url;
                $queue['payment_pin'] = $pin->payment_pin;
                $queue['login_time'] = $pin->login_time;
                $queue['token_creation_time'] = $pin->token_creation_time;
                $queue['logout_time'] = $pin->logout_time;
                $queue['token_expiry_time'] = $pin->token_expiry_time;
                $queue['session_type'] = $pin->session_type;
                $queue['reward_point'] = $pin->reward_point;
                $queue['reward_amount'] = $pin->reward_amount;
                $queue['pay_with_reward_points'] = $pin->pay_with_reward_points;
                $queue['processing'] = $pin->processing;
                $queue['used'] = $pin->used;
                $queue['consumer_session_management_created_at'] = $pin->created_at;
                $queue['consumer_session_management_updated_at'] = $pin->updated_at;
                $queue['created_at'] = $current_time;
                $queue['updated_at'] = $current_time;
                $consumer_session_management_array[] = $queue;
                $pin_array[] = $pin->payment_pin;
                $id_array[] = $pin->id;
                if (count($consumer_session_management_array) == $this->chunkSize) {
                    PaymentPinHistory::insert($consumer_session_management_array); // Insert in Payment Pin History table
                    // Delete from consumer session management and payment pin master table
                    PaymentPinMaster::whereIn('payment_pin', $pin_array)->delete();
                    ConsumerSessionManagement::whereIn('id', $id_array)->delete();
                    $this->info("Inserted " . count($consumer_session_management_array) . " Payment Pins");
                    Log::channel('move-expired-payment-pins')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . count($consumer_session_management_array) . " expired Payment Pins moved to history table and got deleted from payment pin master and consumer session management table.");
                    $consumer_session_management_array = [];
                    $pin_array = [];
                    $id_array = [];
                }
            }
            if (!empty($consumer_session_management_array)) { // Processing the remaining payment pins.
                PaymentPinHistory::insert($consumer_session_management_array); // Insert in Payment Pin History table
                // Delete from consumer session management and payment pin master table
                PaymentPinMaster::whereIn('payment_pin', $pin_array)->delete();
                ConsumerSessionManagement::whereIn('id', $id_array)->delete();
                $this->info("Inserted Left Over" . count($consumer_session_management_array) . " Payment Pins");
                Log::channel('move-expired-payment-pins')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Loaded left over " . count($consumer_session_management_array) . " expired Payment Pins moved to history table and got deleted from payment pin master and consumer session management table.");
            }
            Log::channel('move-expired-payment-pins')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "All expired Payment Pins moved to history table and got deleted from payment pin master and consumer session management table.");
            $this->info("Expired Payment Pin removal process finished...");
        } else {
            Log::channel('move-expired-payment-pins')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No expired Payment Pin found.");
            $this->info("No Expired Payment Pin found...");
        }
    }
}
