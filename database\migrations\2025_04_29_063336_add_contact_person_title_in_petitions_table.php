<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::Statement("ALTER TABLE `petitions` CHANGE COLUMN IF EXISTS `petition_title` `primary_contact_person_title` VARCHAR(255) NOT NULL AFTER `primary_contact_person_email`,
        ADD COLUMN IF NOT EXISTS `secondary_contact_person_title` VARCHAR(255) NULL DEFAULT NULL AFTER `secondary_contact_person_email`");
    }

};
