<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::Statement("ALTER TABLE `petitions` ADD COLUMN IF NOT EXISTS `logo_url` VARCHAR(255) NULL DEFAULT NULL AFTER `zipcode`");
        DB::Statement("ALTER TABLE `petitions` ADD COLUMN IF NOT EXISTS `store_short_name` VARCHAR(255) NULL DEFAULT NULL AFTER `store_name`");
        DB::Statement("ALTER TABLE `petitions` ADD COLUMN IF NOT EXISTS `lat` VARCHAR(50) NULL DEFAULT NULL AFTER `zipcode`");
        DB::Statement("ALTER TABLE `petitions` ADD COLUMN IF NOT EXISTS `long` VARCHAR(50) NULL DEFAULT NULL AFTER `lat`");
    }
};
