{"@timestamp":"2025-08-04T09:05:40.022802+00:00","V":"V-a51d9a6","EC2":"Local-Server","IP":"::1","TID":"94455f2c0f150efa98cfae05b91f6716","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"ERROR","MESSAGE":"cURL error 60: SSL certificate problem: unable to get local issuer certificate (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://maps.googleapis.com/maps/api/place/findplacefromtext/json?fields=place_id&input=The%20Local%20Charm%20CanPay%203456%20East%20Circle%20Drive%20NE,%20Suite%20103&inputtype=textquery&key=AIzaSyB2rEslfuVTQ20-ZF3VH7ZMhqp6OlAGPHk {"exception":"[object] (GuzzleHttp\\Exception\\RequestException(code: 0): cURL error 60: SSL certificate problem: unable to get local issuer certificate (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://maps.googleapis.com/maps/api/place/findplacefromtext/json?fields=place_id&input=The%20Local%20Charm%20CanPay%203456%20East%20Circle%20Drive%20NE,%20Suite%20103&inputtype=textquery&key=AIzaSyB2rEslfuVTQ20-ZF3VH7ZMhqp6OlAGPHk at C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlFactory.php:276)"}"}
{"@timestamp":"2025-08-04T09:36:10.144492+00:00","V":"V-4107b98","EC2":"Local-Server","IP":null,"TID":"33c26b61ee8f6c16","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"Status updated successfully for code 217: Provisioned -> Paused - Store In Discussion with CanPay []"}
{"@timestamp":"2025-08-04T09:36:10.168718+00:00","V":"V-4107b98","EC2":"Local-Server","IP":null,"TID":"33c26b61ee8f6c16","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"ERROR","MESSAGE":"SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry 'Rejected' for key 'status_master.status' (Connection: mysql, SQL: insert into `status_master` (`id`, `status`, `code`, `updated_at`, `created_at`) values (9d962cae6af09b3944b7bc28d3d78a37, Rejected, 724, 2025-08-04 09:36:10, 2025-08-04 09:36:10)) {"exception":"[object] (Illuminate\\Database\\UniqueConstraintViolationException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry 'Rejected' for key 'status_master.status' (Connection: mysql, SQL: insert into `status_master` (`id`, `status`, `code`, `updated_at`, `created_at`) values (9d962cae6af09b3944b7bc28d3d78a37, Rejected, 724, 2025-08-04 09:36:10, 2025-08-04 09:36:10)) at C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\illuminate\\database\\Connection.php:820)\n[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry 'Rejected' for key 'status_master.status' at C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\illuminate\\database\\MySqlConnection.php:53)"}"}
{"@timestamp":"2025-08-04T11:10:34.094190+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"2cb3b17bf903ae2048ae433bc9797a94","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\MerchantController::forgotPassword(Line: 1278) - Temporary password set porcess started. []"}
{"@timestamp":"2025-08-04T11:10:34.308605+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"2cb3b17bf903ae2048ae433bc9797a94","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\MerchantController::forgotPassword(Line: 1290) - No user found for Email Id: <EMAIL> []"}
{"@timestamp":"2025-08-04T11:12:15.817322+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"5f8c01094597f662f85069726919d1c0","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\MerchantController::login(Line: 227) - Corporate Parent - User with ID : aa5c6efd7c2a216f600daaee1284d641 logged in successfully []"}
{"@timestamp":"2025-08-04T11:12:16.538393+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"82d377aadfeb2a18e73832e1bc657ef5","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\\Http\\Controllers\\MerchantController::getAuthorizedTerminals(Line997) : Terminal List fetched successfully []"}
{"@timestamp":"2025-08-04T11:12:17.675440+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"0c484dac7de95c33ac467ccc3a4db8e6","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\MerchantController::getAuthorizedStores(Line: 580) - All Authorized Stores Fetched Successfully by User ID : aa5c6efd7c2a216f600daaee1284d641 []"}
{"@timestamp":"2025-08-04T11:12:26.554750+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"6573e0deb6ad9f2460d2e5060463676a","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\MerchantController::getStoreTransactionTypes(Line: 1266) - Transaction types for Store Id: 1b3c259c6680438555a4d13c042e8b42 fetched successfully by User Id: .aa5c6efd7c2a216f600daaee1284d641 []"}
{"@timestamp":"2025-08-04T11:12:42.598489+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"d7f957b9e075b5746463809ad553161c","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\\Http\\Controllers\\MerchantController::getAuthorizedTerminals(Line997) : Terminal List fetched successfully []"}
{"@timestamp":"2025-08-04T11:12:43.980327+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"4b3ea48e0f80da42110ecdb0395280e3","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\MerchantController::getAuthorizedStores(Line: 580) - All Authorized Stores Fetched Successfully by User ID : aa5c6efd7c2a216f600daaee1284d641 []"}
{"@timestamp":"2025-08-04T11:12:50.425253+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"be4f7198903d22f54d2b68d6a492a004","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\MerchantController::getStoreTransactionTypes(Line: 1266) - Transaction types for Store Id: 1b3c259c6680438555a4d13c042e8b42 fetched successfully by User Id: .aa5c6efd7c2a216f600daaee1284d641 []"}
{"@timestamp":"2025-08-04T11:13:19.757915+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"3239ac514271585689f7ccd8175315a4","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\MerchantController::logout(Line: 269) - Logging out from merchant POS for user with id: aa5c6efd7c2a216f600daaee1284d641 []"}
{"@timestamp":"2025-08-04T11:13:32.038053+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"5610187815264542b584236f7b450390","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\MerchantController::login(Line: 227) - Corporate Parent - User with ID : aa5c6efd7c2a216f600daaee1284d641 logged in successfully []"}
{"@timestamp":"2025-08-04T11:13:32.603176+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"ee2e9b526c085775630ada5d2b5deeb1","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\\Http\\Controllers\\MerchantController::getAuthorizedTerminals(Line997) : Terminal List fetched successfully []"}
{"@timestamp":"2025-08-04T11:13:35.913366+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"4b42dedbbb26743b6ae866a4d9bc243b","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\MerchantController::getAuthorizedStores(Line: 580) - All Authorized Stores Fetched Successfully by User ID : aa5c6efd7c2a216f600daaee1284d641 []"}
{"@timestamp":"2025-08-04T11:13:43.630178+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"21ce57cfabf49adf11c18f2822358fcc","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\MerchantController::getStoreTransactionTypes(Line: 1266) - Transaction types for Store Id: c351fd48f27e45050ff812855c30ca46 fetched successfully by User Id: .aa5c6efd7c2a216f600daaee1284d641 []"}
{"@timestamp":"2025-08-04T11:13:51.945160+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"af2c3063fcc3c410d181e2817f6e819a","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\\Http\\Controllers\\MerchantController::getAuthorizedTerminals(Line997) : Terminal List fetched successfully []"}
{"@timestamp":"2025-08-04T11:13:53.835231+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"b1ea6752f961bb87e2cafe39ed3e90d6","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\MerchantController::getAuthorizedStores(Line: 580) - All Authorized Stores Fetched Successfully by User ID : aa5c6efd7c2a216f600daaee1284d641 []"}
{"@timestamp":"2025-08-04T11:14:02.194639+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"5e53e2b093945d13923fa6653b61bab5","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\MerchantController::getStoreTransactionTypes(Line: 1266) - Transaction types for Store Id: 1b3c259c6680438555a4d13c042e8b42 fetched successfully by User Id: .aa5c6efd7c2a216f600daaee1284d641 []"}
{"@timestamp":"2025-08-04T11:14:11.794765+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"63ec5d75b52d7ba9ddaa56a803311b21","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\MerchantController::getStoreTransactionTypes(Line: 1266) - Transaction types for Store Id: c351fd48f27e45050ff812855c30ca46 fetched successfully by User Id: .aa5c6efd7c2a216f600daaee1284d641 []"}
{"@timestamp":"2025-08-04T11:21:33.325901+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"ada324ac917daedcc044513ba868f364","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\\Http\\Controllers\\MerchantController::getAuthorizedTerminals(Line997) : Terminal List fetched successfully []"}
{"@timestamp":"2025-08-04T11:21:34.533962+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"4924da98e52ef692c26c25457be474af","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\MerchantController::getAuthorizedStores(Line: 580) - All Authorized Stores Fetched Successfully by User ID : aa5c6efd7c2a216f600daaee1284d641 []"}
{"@timestamp":"2025-08-04T11:21:39.835984+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"795dcb7f4416b25e670d64f354f28b20","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\MerchantController::getStoreTransactionTypes(Line: 1266) - Transaction types for Store Id: c351fd48f27e45050ff812855c30ca46 fetched successfully by User Id: .aa5c6efd7c2a216f600daaee1284d641 []"}
{"@timestamp":"2025-08-04T11:21:54.109781+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"5daf0ce0bcfccf78e31c30a2966de35b","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\MerchantController::getStoreTransactionTypes(Line: 1266) - Transaction types for Store Id: 1b3c259c6680438555a4d13c042e8b42 fetched successfully by User Id: .aa5c6efd7c2a216f600daaee1284d641 []"}
{"@timestamp":"2025-08-04T11:22:00.578486+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"364e4b5bf10c5982ea77b9c11990987e","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\MerchantController::getStoreTransactionTypes(Line: 1266) - Transaction types for Store Id: c351fd48f27e45050ff812855c30ca46 fetched successfully by User Id: .aa5c6efd7c2a216f600daaee1284d641 []"}
{"@timestamp":"2025-08-04T11:23:28.381134+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"afb6ca949263460cd51cde44d2fd52ed","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\MerchantController::getStoreTransactionTypes(Line: 1266) - Transaction types for Store Id: c351fd48f27e45050ff812855c30ca46 fetched successfully by User Id: .aa5c6efd7c2a216f600daaee1284d641 []"}
{"@timestamp":"2025-08-04T11:23:29.588813+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"755d2172e59dd56ff71fbcc18327f719","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\MerchantController::getStoreTransactionTypes(Line: 1266) - Transaction types for Store Id: 1b3c259c6680438555a4d13c042e8b42 fetched successfully by User Id: .aa5c6efd7c2a216f600daaee1284d641 []"}
{"@timestamp":"2025-08-04T11:23:31.022125+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"7f6a481d3cfea39058495e8006d7e2b9","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\MerchantController::getStoreTransactionTypes(Line: 1266) - Transaction types for Store Id: c351fd48f27e45050ff812855c30ca46 fetched successfully by User Id: .aa5c6efd7c2a216f600daaee1284d641 []"}
{"@timestamp":"2025-08-04T11:24:03.902833+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"a5ae025535c8e61eda7604a1d5ddc03b","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\MerchantController::getStoreTransactionTypes(Line: 1266) - Transaction types for Store Id: 1b3c259c6680438555a4d13c042e8b42 fetched successfully by User Id: .aa5c6efd7c2a216f600daaee1284d641 []"}
{"@timestamp":"2025-08-04T11:24:17.874323+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"59da16aec66db04e0b708ae9de8534e0","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\MerchantController::getStoreTransactionTypes(Line: 1266) - Transaction types for Store Id: c351fd48f27e45050ff812855c30ca46 fetched successfully by User Id: .aa5c6efd7c2a216f600daaee1284d641 []"}
{"@timestamp":"2025-08-04T11:24:19.817497+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"8aa91da7d868ead1dbf8c65690c02aae","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\MerchantController::getStoreTransactionTypes(Line: 1266) - Transaction types for Store Id: 1b3c259c6680438555a4d13c042e8b42 fetched successfully by User Id: .aa5c6efd7c2a216f600daaee1284d641 []"}
{"@timestamp":"2025-08-04T11:24:26.550851+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"08c2d4cd15d9f207739aebaf762abb36","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\\Http\\Controllers\\MerchantController::getAuthorizedTerminals(Line997) : Terminal List fetched successfully []"}
{"@timestamp":"2025-08-04T11:24:29.741910+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"adbb8af7ef62a6c95c5942b879f4c902","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\MerchantController::getAuthorizedStores(Line: 580) - All Authorized Stores Fetched Successfully by User ID : aa5c6efd7c2a216f600daaee1284d641 []"}
{"@timestamp":"2025-08-04T11:24:37.062249+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"ba898052fda775595895995c39afc040","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\MerchantController::getStoreTransactionTypes(Line: 1266) - Transaction types for Store Id: c351fd48f27e45050ff812855c30ca46 fetched successfully by User Id: .aa5c6efd7c2a216f600daaee1284d641 []"}
{"@timestamp":"2025-08-04T11:24:38.876924+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"3c391dfd51a93c062eb4535896fb4ecf","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\MerchantController::getStoreTransactionTypes(Line: 1266) - Transaction types for Store Id: 1b3c259c6680438555a4d13c042e8b42 fetched successfully by User Id: .aa5c6efd7c2a216f600daaee1284d641 []"}
{"@timestamp":"2025-08-04T11:24:41.683208+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"7199936cad9fdc5f70296b328f257610","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\\Http\\Controllers\\MerchantController::getAuthorizedTerminals(Line997) : Terminal List fetched successfully []"}
{"@timestamp":"2025-08-04T11:24:49.791181+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"9b528fc12fa2b914f74a6cc8a626ce2e","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\MerchantController::getAuthorizedStores(Line: 580) - All Authorized Stores Fetched Successfully by User ID : aa5c6efd7c2a216f600daaee1284d641 []"}
{"@timestamp":"2025-08-04T11:24:54.992126+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"5fab61a18c6c5745e485a395b49574fb","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\MerchantController::getStoreTransactionTypes(Line: 1266) - Transaction types for Store Id: c351fd48f27e45050ff812855c30ca46 fetched successfully by User Id: .aa5c6efd7c2a216f600daaee1284d641 []"}
{"@timestamp":"2025-08-04T11:25:03.368852+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"299e07550b359cab33c754b08ee9d6c5","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"ERROR","MESSAGE":"App\Http\Controllers\MerchantController::setupTerminal(Line: 711) - Exception occured during Terminal Creation By User ID :  aa5c6efd7c2a216f600daaee1284d641. {"Exception":"[object] (GuzzleHttp\\Exception\\RequestException(code: 0): cURL error 60: SSL certificate problem: unable to get local issuer certificate (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://firestore.googleapis.com/v1/projects/canpay-dev-b9e31/databases/(default)/documents/terminals/1a20d02bf82d9e7433782f289ddcebf0?key=AIzaSyAQ-EN11lE3WK46g_jXQFCnlUcPvAG0gos&updateMask.fieldPaths=terminal_id&updateMask.fieldPaths=unique_identification_id at C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlFactory.php:276)"}"}
{"@timestamp":"2025-08-04T11:25:27.742329+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"63e1e446fd4c63cb9d75636618ac650e","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"ERROR","MESSAGE":"App\Http\Controllers\MerchantController::setupTerminal(Line: 711) - Exception occured during Terminal Creation By User ID :  aa5c6efd7c2a216f600daaee1284d641. {"Exception":"[object] (GuzzleHttp\\Exception\\RequestException(code: 0): cURL error 60: SSL certificate problem: unable to get local issuer certificate (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://firestore.googleapis.com/v1/projects/canpay-dev-b9e31/databases/(default)/documents/terminals/1a23e6e35e2dc0815dad4798bb2777c4?key=AIzaSyAQ-EN11lE3WK46g_jXQFCnlUcPvAG0gos&updateMask.fieldPaths=terminal_id&updateMask.fieldPaths=unique_identification_id at C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlFactory.php:276)"}"}
{"@timestamp":"2025-08-04T11:25:42.859769+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"0f178da575b78be7a72f91ca2a095827","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"ERROR","MESSAGE":"App\Http\Controllers\MerchantController::setupTerminal(Line: 711) - Exception occured during Terminal Creation By User ID :  aa5c6efd7c2a216f600daaee1284d641. {"Exception":"[object] (GuzzleHttp\\Exception\\RequestException(code: 0): cURL error 60: SSL certificate problem: unable to get local issuer certificate (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://firestore.googleapis.com/v1/projects/canpay-dev-b9e31/databases/(default)/documents/terminals/1c7572202dee0856338779e78529b1df?key=AIzaSyAQ-EN11lE3WK46g_jXQFCnlUcPvAG0gos&updateMask.fieldPaths=terminal_id&updateMask.fieldPaths=unique_identification_id at C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlFactory.php:276)"}"}
{"@timestamp":"2025-08-04T11:27:58.582235+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"522c96b7fc8b43086573056416096831","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Clients\FirestoreHttpClient::storeIntoCollections(Line: 39) - Data stored into firestore {"terminal_id":"290f817c6983dfc71ef51ade191a6cfa","unique_identification_id":"fa754f41e3e2976a8836a0f611cfc9db"}"}
{"@timestamp":"2025-08-04T11:27:58.583029+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"522c96b7fc8b43086573056416096831","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\MerchantController::setupTerminal(Line: 706) - Terminal Created Successfully and inserted in Database with teminal_id : 290f817c6983dfc71ef51ade191a6cfa by User ID : aa5c6efd7c2a216f600daaee1284d641 []"}
{"@timestamp":"2025-08-04T11:28:02.258091+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"6cd63a3164c85e82c6a17c6ca302ca8c","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\\Http\\Controllers\\MerchantController::getAuthorizedTerminals(Line997) : Terminal List fetched successfully []"}
{"@timestamp":"2025-08-04T11:28:07.376433+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"aec907728b76e3cb330c986bd2c23cd7","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\MerchantController::getAuthorizedStores(Line: 580) - All Authorized Stores Fetched Successfully by User ID : aa5c6efd7c2a216f600daaee1284d641 []"}
{"@timestamp":"2025-08-04T11:28:07.816187+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"cd4b5ff5bb580011efa10ff357e39296","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\MerchantController::getStoreTransactionTypes(Line: 1266) - Transaction types for Store Id: c351fd48f27e45050ff812855c30ca46 fetched successfully by User Id: .aa5c6efd7c2a216f600daaee1284d641 []"}
{"@timestamp":"2025-08-04T11:28:10.936418+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"303516e41bd96dc42fca09c99b3fdd31","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\MerchantController::updateTerminal(Line: 821) - Terminal Updated Successfully for teminal_id : acbb91a44ee8e1a3cebfac8aec7ffde9 by User ID : aa5c6efd7c2a216f600daaee1284d641 []"}
{"@timestamp":"2025-08-04T11:28:12.648821+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"e94555df234e16c2ba6b9f42b362256d","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\\Http\\Controllers\\MerchantController::getAuthorizedTerminals(Line997) : Terminal List fetched successfully []"}
{"@timestamp":"2025-08-04T11:28:15.052927+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"494fdb53d4f5e0a555eb10808f2a74d4","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\MerchantController::getAuthorizedStores(Line: 580) - All Authorized Stores Fetched Successfully by User ID : aa5c6efd7c2a216f600daaee1284d641 []"}
{"@timestamp":"2025-08-04T11:28:15.479179+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"2e62748dfe5c387ab17611f6b6dc64e9","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\MerchantController::getStoreTransactionTypes(Line: 1266) - Transaction types for Store Id: c351fd48f27e45050ff812855c30ca46 fetched successfully by User Id: .aa5c6efd7c2a216f600daaee1284d641 []"}
{"@timestamp":"2025-08-04T11:28:16.307120+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"2202b0be53ccf9569a1a071aeea3f8fc","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\MerchantController::updateTerminal(Line: 821) - Terminal Updated Successfully for teminal_id : bde686b48405515ebb2aeace153155c8 by User ID : aa5c6efd7c2a216f600daaee1284d641 []"}
{"@timestamp":"2025-08-04T11:28:17.984013+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"6c1c236287f6ecba81184f873ae7bba6","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\\Http\\Controllers\\MerchantController::getAuthorizedTerminals(Line997) : Terminal List fetched successfully []"}
{"@timestamp":"2025-08-04T11:28:22.142864+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"97450adfb4b0594f62b7870010f524df","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\MerchantController::getAuthorizedStores(Line: 580) - All Authorized Stores Fetched Successfully by User ID : aa5c6efd7c2a216f600daaee1284d641 []"}
{"@timestamp":"2025-08-04T11:28:22.571257+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"edaa901f5fc8af89907ab41de723e80e","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\MerchantController::getStoreTransactionTypes(Line: 1266) - Transaction types for Store Id: c351fd48f27e45050ff812855c30ca46 fetched successfully by User Id: .aa5c6efd7c2a216f600daaee1284d641 []"}
{"@timestamp":"2025-08-04T11:28:24.050230+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"b018c832fa99c08b01fb3668df53a8e8","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\MerchantController::updateTerminal(Line: 821) - Terminal Updated Successfully for teminal_id : 77950d4123b3afed95e1134a7cdbfe51 by User ID : aa5c6efd7c2a216f600daaee1284d641 []"}
{"@timestamp":"2025-08-04T11:28:26.312338+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"d3dd3c36cbb3eee30d50aa3ba77e2abb","USER_ID":"aa5c6efd7c2a216f600daaee1284d641","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\\Http\\Controllers\\MerchantController::getAuthorizedTerminals(Line997) : Terminal List fetched successfully []"}
{"@timestamp":"2025-08-04T11:30:42.083239+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"f452ed695b339deb6ad1763e7da02b71","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Middleware\RegistrationSessionMiddleware::handle(Line: 34) - Registration session is valid and redirecting to API. []"}
{"@timestamp":"2025-08-04T11:30:42.322698+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"f452ed695b339deb6ad1763e7da02b71","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\RegistrationController::checkDuplicatePhoneNumber(Line: 1692) - Mobile number available for use. []"}
{"@timestamp":"2025-08-04T11:30:43.002117+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"a93df8724b3d16ac17cc92b2d5194b59","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Middleware\RegistrationSessionMiddleware::handle(Line: 34) - Registration session is valid and redirecting to API. []"}
{"@timestamp":"2025-08-04T11:30:43.186624+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"a93df8724b3d16ac17cc92b2d5194b59","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\RegistrationController::checkConsecutiveRegistrationAttempts(Line: 238) - Not an consecutive attempt for session :1754306923588 []"}
{"@timestamp":"2025-08-04T11:30:43.259202+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"a93df8724b3d16ac17cc92b2d5194b59","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"DEBUG","MESSAGE":"array (   'flag' => false,   'id' => 'b1272264b2e9ea510cc04180143affad', ) []"}
{"@timestamp":"2025-08-04T11:30:43.259937+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"a93df8724b3d16ac17cc92b2d5194b59","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\RegistrationController::sendOTP(Line: 150) - =====================TESTING MODE ENABLED======================= []"}
{"@timestamp":"2025-08-04T11:30:43.260042+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"a93df8724b3d16ac17cc92b2d5194b59","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\RegistrationController::sendOTP(Line: 164) - OTP sent to phone number : ********** []"}
{"@timestamp":"2025-08-04T11:30:50.687484+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"57af2de5888c56d8b5d12c6011a3bd0c","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Middleware\RegistrationSessionMiddleware::handle(Line: 34) - Registration session is valid and redirecting to API. []"}
{"@timestamp":"2025-08-04T11:30:50.843621+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"57af2de5888c56d8b5d12c6011a3bd0c","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\RegistrationController::verifyOTP(Line: 300) - OTP verified successsfully for phone number : ********** []"}
{"@timestamp":"2025-08-04T11:30:52.597407+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"042e4166417c77ffade936217ce579c4","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Middleware\RegistrationSessionMiddleware::handle(Line: 34) - Registration session is valid and redirecting to API. []"}
{"@timestamp":"2025-08-04T11:30:52.760496+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"042e4166417c77ffade936217ce579c4","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\RegistrationController::redirectToValidStep(Line: 1947) - Redirecting to step: 2 []"}
{"@timestamp":"2025-08-04T11:31:09.291165+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"041b84fe58ee82a933c7903e685edae7","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Middleware\RegistrationSessionMiddleware::handle(Line: 34) - Registration session is valid and redirecting to API. []"}
{"@timestamp":"2025-08-04T11:31:09.440762+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"041b84fe58ee82a933c7903e685edae7","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\RegistrationController::enterPIN(Line: 2007) - Storing quick access pin for the consumer []"}
{"@timestamp":"2025-08-04T11:32:57.928285+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"876f13cb33c0c4a07c1661a33e6d82e8","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Middleware\RegistrationSessionMiddleware::handle(Line: 34) - Registration session is valid and redirecting to API. []"}
{"@timestamp":"2025-08-04T11:32:58.095534+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"876f13cb33c0c4a07c1661a33e6d82e8","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\RegistrationController::checkDuplicateEmail(Line: 1640) - Email available for use. []"}
{"@timestamp":"2025-08-04T11:32:58.901419+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"f4116ba0dd9e699f4e969fc6e664d38d","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Middleware\RegistrationSessionMiddleware::handle(Line: 34) - Registration session is valid and redirecting to API. []"}
{"@timestamp":"2025-08-04T11:32:59.065737+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"f4116ba0dd9e699f4e969fc6e664d38d","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\RegistrationController::idValidation(Line: 352) - Starting identity validation for consumer... []"}
{"@timestamp":"2025-08-04T11:33:00.511366+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"f4116ba0dd9e699f4e969fc6e664d38d","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"Topic: consumer_data_for_matching_algorithm posted to kafka returned response: {"error_code":200,"cluster_id":"lkc-577jyz","topic_name":"consumer_data_for_matching_algorithm","partition_id":0,"offset":8008,"timestamp":"2025-08-04T11:33:00.272Z","value":{"type":"JSON","size":574}}  []"}
{"@timestamp":"2025-08-04T11:33:02.177364+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"f4116ba0dd9e699f4e969fc6e664d38d","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Factories\IdValidatorPlaid\IdValidatorPlaidFactory::getPlaidProfile(Line: 128) - Plaid profile created successfully with profile id : prf_3H7xMg5CuPeqrF []"}
{"@timestamp":"2025-08-04T11:33:02.177718+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"f4116ba0dd9e699f4e969fc6e664d38d","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Clients\PlaidHttpClient::searchIdentity(60) - Plaid search API request:  {"body":"{\r\n                    \"data\": {\r\n                      \"type\": \"identity_search\",\r\n                      \"attributes\": {\r\n                        \"phone\": {\r\n                          \"number\": \"+***********\"\r\n                        },\r\n                        \"name\": {\r\n                          \"first\": \"Ashi\",\r\n                          \"last\": \"Yogi\"\r\n                        },\r\n                        \"birth\": {\r\n                            \"day\": 1,\r\n                            \"month\": 12,\r\n                            \"year\": 1999\r\n                        },\r\n                        \"us_address\": {\r\n                          \"street\": \"123 New york\",\r\n                          \"city\": \"New York\",\r\n                          \"subdivision\": \"NY\",\r\n                          \"postal_code\": \"12211\"\r\n                        }\r\n                      },\r\n                      \"relationships\": {\r\n                        \"profile\": {\r\n                          \"data\": {\r\n                            \"type\": \"profile\",\r\n                            \"id\": \"prf_3H7xMg5CuPeqrF\"\r\n                          }\r\n                        }\r\n                      }\r\n                    }\r\n                  }","headers":{"Content-Type":"application/json","Plaid-Version":"2020-09-14","Plaid-Client-ID":"66e0826bb73fe9001a5abca5","Plaid-Secret":"4379846315b3dad001b063a72caa03"}}"}
{"@timestamp":"2025-08-04T11:33:03.107201+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"f4116ba0dd9e699f4e969fc6e664d38d","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Clients\PlaidHttpClient::searchIdentity(Line: 72) - Plaid search api with phone returned row response: {"data":{"attributes":{"birth":{"day":1,"month":12,"year":1999},"created_at":"2025-08-04T11:33:02Z","name":{"first":"Ashi","last":"Yogi","middle":null},"phone":{"number":"+***********"},"us_address":{"city":"New York","postal_code":"12211","street":"123 New york","subdivision":"NY"}},"id":"idnsch_bRBpz9YBfWHTNG","relationships":{"identity_records":{"data":[{"id":"idnrcd_aNtRhzL6TtbbKr","type":"identity_record"},{"id":"idnrcd_3ofUCwLEiPJsab","type":"identity_record"}]},"profile":{"data":{"id":"prf_3H7xMg5CuPeqrF","type":"profile"}}},"type":"identity_search"},"included":[{"attributes":[],"id":"idnrcd_aNtRhzL6TtbbKr","relationships":{"addresses":{"data":[{"id":"usaddr_6snLCGkxUTqU17","type":"us_address"},{"id":"usaddr_4Q2pvzuUdzVXF8","type":"us_address"}]},"births":{"data":[{"id":"bth_egfTsCKKLBowi5","type":"birth"},{"id":"bth_8ngnEUy7aVwLSu","type":"birth"}]},"deaths":{"data":[{"id":"dth_eB5Bb3GQARG5wX","type":"death"}]},"names":{"data":[{"id":"nam_cTKEfZErBSWxq3","type":"name"},{"id":"nam_2Xow6mvb2NnffZ","type":"name"}]},"phones":{"data":[{"id":"phn_eyzEio7NRAckwE","type":"phone"},{"id":"phn_9s6eW9wKu4byBr","type":"phone"},{"id":"phn_8CA7G3MCQHWMVM","type":"phone"}]},"ssns":{"data":[{"id":"ssn_ct2cJySkN2fykC","type":"ssn"},{"id":"ssn_bjYu9fPweh2msH","type":"ssn"}]}},"type":"identity_record"},{"attributes":[],"id":"idnrcd_3ofUCwLEiPJsab","relationships":{"addresses":{"data":[{"id":"usaddr_aM8bzBPEKfxF5F","type":"us_address"},{"id":"usaddr_4KRZ8RmN2o3oGN","type":"us_address"}]},"births":{"data":[{"id":"bth_bTPVCajeUU419s","type":"birth"}]},"deaths":{"data":[]},"names":{"data":[{"id":"nam_2iiB9YWFuzVuyu","type":"name"}]},"phones":{"data":[{"id":"phn_dCG9oLTyKHCpYZ","type":"phone"},{"id":"phn_2MA3X88vKxhTw9","type":"phone"}]},"ssns":{"data":[{"id":"ssn_74K8uDUmFGZqcV","type":"ssn"}]}},"type":"identity_record"},{"attributes":{"city":"PAWNEE","country_code":"US","postal_code":"46001","street":"123 MAIN ST","subdivision":"IN","usage":{"earliest":{"month":4,"year":2009},"latest":{"month":2,"year":2015}}},"id":"usaddr_6snLCGkxUTqU17","type":"us_address"},{"attributes":{"city":"EAGLETON","country_code":"US","postal_code":"46002","street":null,"subdivision":"IN","usage":null},"id":"usaddr_4Q2pvzuUdzVXF8","type":"us_address"},{"attributes":{"day":18,"month":1,"year":1975},"id":"bth_egfTsCKKLBowi5","type":"birth"},{"attributes":{"day":18,"month":1,"year":1976},"id":"bth_8ngnEUy7aVwLSu","type":"birth"},{"attributes":{"day":24,"month":2,"year":2015},"id":"dth_eB5Bb3GQARG5wX","type":"death"},{"attributes":{"first":"LESLIE","last":"KNOPE","middle":"BARBARA"},"id":"nam_cTKEfZErBSWxq3","type":"name"},{"attributes":{"first":"LESLIE","last":"KNOPE-WYATT","middle":null},"id":"nam_2Xow6mvb2NnffZ","type":"name"},{"attributes":{"number":"+11236547890","usage":{"earliest":{"month":1,"year":2017},"latest":{"month":10,"year":2022}}},"id":"phn_eyzEio7NRAckwE","type":"phone"},{"attributes":{"number":"+12345678909","usage":{"earliest":{"month":1,"year":2017},"latest":{"month":10,"year":2022}}},"id":"phn_9s6eW9wKu4byBr","type":"phone"},{"attributes":{"number":"+19876543212","usage":{"earliest":{"month":12,"year":2006},"latest":{"month":1,"year":2017}}},"id":"phn_8CA7G3MCQHWMVM","type":"phone"},{"attributes":{"area":"123","group":"45","number":"123456788","serial":"6788"},"id":"ssn_ct2cJySkN2fykC","type":"ssn"},{"attributes":{"area":"123","group":"45","number":"123456789","serial":"6789"},"id":"ssn_bjYu9fPweh2msH","type":"ssn"},{"attributes":{"city":"NEW YORK","country_code":"US","postal_code":"10001","street":"123 MAIN ST","subdivision":"NY","usage":null},"id":"usaddr_aM8bzBPEKfxF5F","type":"us_address"},{"attributes":{"city":"SHERMAN OAKS","country_code":"US","postal_code":"91423","street":"4620 WORTSER AVE APT 101","subdivision":"CA","usage":null},"id":"usaddr_4KRZ8RmN2o3oGN","type":"us_address"},{"attributes":{"day":null,"month":null,"year":1974},"id":"bth_bTPVCajeUU419s","type":"birth"},{"attributes":{"first":"BEN","last":"WYATT","middle":null},"id":"nam_2iiB9YWFuzVuyu","type":"name"},{"attributes":{"number":"+11122334455","usage":{"earliest":{"month":12,"year":1995},"latest":{"month":1,"year":2022}}},"id":"phn_dCG9oLTyKHCpYZ","type":"phone"},{"attributes":{"number":"+***********","usage":{"earliest":{"month":12,"year":1995},"latest":{"month":1,"year":2022}}},"id":"phn_2MA3X88vKxhTw9","type":"phone"},{"attributes":{"area":"111","group":"22","number":"111223333","serial":"3333"},"id":"ssn_74K8uDUmFGZqcV","type":"ssn"}],"request_id":"K8zCcR6VXGvLE7n"} []"}
{"@timestamp":"2025-08-04T11:33:03.107725+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"f4116ba0dd9e699f4e969fc6e664d38d","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Clients\PlaidHttpClient::searchIdentity(Line: 83) - Plaid search api with phone returned response : {"data":{"attributes":{"birth":{"day":1,"month":12,"year":1999},"created_at":"2025-08-04T11:33:02Z","name":{"first":"Ashi","last":"Yogi","middle":null},"phone":{"number":"+***********"},"us_address":{"city":"New York","postal_code":"12211","street":"123 New york","subdivision":"NY"}},"id":"idnsch_bRBpz9YBfWHTNG","relationships":{"identity_records":{"data":[{"id":"idnrcd_aNtRhzL6TtbbKr","type":"identity_record"},{"id":"idnrcd_3ofUCwLEiPJsab","type":"identity_record"}]},"profile":{"data":{"id":"prf_3H7xMg5CuPeqrF","type":"profile"}}},"type":"identity_search"},"included":[{"attributes":[],"id":"idnrcd_aNtRhzL6TtbbKr","relationships":{"addresses":{"data":[{"id":"usaddr_6snLCGkxUTqU17","type":"us_address"},{"id":"usaddr_4Q2pvzuUdzVXF8","type":"us_address"}]},"births":{"data":[{"id":"bth_egfTsCKKLBowi5","type":"birth"},{"id":"bth_8ngnEUy7aVwLSu","type":"birth"}]},"deaths":{"data":[{"id":"dth_eB5Bb3GQARG5wX","type":"death"}]},"names":{"data":[{"id":"nam_cTKEfZErBSWxq3","type":"name"},{"id":"nam_2Xow6mvb2NnffZ","type":"name"}]},"phones":{"data":[{"id":"phn_eyzEio7NRAckwE","type":"phone"},{"id":"phn_9s6eW9wKu4byBr","type":"phone"},{"id":"phn_8CA7G3MCQHWMVM","type":"phone"}]},"ssns":{"data":[{"id":"ssn_ct2cJySkN2fykC","type":"ssn"},{"id":"ssn_bjYu9fPweh2msH","type":"ssn"}]}},"type":"identity_record"},{"attributes":[],"id":"idnrcd_3ofUCwLEiPJsab","relationships":{"addresses":{"data":[{"id":"usaddr_aM8bzBPEKfxF5F","type":"us_address"},{"id":"usaddr_4KRZ8RmN2o3oGN","type":"us_address"}]},"births":{"data":[{"id":"bth_bTPVCajeUU419s","type":"birth"}]},"deaths":{"data":[]},"names":{"data":[{"id":"nam_2iiB9YWFuzVuyu","type":"name"}]},"phones":{"data":[{"id":"phn_dCG9oLTyKHCpYZ","type":"phone"},{"id":"phn_2MA3X88vKxhTw9","type":"phone"}]},"ssns":{"data":[{"id":"ssn_74K8uDUmFGZqcV","type":"ssn"}]}},"type":"identity_record"},{"attributes":{"city":"PAWNEE","country_code":"US","postal_code":"46001","street":"123 MAIN ST","subdivision":"IN","usage":{"earliest":{"month":4,"year":2009},"latest":{"month":2,"year":2015}}},"id":"usaddr_6snLCGkxUTqU17","type":"us_address"},{"attributes":{"city":"EAGLETON","country_code":"US","postal_code":"46002","street":null,"subdivision":"IN","usage":null},"id":"usaddr_4Q2pvzuUdzVXF8","type":"us_address"},{"attributes":{"day":18,"month":1,"year":1975},"id":"bth_egfTsCKKLBowi5","type":"birth"},{"attributes":{"day":18,"month":1,"year":1976},"id":"bth_8ngnEUy7aVwLSu","type":"birth"},{"attributes":{"day":24,"month":2,"year":2015},"id":"dth_eB5Bb3GQARG5wX","type":"death"},{"attributes":{"first":"LESLIE","last":"KNOPE","middle":"BARBARA"},"id":"nam_cTKEfZErBSWxq3","type":"name"},{"attributes":{"first":"LESLIE","last":"KNOPE-WYATT","middle":null},"id":"nam_2Xow6mvb2NnffZ","type":"name"},{"attributes":{"number":"+11236547890","usage":{"earliest":{"month":1,"year":2017},"latest":{"month":10,"year":2022}}},"id":"phn_eyzEio7NRAckwE","type":"phone"},{"attributes":{"number":"+12345678909","usage":{"earliest":{"month":1,"year":2017},"latest":{"month":10,"year":2022}}},"id":"phn_9s6eW9wKu4byBr","type":"phone"},{"attributes":{"number":"+19876543212","usage":{"earliest":{"month":12,"year":2006},"latest":{"month":1,"year":2017}}},"id":"phn_8CA7G3MCQHWMVM","type":"phone"},{"attributes":{"number":"MTIzNDU2Nzg4","group":"MTIz","area":"NDU=","serial":"Njc4OA=="},"id":"ssn_ct2cJySkN2fykC","type":"ssn"},{"attributes":{"number":"MTIzNDU2Nzg5","group":"MTIz","area":"NDU=","serial":"Njc4OQ=="},"id":"ssn_bjYu9fPweh2msH","type":"ssn"},{"attributes":{"city":"NEW YORK","country_code":"US","postal_code":"10001","street":"123 MAIN ST","subdivision":"NY","usage":null},"id":"usaddr_aM8bzBPEKfxF5F","type":"us_address"},{"attributes":{"city":"SHERMAN OAKS","country_code":"US","postal_code":"91423","street":"4620 WORTSER AVE APT 101","subdivision":"CA","usage":null},"id":"usaddr_4KRZ8RmN2o3oGN","type":"us_address"},{"attributes":{"day":null,"month":null,"year":1974},"id":"bth_bTPVCajeUU419s","type":"birth"},{"attributes":{"first":"BEN","last":"WYATT","middle":null},"id":"nam_2iiB9YWFuzVuyu","type":"name"},{"attributes":{"number":"+11122334455","usage":{"earliest":{"month":12,"year":1995},"latest":{"month":1,"year":2022}}},"id":"phn_dCG9oLTyKHCpYZ","type":"phone"},{"attributes":{"number":"+***********","usage":{"earliest":{"month":12,"year":1995},"latest":{"month":1,"year":2022}}},"id":"phn_2MA3X88vKxhTw9","type":"phone"},{"attributes":{"number":"MTExMjIzMzMz","group":"MTEx","area":"MjI=","serial":"MzMzMw=="},"id":"ssn_74K8uDUmFGZqcV","type":"ssn"}],"request_id":"K8zCcR6VXGvLE7n"} []"}
{"@timestamp":"2025-08-04T11:33:03.749676+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"f4116ba0dd9e699f4e969fc6e664d38d","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Clients\PlaidHttpClient::identityAssessment(Line: 124) - Plaid assessment api returned response : {   "data": {     "attributes": {       "birth": {         "day": 1,         "month": 12,         "year": 1999       },       "created_at": "2025-08-04T11:33:03Z",       "name": {         "first": "Ashi",         "last": "Yogi",         "middle": null       },       "phone": {         "number": "+***********"       },       "us_address": {         "city": "New York",         "postal_code": "12211",         "street": "123 New york",         "subdivision": "NY"       }     },     "id": "idnasm_brkAHoQEbqhaQU",     "relationships": {       "identity_record_comparisons": {         "data": [           {             "id": "idrcmp_23ziFZn5MNRy1S",             "type": "identity_record_comparison"           },           {             "id": "idrcmp_5gFMEduQZBpkuG",             "type": "identity_record_comparison"           }         ]       },       "identity_search": {         "data": {           "id": "idnsch_bRBpz9YBfWHTNG",           "type": "identity_search"         }       }     },     "type": "identity_assessment"   },   "included": [     {       "attributes": {         "score": 18       },       "id": "idrcmp_23ziFZn5MNRy1S",       "relationships": {         "address_comparisons": {           "data": [             {               "id": "usacmp_2yxMHfVGzk3SJs",               "type": "us_address_comparison"             },             {               "id": "usacmp_ar7Xoze3XXVP1y",               "type": "us_address_comparison"             }           ]         },         "birth_comparisons": {           "data": [             {               "id": "bthcmp_4W7Gu4FPiADwPR",               "type": "birth_comparison"             }           ]         },         "identity_record": {           "data": {             "id": "idnrcd_3ofUCwLEiPJsab",             "type": "identity_record"           }         },         "name_comparisons": {           "data": [             {               "id": "namcmp_8za2tH2gtWcRUg",               "type": "name_comparison"             }           ]         },         "phone_comparisons": {           "data": [             {               "id": "phncmp_bCsmgc9K7jHsVy",               "type": "phone_comparison"             },             {               "id": "phncmp_acmfhwvzCTAPqG",               "type": "phone_comparison"             }           ]         },         "ssn_comparisons": {           "data": []         }       },       "type": "identity_record_comparison"     },     {       "attributes": {         "score": 7       },       "id": "idrcmp_5gFMEduQZBpkuG",       "relationships": {         "address_comparisons": {           "data": [             {               "id": "usacmp_3Yf2L7ycpharRw",               "type": "us_address_comparison"             },             {               "id": "usacmp_8TwWpsRtjLFjTc",               "type": "us_address_comparison"             }           ]         },         "birth_comparisons": {           "data": [             {               "id": "bthcmp_8BrXKKocTtJg4h",               "type": "birth_comparison"             },             {               "id": "bthcmp_7Ho9KTA4ZHTmZV",               "type": "birth_comparison"             }           ]         },         "identity_record": {           "data": {             "id": "idnrcd_aNtRhzL6TtbbKr",             "type": "identity_record"           }         },         "name_comparisons": {           "data": [             {               "id": "namcmp_ceionHDHfntTrU",               "type": "name_comparison"             },             {               "id": "namcmp_4zWT4B135dH8im",               "type": "name_comparison"             }           ]         },         "phone_comparisons": {           "data": [             {               "id": "phncmp_9sSTnA94LpLfYo",               "type": "phone_comparison"             },             {               "id": "phncmp_2Y5t1DVVWZ7krM",               "type": "phone_comparison"             },             {               "id": "phncmp_7BGG2RbHnwBTHV",               "type": "phone_comparison"             }           ]         },         "ssn_comparisons": {           "data": []         }       },       "type": "identity_record_comparison"     },     {       "attributes": {         "components": {           "city": {             "input": "New York",             "score": 0,             "source": "SHERMAN OAKS"           },           "postal_code": {             "input": "12211",             "score": 0,             "source": "91423"           },           "street": {             "input": "123 New york",             "score": 0,             "source": "4620 WORTSER AVE APT 101"           },           "subdivision": {             "input": "NY",             "score": 0,             "source": "CA"           }         },         "score": 0       },       "id": "usacmp_2yxMHfVGzk3SJs",       "relationships": {         "source_record": {           "data": {             "id": "usaddr_4KRZ8RmN2o3oGN",             "type": "us_address"           }         }       },       "type": "us_address_comparison"     },     {       "attributes": {         "components": {           "city": {             "input": "New York",             "score": 100,             "source": "NEW YORK"           },           "postal_code": {             "input": "12211",             "score": 0,             "source": "10001"           },           "street": {             "input": "123 New york",             "score": 53,             "source": "123 MAIN ST"           },           "subdivision": {             "input": "NY",             "score": 100,             "source": "NY"           }         },         "score": 60       },       "id": "usacmp_ar7Xoze3XXVP1y",       "relationships": {         "source_record": {           "data": {             "id": "usaddr_aM8bzBPEKfxF5F",             "type": "us_address"           }         }       },       "type": "us_address_comparison"     },     {       "attributes": {         "components": {           "city": {             "input": "New York",             "score": 0,             "source": "EAGLETON"           },           "postal_code": {             "input": "12211",             "score": 0,             "source": "46002"           },           "street": {             "input": "123 New york",             "score": 0,             "source": null           },           "subdivision": {             "input": "NY",             "score": 0,             "source": "IN"           }         },         "score": 0       },       "id": "usacmp_3Yf2L7ycpharRw",       "relationships": {         "source_record": {           "data": {             "id": "usaddr_4Q2pvzuUdzVXF8",             "type": "us_address"           }         }       },       "type": "us_address_comparison"     },     {       "attributes": {         "components": {           "city": {             "input": "New York",             "score": 0,             "source": "PAWNEE"           },           "postal_code": {             "input": "12211",             "score": 0,             "source": "46001"           },           "street": {             "input": "123 New york",             "score": 53,             "source": "123 MAIN ST"           },           "subdivision": {             "input": "NY",             "score": 0,             "source": "IN"           }         },         "score": 26       },       "id": "usacmp_8TwWpsRtjLFjTc",       "relationships": {         "source_record": {           "data": {             "id": "usaddr_6snLCGkxUTqU17",             "type": "us_address"           }         }       },       "type": "us_address_comparison"     },     {       "attributes": {         "components": {           "day": {             "input": 1,             "score": null,             "source": null           },           "month": {             "input": 12,             "score": null,             "source": null           },           "year": {             "input": 1999,             "score": 0,             "source": 1974           }         },         "score": 0       },       "id": "bthcmp_4W7Gu4FPiADwPR",       "relationships": {         "source_record": {           "data": {             "id": "bth_bTPVCajeUU419s",             "type": "birth"           }         }       },       "type": "birth_comparison"     },     {       "attributes": {         "components": {           "day": {             "input": 1,             "score": 0,             "source": 18           },           "month": {             "input": 12,             "score": 0,             "source": 1           },           "year": {             "input": 1999,             "score": 0,             "source": 1975           }         },         "score": 0       },       "id": "bthcmp_8BrXKKocTtJg4h",       "relationships": {         "source_record": {           "data": {             "id": "bth_egfTsCKKLBowi5",             "type": "birth"           }         }       },       "type": "birth_comparison"     },     {       "attributes": {         "components": {           "day": {             "input": 1,             "score": 0,             "source": 18           },           "month": {             "input": 12,             "score": 0,             "source": 1           },           "year": {             "input": 1999,             "score": 0,             "source": 1976           }         },         "score": 0       },       "id": "bthcmp_7Ho9KTA4ZHTmZV",       "relationships": {         "source_record": {           "data": {             "id": "bth_8ngnEUy7aVwLSu",             "type": "birth"           }         }       },       "type": "birth_comparison"     },     {       "attributes": {         "components": {           "number": {             "input": "+***********",             "score": 0,             "source": "+***********"           }         },         "score": 0       },       "id": "phncmp_bCsmgc9K7jHsVy",       "relationships": {         "source_record": {           "data": {             "id": "phn_2MA3X88vKxhTw9",             "type": "phone"           }         }       },       "type": "phone_comparison"     },     {       "attributes": {         "components": {           "number": {             "input": "+***********",             "score": 0,             "source": "+11122334455"           }         },         "score": 0       },       "id": "phncmp_acmfhwvzCTAPqG",       "relationships": {         "source_record": {           "data": {             "id": "phn_dCG9oLTyKHCpYZ",             "type": "phone"           }         }       },       "type": "phone_comparison"     },     {       "attributes": {         "components": {           "number": {             "input": "+***********",             "score": 0,             "source": "+19876543212"           }         },         "score": 0       },       "id": "phncmp_9sSTnA94LpLfYo",       "relationships": {         "source_record": {           "data": {             "id": "phn_8CA7G3MCQHWMVM",             "type": "phone"           }         }       },       "type": "phone_comparison"     },     {       "attributes": {         "components": {           "number": {             "input": "+***********",             "score": 0,             "source": "+12345678909"           }         },         "score": 0       },       "id": "phncmp_2Y5t1DVVWZ7krM",       "relationships": {         "source_record": {           "data": {             "id": "phn_9s6eW9wKu4byBr",             "type": "phone"           }         }       },       "type": "phone_comparison"     },     {       "attributes": {         "components": {           "number": {             "input": "+***********",             "score": 0,             "source": "+11236547890"           }         },         "score": 0       },       "id": "phncmp_7BGG2RbHnwBTHV",       "relationships": {         "source_record": {           "data": {             "id": "phn_eyzEio7NRAckwE",             "type": "phone"           }         }       },       "type": "phone_comparison"     },     {       "attributes": {         "components": {           "first": {             "input": "Ashi",             "score": 0,             "source": "BEN"           },           "last": {             "input": "Yogi",             "score": 0,             "source": "WYATT"           },           "middle": {             "input": null,             "score": null,             "source": null           }         },         "score": 11       },       "id": "namcmp_8za2tH2gtWcRUg",       "relationships": {         "source_record": {           "data": {             "id": "nam_2iiB9YWFuzVuyu",             "type": "name"           }         }       },       "type": "name_comparison"     },     {       "attributes": {         "components": {           "first": {             "input": "Ashi",             "score": 0,             "source": "LESLIE"           },           "last": {             "input": "Yogi",             "score": 0,             "source": "KNOPE-WYATT"           },           "middle": {             "input": null,             "score": null,             "source": null           }         },         "score": 0       },       "id": "namcmp_ceionHDHfntTrU",       "relationships": {         "source_record": {           "data": {             "id": "nam_2Xow6mvb2NnffZ",             "type": "name"           }         }       },       "type": "name_comparison"     },     {       "attributes": {         "components": {           "first": {             "input": "Ashi",             "score": 0,             "source": "LESLIE"           },           "last": {             "input": "Yogi",             "score": 0,             "source": "KNOPE"           },           "middle": {             "input": null,             "score": null,             "source": "BARBARA"           }         },         "score": 0       },       "id": "namcmp_4zWT4B135dH8im",       "relationships": {         "source_record": {           "data": {             "id": "nam_cTKEfZErBSWxq3",             "type": "name"           }         }       },       "type": "name_comparison"     }   ],   "request_id": "r8iAcwA63RVRRga" } []"}
{"@timestamp":"2025-08-04T11:33:03.796142+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"f4116ba0dd9e699f4e969fc6e664d38d","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"saveCognitoResponsePostingHistory(Line: 2300) - Save Cognito response posting history for phone no: ********** []"}
{"@timestamp":"2025-08-04T11:33:05.148508+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"f4116ba0dd9e699f4e969fc6e664d38d","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"Topic: network-consumer-cognito-data posted to kafka returned response: {"error_code":200,"cluster_id":"lkc-577jyz","topic_name":"network-consumer-cognito-data","partition_id":0,"offset":4853,"timestamp":"2025-08-04T11:33:04.922Z","value":{"type":"JSON","size":11898}}  []"}
{"@timestamp":"2025-08-04T11:33:05.153924+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"f4116ba0dd9e699f4e969fc6e664d38d","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Factories\PlaidValidationRules\PlaidRulesWithoutSSN::enrichPlaidData(Line: 34) - ====STARTING PLAID RULE ENGINE WITHOUT SSN AS INPUT==== []"}
{"@timestamp":"2025-08-04T11:33:05.154164+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"f4116ba0dd9e699f4e969fc6e664d38d","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Factories\PlaidValidationRules\PlaidRulesWithoutSSN::enrichPlaidData(Line: 82) - ====RETURNING ENRICH DATA==== {"cognito_search_id":"idnsch_bRBpz9YBfWHTNG","session_id":"b1272264b2e9ea510cc04180143affad","input_name":"Ashi  Yogi","input_phone":"**********","input_address":"123 New york New York NY 12211","input_ssn":"","input_dob":"1999-12-01","deaths":[{"day":24,"month":2,"year":2015}],"average_scores":[18,7],"scores":[{"average_score":18,"phone_score":-1,"name_score":"11","address_score":-1,"ssn_score":-1,"birth_score":-1,"phone_components":"{\"number\":{\"input\":\"+***********\",\"score\":0,\"source\":\"+***********\"}}","name_components":"{\"first\":{\"input\":\"Ashi\",\"score\":0,\"source\":\"BEN\"},\"last\":{\"input\":\"Yogi\",\"score\":0,\"source\":\"WYATT\"},\"middle\":{\"input\":null,\"score\":null,\"source\":null}}","address_components":"{\"city\":{\"input\":\"New York\",\"score\":0,\"source\":\"SHERMAN OAKS\"},\"postal_code\":{\"input\":\"12211\",\"score\":0,\"source\":\"91423\"},\"street\":{\"input\":\"123 New york\",\"score\":0,\"source\":\"4620 WORTSER AVE APT 101\"},\"subdivision\":{\"input\":\"NY\",\"score\":0,\"source\":\"CA\"}}","ssn_components":"","birth_components":"{\"day\":{\"input\":1,\"score\":null,\"source\":null},\"month\":{\"input\":12,\"score\":null,\"source\":null},\"year\":{\"input\":1999,\"score\":0,\"source\":1974}}"},{"average_score":18,"phone_score":-1,"name_score":-1,"address_score":"60","ssn_score":-1,"birth_score":-1,"phone_components":"{\"number\":{\"input\":\"+***********\",\"score\":0,\"source\":\"+11122334455\"}}","name_components":"{\"first\":{\"input\":\"Ashi\",\"score\":0,\"source\":\"BEN\"},\"last\":{\"input\":\"Yogi\",\"score\":0,\"source\":\"WYATT\"},\"middle\":{\"input\":null,\"score\":null,\"source\":null}}","address_components":"{\"city\":{\"input\":\"New York\",\"score\":100,\"source\":\"NEW YORK\"},\"postal_code\":{\"input\":\"12211\",\"score\":0,\"source\":\"10001\"},\"street\":{\"input\":\"123 New york\",\"score\":53,\"source\":\"123 MAIN ST\"},\"subdivision\":{\"input\":\"NY\",\"score\":100,\"source\":\"NY\"}}","ssn_components":"","birth_components":"{\"day\":{\"input\":1,\"score\":null,\"source\":null},\"month\":{\"input\":12,\"score\":null,\"source\":null},\"year\":{\"input\":1999,\"score\":0,\"source\":1974}}"},{"average_score":7,"phone_score":-1,"name_score":-1,"address_score":-1,"ssn_score":-1,"birth_score":-1,"phone_components":"{\"number\":{\"input\":\"+***********\",\"score\":0,\"source\":\"+19876543212\"}}","name_components":"{\"first\":{\"input\":\"Ashi\",\"score\":0,\"source\":\"LESLIE\"},\"last\":{\"input\":\"Yogi\",\"score\":0,\"source\":\"KNOPE-WYATT\"},\"middle\":{\"input\":null,\"score\":null,\"source\":null}}","address_components":"{\"city\":{\"input\":\"New York\",\"score\":0,\"source\":\"EAGLETON\"},\"postal_code\":{\"input\":\"12211\",\"score\":0,\"source\":\"46002\"},\"street\":{\"input\":\"123 New york\",\"score\":0,\"source\":null},\"subdivision\":{\"input\":\"NY\",\"score\":0,\"source\":\"IN\"}}","ssn_components":"","birth_components":"{\"day\":{\"input\":1,\"score\":0,\"source\":18},\"month\":{\"input\":12,\"score\":0,\"source\":1},\"year\":{\"input\":1999,\"score\":0,\"source\":1975}}"},{"average_score":7,"phone_score":-1,"name_score":-1,"address_score":"26","ssn_score":-1,"birth_score":-1,"phone_components":"{\"number\":{\"input\":\"+***********\",\"score\":0,\"source\":\"+12345678909\"}}","name_components":"{\"first\":{\"input\":\"Ashi\",\"score\":0,\"source\":\"LESLIE\"},\"last\":{\"input\":\"Yogi\",\"score\":0,\"source\":\"KNOPE\"},\"middle\":{\"input\":null,\"score\":null,\"source\":\"BARBARA\"}}","address_components":"{\"city\":{\"input\":\"New York\",\"score\":0,\"source\":\"PAWNEE\"},\"postal_code\":{\"input\":\"12211\",\"score\":0,\"source\":\"46001\"},\"street\":{\"input\":\"123 New york\",\"score\":53,\"source\":\"123 MAIN ST\"},\"subdivision\":{\"input\":\"NY\",\"score\":0,\"source\":\"IN\"}}","ssn_components":"","birth_components":"{\"day\":{\"input\":1,\"score\":0,\"source\":18},\"month\":{\"input\":12,\"score\":0,\"source\":1},\"year\":{\"input\":1999,\"score\":0,\"source\":1976}}"},{"average_score":7,"phone_score":-1,"name_score":-1,"address_score":-1,"ssn_score":-1,"birth_score":-1,"phone_components":"{\"number\":{\"input\":\"+***********\",\"score\":0,\"source\":\"+11236547890\"}}","name_components":"{\"first\":{\"input\":\"Ashi\",\"score\":0,\"source\":\"LESLIE\"},\"last\":{\"input\":\"Yogi\",\"score\":0,\"source\":\"KNOPE\"},\"middle\":{\"input\":null,\"score\":null,\"source\":\"BARBARA\"}}","address_components":"{\"city\":{\"input\":\"New York\",\"score\":0,\"source\":\"PAWNEE\"},\"postal_code\":{\"input\":\"12211\",\"score\":0,\"source\":\"46001\"},\"street\":{\"input\":\"123 New york\",\"score\":53,\"source\":\"123 MAIN ST\"},\"subdivision\":{\"input\":\"NY\",\"score\":0,\"source\":\"IN\"}}","ssn_components":"","birth_components":"{\"day\":{\"input\":1,\"score\":0,\"source\":18},\"month\":{\"input\":12,\"score\":0,\"source\":1},\"year\":{\"input\":1999,\"score\":0,\"source\":1976}}"}],"X_average_score_max":18,"X_phone_score_max":-1,"X_name_score_max":"11","X_address_score_max":"60","X_dob_score_max":-1,"X_ssn_score_100_count":0,"X_phone_score_100_count":0,"source_phone":"+11236547890","source_name":"BENWYATT","source_dob":"1-18-1976","source_address":"123 MAIN ST NEW YORK NY 10001"}"}
{"@timestamp":"2025-08-04T11:33:05.154275+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"f4116ba0dd9e699f4e969fc6e664d38d","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Factories\PlaidValidationRules\PlaidRulesWithoutSSN::_lookupDecisionTable(Line: 197) - ====Looking up in to decision table==== []"}
{"@timestamp":"2025-08-04T11:33:05.181587+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"f4116ba0dd9e699f4e969fc6e664d38d","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Factories\PlaidValidationRules\PlaidRulesWithoutSSN::checkPlaidRules(Line: 115) - Checking up plaid rules. 2 record(s) found based on average score. []"}
{"@timestamp":"2025-08-04T11:33:05.181782+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"f4116ba0dd9e699f4e969fc6e664d38d","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Factories\PlaidValidationRules\PlaidRulesWithoutSSN::checkPlaidRules(Line: 135) - Checking up plaid rules. Death date found. []"}
{"@timestamp":"2025-08-04T11:33:05.208584+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"f4116ba0dd9e699f4e969fc6e664d38d","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Factories\PlaidValidationRules\PlaidRulesWithoutSSN::checkPlaidRules(Line: 143) - Final result ready. Returning final result. {"cognito_search_id":"idnsch_bRBpz9YBfWHTNG","session_id":"b1272264b2e9ea510cc04180143affad","input_name":"Ashi  Yogi","input_phone":"**********","input_address":"123 New york New York NY 12211","input_ssn":"","input_dob":"1999-12-01","deaths":[{"day":24,"month":2,"year":2015}],"average_scores":[18,7],"scores":[{"average_score":18,"phone_score":-1,"name_score":"11","address_score":-1,"ssn_score":-1,"birth_score":-1,"phone_components":"{\"number\":{\"input\":\"+***********\",\"score\":0,\"source\":\"+***********\"}}","name_components":"{\"first\":{\"input\":\"Ashi\",\"score\":0,\"source\":\"BEN\"},\"last\":{\"input\":\"Yogi\",\"score\":0,\"source\":\"WYATT\"},\"middle\":{\"input\":null,\"score\":null,\"source\":null}}","address_components":"{\"city\":{\"input\":\"New York\",\"score\":0,\"source\":\"SHERMAN OAKS\"},\"postal_code\":{\"input\":\"12211\",\"score\":0,\"source\":\"91423\"},\"street\":{\"input\":\"123 New york\",\"score\":0,\"source\":\"4620 WORTSER AVE APT 101\"},\"subdivision\":{\"input\":\"NY\",\"score\":0,\"source\":\"CA\"}}","ssn_components":"","birth_components":"{\"day\":{\"input\":1,\"score\":null,\"source\":null},\"month\":{\"input\":12,\"score\":null,\"source\":null},\"year\":{\"input\":1999,\"score\":0,\"source\":1974}}"},{"average_score":18,"phone_score":-1,"name_score":-1,"address_score":"60","ssn_score":-1,"birth_score":-1,"phone_components":"{\"number\":{\"input\":\"+***********\",\"score\":0,\"source\":\"+11122334455\"}}","name_components":"{\"first\":{\"input\":\"Ashi\",\"score\":0,\"source\":\"BEN\"},\"last\":{\"input\":\"Yogi\",\"score\":0,\"source\":\"WYATT\"},\"middle\":{\"input\":null,\"score\":null,\"source\":null}}","address_components":"{\"city\":{\"input\":\"New York\",\"score\":100,\"source\":\"NEW YORK\"},\"postal_code\":{\"input\":\"12211\",\"score\":0,\"source\":\"10001\"},\"street\":{\"input\":\"123 New york\",\"score\":53,\"source\":\"123 MAIN ST\"},\"subdivision\":{\"input\":\"NY\",\"score\":100,\"source\":\"NY\"}}","ssn_components":"","birth_components":"{\"day\":{\"input\":1,\"score\":null,\"source\":null},\"month\":{\"input\":12,\"score\":null,\"source\":null},\"year\":{\"input\":1999,\"score\":0,\"source\":1974}}"},{"average_score":7,"phone_score":-1,"name_score":-1,"address_score":-1,"ssn_score":-1,"birth_score":-1,"phone_components":"{\"number\":{\"input\":\"+***********\",\"score\":0,\"source\":\"+19876543212\"}}","name_components":"{\"first\":{\"input\":\"Ashi\",\"score\":0,\"source\":\"LESLIE\"},\"last\":{\"input\":\"Yogi\",\"score\":0,\"source\":\"KNOPE-WYATT\"},\"middle\":{\"input\":null,\"score\":null,\"source\":null}}","address_components":"{\"city\":{\"input\":\"New York\",\"score\":0,\"source\":\"EAGLETON\"},\"postal_code\":{\"input\":\"12211\",\"score\":0,\"source\":\"46002\"},\"street\":{\"input\":\"123 New york\",\"score\":0,\"source\":null},\"subdivision\":{\"input\":\"NY\",\"score\":0,\"source\":\"IN\"}}","ssn_components":"","birth_components":"{\"day\":{\"input\":1,\"score\":0,\"source\":18},\"month\":{\"input\":12,\"score\":0,\"source\":1},\"year\":{\"input\":1999,\"score\":0,\"source\":1975}}"},{"average_score":7,"phone_score":-1,"name_score":-1,"address_score":"26","ssn_score":-1,"birth_score":-1,"phone_components":"{\"number\":{\"input\":\"+***********\",\"score\":0,\"source\":\"+12345678909\"}}","name_components":"{\"first\":{\"input\":\"Ashi\",\"score\":0,\"source\":\"LESLIE\"},\"last\":{\"input\":\"Yogi\",\"score\":0,\"source\":\"KNOPE\"},\"middle\":{\"input\":null,\"score\":null,\"source\":\"BARBARA\"}}","address_components":"{\"city\":{\"input\":\"New York\",\"score\":0,\"source\":\"PAWNEE\"},\"postal_code\":{\"input\":\"12211\",\"score\":0,\"source\":\"46001\"},\"street\":{\"input\":\"123 New york\",\"score\":53,\"source\":\"123 MAIN ST\"},\"subdivision\":{\"input\":\"NY\",\"score\":0,\"source\":\"IN\"}}","ssn_components":"","birth_components":"{\"day\":{\"input\":1,\"score\":0,\"source\":18},\"month\":{\"input\":12,\"score\":0,\"source\":1},\"year\":{\"input\":1999,\"score\":0,\"source\":1976}}"},{"average_score":7,"phone_score":-1,"name_score":-1,"address_score":-1,"ssn_score":-1,"birth_score":-1,"phone_components":"{\"number\":{\"input\":\"+***********\",\"score\":0,\"source\":\"+11236547890\"}}","name_components":"{\"first\":{\"input\":\"Ashi\",\"score\":0,\"source\":\"LESLIE\"},\"last\":{\"input\":\"Yogi\",\"score\":0,\"source\":\"KNOPE\"},\"middle\":{\"input\":null,\"score\":null,\"source\":\"BARBARA\"}}","address_components":"{\"city\":{\"input\":\"New York\",\"score\":0,\"source\":\"PAWNEE\"},\"postal_code\":{\"input\":\"12211\",\"score\":0,\"source\":\"46001\"},\"street\":{\"input\":\"123 New york\",\"score\":53,\"source\":\"123 MAIN ST\"},\"subdivision\":{\"input\":\"NY\",\"score\":0,\"source\":\"IN\"}}","ssn_components":"","birth_components":"{\"day\":{\"input\":1,\"score\":0,\"source\":18},\"month\":{\"input\":12,\"score\":0,\"source\":1},\"year\":{\"input\":1999,\"score\":0,\"source\":1976}}"}],"X_average_score_max":18,"X_phone_score_max":-1,"X_name_score_max":"11","X_address_score_max":"60","X_dob_score_max":-1,"X_ssn_score_100_count":0,"X_phone_score_100_count":0,"source_phone":"+11236547890","source_name":"BENWYATT","source_dob":"1-18-1976","source_address":"123 MAIN ST NEW YORK NY 10001","outcome_status":"Manual Review","outcome_mustsubmitid":"Yes","outcome_reason":"Decision table matched on Rule ID: 1034. 2 record(s) found based on average score.Date of death found. Needs checking. ","outcome_ssnrequired":"Y"}"}
{"@timestamp":"2025-08-04T11:33:05.216694+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"f4116ba0dd9e699f4e969fc6e664d38d","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\RegistrationController::_getAssessment(Line: 663) - SSN required for identity validation. Please provide SSN. []"}
{"@timestamp":"2025-08-04T11:33:42.935623+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"0c58d6fc0733897d3888a15dac479513","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Middleware\RegistrationSessionMiddleware::handle(Line: 34) - Registration session is valid and redirecting to API. []"}
{"@timestamp":"2025-08-04T11:33:43.090138+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"0c58d6fc0733897d3888a15dac479513","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\RegistrationController::getAssessmentWithSSN(Line: 556) - Starting assessment of earlier search for session: 1754306923588 []"}
{"@timestamp":"2025-08-04T11:33:43.106138+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"0c58d6fc0733897d3888a15dac479513","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\RegistrationController::getAssessmentWithSSN(Line: 604) - Previous search result retrieved:  {"\u0000*\u0000connection":"mysql","\u0000*\u0000table":"validation_log_table","\u0000*\u0000primaryKey":"id","\u0000*\u0000keyType":"int","incrementing":false,"\u0000*\u0000with":[],"\u0000*\u0000withCount":[],"preventsLazyLoading":false,"\u0000*\u0000perPage":15,"exists":true,"wasRecentlyCreated":false,"\u0000*\u0000escapeWhenCastingToString":false,"\u0000*\u0000attributes":{"id":"261c80130fa4b58db42f8223a23d05e7","api":"/idv_classic/identity_searches","phone":"**********","session_id":"b1272264b2e9ea510cc04180143affad","first_name":"Ashi","middle_name":null,"last_name":"Yogi","date_of_birth":null,"ssn":null,"body":null,"response":"{\"data\":{\"attributes\":{\"birth\":{\"day\":1,\"month\":12,\"year\":1999},\"created_at\":\"2025-08-04T11:33:02Z\",\"name\":{\"first\":\"Ashi\",\"last\":\"Yogi\",\"middle\":null},\"phone\":{\"number\":\"+***********\"},\"us_address\":{\"city\":\"New York\",\"postal_code\":\"12211\",\"street\":\"123 New york\",\"subdivision\":\"NY\"}},\"id\":\"idnsch_bRBpz9YBfWHTNG\",\"relationships\":{\"identity_records\":{\"data\":[{\"id\":\"idnrcd_aNtRhzL6TtbbKr\",\"type\":\"identity_record\"},{\"id\":\"idnrcd_3ofUCwLEiPJsab\",\"type\":\"identity_record\"}]},\"profile\":{\"data\":{\"id\":\"prf_3H7xMg5CuPeqrF\",\"type\":\"profile\"}}},\"type\":\"identity_search\"},\"included\":[{\"attributes\":[],\"id\":\"idnrcd_aNtRhzL6TtbbKr\",\"relationships\":{\"addresses\":{\"data\":[{\"id\":\"usaddr_6snLCGkxUTqU17\",\"type\":\"us_address\"},{\"id\":\"usaddr_4Q2pvzuUdzVXF8\",\"type\":\"us_address\"}]},\"births\":{\"data\":[{\"id\":\"bth_egfTsCKKLBowi5\",\"type\":\"birth\"},{\"id\":\"bth_8ngnEUy7aVwLSu\",\"type\":\"birth\"}]},\"deaths\":{\"data\":[{\"id\":\"dth_eB5Bb3GQARG5wX\",\"type\":\"death\"}]},\"names\":{\"data\":[{\"id\":\"nam_cTKEfZErBSWxq3\",\"type\":\"name\"},{\"id\":\"nam_2Xow6mvb2NnffZ\",\"type\":\"name\"}]},\"phones\":{\"data\":[{\"id\":\"phn_eyzEio7NRAckwE\",\"type\":\"phone\"},{\"id\":\"phn_9s6eW9wKu4byBr\",\"type\":\"phone\"},{\"id\":\"phn_8CA7G3MCQHWMVM\",\"type\":\"phone\"}]},\"ssns\":{\"data\":[{\"id\":\"ssn_ct2cJySkN2fykC\",\"type\":\"ssn\"},{\"id\":\"ssn_bjYu9fPweh2msH\",\"type\":\"ssn\"}]}},\"type\":\"identity_record\"},{\"attributes\":[],\"id\":\"idnrcd_3ofUCwLEiPJsab\",\"relationships\":{\"addresses\":{\"data\":[{\"id\":\"usaddr_aM8bzBPEKfxF5F\",\"type\":\"us_address\"},{\"id\":\"usaddr_4KRZ8RmN2o3oGN\",\"type\":\"us_address\"}]},\"births\":{\"data\":[{\"id\":\"bth_bTPVCajeUU419s\",\"type\":\"birth\"}]},\"deaths\":{\"data\":[]},\"names\":{\"data\":[{\"id\":\"nam_2iiB9YWFuzVuyu\",\"type\":\"name\"}]},\"phones\":{\"data\":[{\"id\":\"phn_dCG9oLTyKHCpYZ\",\"type\":\"phone\"},{\"id\":\"phn_2MA3X88vKxhTw9\",\"type\":\"phone\"}]},\"ssns\":{\"data\":[{\"id\":\"ssn_74K8uDUmFGZqcV\",\"type\":\"ssn\"}]}},\"type\":\"identity_record\"},{\"attributes\":{\"city\":\"PAWNEE\",\"country_code\":\"US\",\"postal_code\":\"46001\",\"street\":\"123 MAIN ST\",\"subdivision\":\"IN\",\"usage\":{\"earliest\":{\"month\":4,\"year\":2009},\"latest\":{\"month\":2,\"year\":2015}}},\"id\":\"usaddr_6snLCGkxUTqU17\",\"type\":\"us_address\"},{\"attributes\":{\"city\":\"EAGLETON\",\"country_code\":\"US\",\"postal_code\":\"46002\",\"street\":null,\"subdivision\":\"IN\",\"usage\":null},\"id\":\"usaddr_4Q2pvzuUdzVXF8\",\"type\":\"us_address\"},{\"attributes\":{\"day\":18,\"month\":1,\"year\":1975},\"id\":\"bth_egfTsCKKLBowi5\",\"type\":\"birth\"},{\"attributes\":{\"day\":18,\"month\":1,\"year\":1976},\"id\":\"bth_8ngnEUy7aVwLSu\",\"type\":\"birth\"},{\"attributes\":{\"day\":24,\"month\":2,\"year\":2015},\"id\":\"dth_eB5Bb3GQARG5wX\",\"type\":\"death\"},{\"attributes\":{\"first\":\"LESLIE\",\"last\":\"KNOPE\",\"middle\":\"BARBARA\"},\"id\":\"nam_cTKEfZErBSWxq3\",\"type\":\"name\"},{\"attributes\":{\"first\":\"LESLIE\",\"last\":\"KNOPE-WYATT\",\"middle\":null},\"id\":\"nam_2Xow6mvb2NnffZ\",\"type\":\"name\"},{\"attributes\":{\"number\":\"+11236547890\",\"usage\":{\"earliest\":{\"month\":1,\"year\":2017},\"latest\":{\"month\":10,\"year\":2022}}},\"id\":\"phn_eyzEio7NRAckwE\",\"type\":\"phone\"},{\"attributes\":{\"number\":\"+12345678909\",\"usage\":{\"earliest\":{\"month\":1,\"year\":2017},\"latest\":{\"month\":10,\"year\":2022}}},\"id\":\"phn_9s6eW9wKu4byBr\",\"type\":\"phone\"},{\"attributes\":{\"number\":\"+19876543212\",\"usage\":{\"earliest\":{\"month\":12,\"year\":2006},\"latest\":{\"month\":1,\"year\":2017}}},\"id\":\"phn_8CA7G3MCQHWMVM\",\"type\":\"phone\"},{\"attributes\":{\"number\":\"MTIzNDU2Nzg4\",\"group\":\"MTIz\",\"area\":\"NDU=\",\"serial\":\"Njc4OA==\"},\"id\":\"ssn_ct2cJySkN2fykC\",\"type\":\"ssn\"},{\"attributes\":{\"number\":\"MTIzNDU2Nzg5\",\"group\":\"MTIz\",\"area\":\"NDU=\",\"serial\":\"Njc4OQ==\"},\"id\":\"ssn_bjYu9fPweh2msH\",\"type\":\"ssn\"},{\"attributes\":{\"city\":\"NEW YORK\",\"country_code\":\"US\",\"postal_code\":\"10001\",\"street\":\"123 MAIN ST\",\"subdivision\":\"NY\",\"usage\":null},\"id\":\"usaddr_aM8bzBPEKfxF5F\",\"type\":\"us_address\"},{\"attributes\":{\"city\":\"SHERMAN OAKS\",\"country_code\":\"US\",\"postal_code\":\"91423\",\"street\":\"4620 WORTSER AVE APT 101\",\"subdivision\":\"CA\",\"usage\":null},\"id\":\"usaddr_4KRZ8RmN2o3oGN\",\"type\":\"us_address\"},{\"attributes\":{\"day\":null,\"month\":null,\"year\":1974},\"id\":\"bth_bTPVCajeUU419s\",\"type\":\"birth\"},{\"attributes\":{\"first\":\"BEN\",\"last\":\"WYATT\",\"middle\":null},\"id\":\"nam_2iiB9YWFuzVuyu\",\"type\":\"name\"},{\"attributes\":{\"number\":\"+11122334455\",\"usage\":{\"earliest\":{\"month\":12,\"year\":1995},\"latest\":{\"month\":1,\"year\":2022}}},\"id\":\"phn_dCG9oLTyKHCpYZ\",\"type\":\"phone\"},{\"attributes\":{\"number\":\"+***********\",\"usage\":{\"earliest\":{\"month\":12,\"year\":1995},\"latest\":{\"month\":1,\"year\":2022}}},\"id\":\"phn_2MA3X88vKxhTw9\",\"type\":\"phone\"},{\"attributes\":{\"number\":\"MTExMjIzMzMz\",\"group\":\"MTEx\",\"area\":\"MjI=\",\"serial\":\"MzMzMw==\"},\"id\":\"ssn_74K8uDUmFGZqcV\",\"type\":\"ssn\"}],\"request_id\":\"K8zCcR6VXGvLE7n\"}","type":"plaid","account_id":null,"response_code":null,"created_at":"2025-08-04 11:33:03","updated_at":"2025-08-04 11:33:03"},"\u0000*\u0000original":{"id":"261c80130fa4b58db42f8223a23d05e7","api":"/idv_classic/identity_searches","phone":"**********","session_id":"b1272264b2e9ea510cc04180143affad","first_name":"Ashi","middle_name":null,"last_name":"Yogi","date_of_birth":null,"ssn":null,"body":null,"response":"{\"data\":{\"attributes\":{\"birth\":{\"day\":1,\"month\":12,\"year\":1999},\"created_at\":\"2025-08-04T11:33:02Z\",\"name\":{\"first\":\"Ashi\",\"last\":\"Yogi\",\"middle\":null},\"phone\":{\"number\":\"+***********\"},\"us_address\":{\"city\":\"New York\",\"postal_code\":\"12211\",\"street\":\"123 New york\",\"subdivision\":\"NY\"}},\"id\":\"idnsch_bRBpz9YBfWHTNG\",\"relationships\":{\"identity_records\":{\"data\":[{\"id\":\"idnrcd_aNtRhzL6TtbbKr\",\"type\":\"identity_record\"},{\"id\":\"idnrcd_3ofUCwLEiPJsab\",\"type\":\"identity_record\"}]},\"profile\":{\"data\":{\"id\":\"prf_3H7xMg5CuPeqrF\",\"type\":\"profile\"}}},\"type\":\"identity_search\"},\"included\":[{\"attributes\":[],\"id\":\"idnrcd_aNtRhzL6TtbbKr\",\"relationships\":{\"addresses\":{\"data\":[{\"id\":\"usaddr_6snLCGkxUTqU17\",\"type\":\"us_address\"},{\"id\":\"usaddr_4Q2pvzuUdzVXF8\",\"type\":\"us_address\"}]},\"births\":{\"data\":[{\"id\":\"bth_egfTsCKKLBowi5\",\"type\":\"birth\"},{\"id\":\"bth_8ngnEUy7aVwLSu\",\"type\":\"birth\"}]},\"deaths\":{\"data\":[{\"id\":\"dth_eB5Bb3GQARG5wX\",\"type\":\"death\"}]},\"names\":{\"data\":[{\"id\":\"nam_cTKEfZErBSWxq3\",\"type\":\"name\"},{\"id\":\"nam_2Xow6mvb2NnffZ\",\"type\":\"name\"}]},\"phones\":{\"data\":[{\"id\":\"phn_eyzEio7NRAckwE\",\"type\":\"phone\"},{\"id\":\"phn_9s6eW9wKu4byBr\",\"type\":\"phone\"},{\"id\":\"phn_8CA7G3MCQHWMVM\",\"type\":\"phone\"}]},\"ssns\":{\"data\":[{\"id\":\"ssn_ct2cJySkN2fykC\",\"type\":\"ssn\"},{\"id\":\"ssn_bjYu9fPweh2msH\",\"type\":\"ssn\"}]}},\"type\":\"identity_record\"},{\"attributes\":[],\"id\":\"idnrcd_3ofUCwLEiPJsab\",\"relationships\":{\"addresses\":{\"data\":[{\"id\":\"usaddr_aM8bzBPEKfxF5F\",\"type\":\"us_address\"},{\"id\":\"usaddr_4KRZ8RmN2o3oGN\",\"type\":\"us_address\"}]},\"births\":{\"data\":[{\"id\":\"bth_bTPVCajeUU419s\",\"type\":\"birth\"}]},\"deaths\":{\"data\":[]},\"names\":{\"data\":[{\"id\":\"nam_2iiB9YWFuzVuyu\",\"type\":\"name\"}]},\"phones\":{\"data\":[{\"id\":\"phn_dCG9oLTyKHCpYZ\",\"type\":\"phone\"},{\"id\":\"phn_2MA3X88vKxhTw9\",\"type\":\"phone\"}]},\"ssns\":{\"data\":[{\"id\":\"ssn_74K8uDUmFGZqcV\",\"type\":\"ssn\"}]}},\"type\":\"identity_record\"},{\"attributes\":{\"city\":\"PAWNEE\",\"country_code\":\"US\",\"postal_code\":\"46001\",\"street\":\"123 MAIN ST\",\"subdivision\":\"IN\",\"usage\":{\"earliest\":{\"month\":4,\"year\":2009},\"latest\":{\"month\":2,\"year\":2015}}},\"id\":\"usaddr_6snLCGkxUTqU17\",\"type\":\"us_address\"},{\"attributes\":{\"city\":\"EAGLETON\",\"country_code\":\"US\",\"postal_code\":\"46002\",\"street\":null,\"subdivision\":\"IN\",\"usage\":null},\"id\":\"usaddr_4Q2pvzuUdzVXF8\",\"type\":\"us_address\"},{\"attributes\":{\"day\":18,\"month\":1,\"year\":1975},\"id\":\"bth_egfTsCKKLBowi5\",\"type\":\"birth\"},{\"attributes\":{\"day\":18,\"month\":1,\"year\":1976},\"id\":\"bth_8ngnEUy7aVwLSu\",\"type\":\"birth\"},{\"attributes\":{\"day\":24,\"month\":2,\"year\":2015},\"id\":\"dth_eB5Bb3GQARG5wX\",\"type\":\"death\"},{\"attributes\":{\"first\":\"LESLIE\",\"last\":\"KNOPE\",\"middle\":\"BARBARA\"},\"id\":\"nam_cTKEfZErBSWxq3\",\"type\":\"name\"},{\"attributes\":{\"first\":\"LESLIE\",\"last\":\"KNOPE-WYATT\",\"middle\":null},\"id\":\"nam_2Xow6mvb2NnffZ\",\"type\":\"name\"},{\"attributes\":{\"number\":\"+11236547890\",\"usage\":{\"earliest\":{\"month\":1,\"year\":2017},\"latest\":{\"month\":10,\"year\":2022}}},\"id\":\"phn_eyzEio7NRAckwE\",\"type\":\"phone\"},{\"attributes\":{\"number\":\"+12345678909\",\"usage\":{\"earliest\":{\"month\":1,\"year\":2017},\"latest\":{\"month\":10,\"year\":2022}}},\"id\":\"phn_9s6eW9wKu4byBr\",\"type\":\"phone\"},{\"attributes\":{\"number\":\"+19876543212\",\"usage\":{\"earliest\":{\"month\":12,\"year\":2006},\"latest\":{\"month\":1,\"year\":2017}}},\"id\":\"phn_8CA7G3MCQHWMVM\",\"type\":\"phone\"},{\"attributes\":{\"number\":\"MTIzNDU2Nzg4\",\"group\":\"MTIz\",\"area\":\"NDU=\",\"serial\":\"Njc4OA==\"},\"id\":\"ssn_ct2cJySkN2fykC\",\"type\":\"ssn\"},{\"attributes\":{\"number\":\"MTIzNDU2Nzg5\",\"group\":\"MTIz\",\"area\":\"NDU=\",\"serial\":\"Njc4OQ==\"},\"id\":\"ssn_bjYu9fPweh2msH\",\"type\":\"ssn\"},{\"attributes\":{\"city\":\"NEW YORK\",\"country_code\":\"US\",\"postal_code\":\"10001\",\"street\":\"123 MAIN ST\",\"subdivision\":\"NY\",\"usage\":null},\"id\":\"usaddr_aM8bzBPEKfxF5F\",\"type\":\"us_address\"},{\"attributes\":{\"city\":\"SHERMAN OAKS\",\"country_code\":\"US\",\"postal_code\":\"91423\",\"street\":\"4620 WORTSER AVE APT 101\",\"subdivision\":\"CA\",\"usage\":null},\"id\":\"usaddr_4KRZ8RmN2o3oGN\",\"type\":\"us_address\"},{\"attributes\":{\"day\":null,\"month\":null,\"year\":1974},\"id\":\"bth_bTPVCajeUU419s\",\"type\":\"birth\"},{\"attributes\":{\"first\":\"BEN\",\"last\":\"WYATT\",\"middle\":null},\"id\":\"nam_2iiB9YWFuzVuyu\",\"type\":\"name\"},{\"attributes\":{\"number\":\"+11122334455\",\"usage\":{\"earliest\":{\"month\":12,\"year\":1995},\"latest\":{\"month\":1,\"year\":2022}}},\"id\":\"phn_dCG9oLTyKHCpYZ\",\"type\":\"phone\"},{\"attributes\":{\"number\":\"+***********\",\"usage\":{\"earliest\":{\"month\":12,\"year\":1995},\"latest\":{\"month\":1,\"year\":2022}}},\"id\":\"phn_2MA3X88vKxhTw9\",\"type\":\"phone\"},{\"attributes\":{\"number\":\"MTExMjIzMzMz\",\"group\":\"MTEx\",\"area\":\"MjI=\",\"serial\":\"MzMzMw==\"},\"id\":\"ssn_74K8uDUmFGZqcV\",\"type\":\"ssn\"}],\"request_id\":\"K8zCcR6VXGvLE7n\"}","type":"plaid","account_id":null,"response_code":null,"created_at":"2025-08-04 11:33:03","updated_at":"2025-08-04 11:33:03"},"\u0000*\u0000changes":[],"\u0000*\u0000casts":[],"\u0000*\u0000classCastCache":[],"\u0000*\u0000attributeCastCache":[],"\u0000*\u0000dateFormat":null,"\u0000*\u0000appends":[],"\u0000*\u0000dispatchesEvents":[],"\u0000*\u0000observables":[],"\u0000*\u0000relations":[],"\u0000*\u0000touches":[],"timestamps":true,"usesUniqueIds":false,"\u0000*\u0000hidden":[],"\u0000*\u0000visible":[],"\u0000*\u0000fillable":["api","phone","first_name","last_name","date_of_birth","ssn","response","type","session_id","account_id","response_code"],"\u0000*\u0000guarded":["*"],"\u0000*\u0000authPasswordName":"password","\u0000*\u0000rememberTokenName":"remember_token"}"}
{"@timestamp":"2025-08-04T11:33:45.291963+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"0c58d6fc0733897d3888a15dac479513","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Clients\PlaidHttpClient::identityAssessment(Line: 124) - Plaid assessment api returned response : {"data":{"attributes":{"birth":{"day":1,"month":12,"year":1999},"created_at":"2025-08-04T11:33:44Z","name":{"first":"Ashi","last":"Yogi","middle":null},"phone":{"number":"+***********"},"ssn":{"area":null,"group":null,"serial":"MTExMQ=="},"us_address":{"city":"New York","postal_code":"12211","street":"123 New york","subdivision":"NY"}},"id":"idnasm_9rMrrDsU8xboy5","relationships":{"identity_record_comparisons":{"data":[{"id":"idrcmp_7UKUrnBM6imBEx","type":"identity_record_comparison"},{"id":"idrcmp_egRkBKxwaDtBU1","type":"identity_record_comparison"}]},"identity_search":{"data":{"id":"idnsch_bRBpz9YBfWHTNG","type":"identity_search"}}},"type":"identity_assessment"},"included":[{"attributes":{"score":14},"id":"idrcmp_7UKUrnBM6imBEx","relationships":{"address_comparisons":{"data":[{"id":"usacmp_4jGxftwPcspA81","type":"us_address_comparison"},{"id":"usacmp_9PzaFDW6ZoTWki","type":"us_address_comparison"}]},"birth_comparisons":{"data":[{"id":"bthcmp_8t4LRJeaaggpLC","type":"birth_comparison"}]},"identity_record":{"data":{"id":"idnrcd_3ofUCwLEiPJsab","type":"identity_record"}},"name_comparisons":{"data":[{"id":"namcmp_aTJzzgZtHuqrgi","type":"name_comparison"}]},"phone_comparisons":{"data":[{"id":"phncmp_6moaPwCodrPbtK","type":"phone_comparison"},{"id":"phncmp_b8jGtjqi29iVU2","type":"phone_comparison"}]},"ssn_comparisons":{"data":[{"id":"ssncmp_7RgGiA1TE1PTR2","type":"ssn_comparison"}]}},"type":"identity_record_comparison"},{"attributes":{"score":5},"id":"idrcmp_egRkBKxwaDtBU1","relationships":{"address_comparisons":{"data":[{"id":"usacmp_5EXjcK91zksSRR","type":"us_address_comparison"},{"id":"usacmp_1PGc3pqq4YAm1W","type":"us_address_comparison"}]},"birth_comparisons":{"data":[{"id":"bthcmp_9VVbKvQccvCdH2","type":"birth_comparison"},{"id":"bthcmp_eXCADcUPpZ31ED","type":"birth_comparison"}]},"identity_record":{"data":{"id":"idnrcd_aNtRhzL6TtbbKr","type":"identity_record"}},"name_comparisons":{"data":[{"id":"namcmp_cs7z4kLx8XRDyH","type":"name_comparison"},{"id":"namcmp_1iCyvMp4GZMcft","type":"name_comparison"}]},"phone_comparisons":{"data":[{"id":"phncmp_bAinkg1qwYFsDy","type":"phone_comparison"},{"id":"phncmp_ccC7uHDLs1Yueg","type":"phone_comparison"},{"id":"phncmp_2mr1GcS3msLdrk","type":"phone_comparison"}]},"ssn_comparisons":{"data":[{"id":"ssncmp_1tXqxKEugjNrve","type":"ssn_comparison"},{"id":"ssncmp_chDkTGx1orDxMD","type":"ssn_comparison"}]}},"type":"identity_record_comparison"},{"attributes":{"components":{"city":{"input":"New York","score":0,"source":"SHERMAN OAKS"},"postal_code":{"input":"12211","score":0,"source":"91423"},"street":{"input":"123 New york","score":0,"source":"4620 WORTSER AVE APT 101"},"subdivision":{"input":"NY","score":0,"source":"CA"}},"score":0},"id":"usacmp_4jGxftwPcspA81","relationships":{"source_record":{"data":{"id":"usaddr_4KRZ8RmN2o3oGN","type":"us_address"}}},"type":"us_address_comparison"},{"attributes":{"components":{"city":{"input":"New York","score":100,"source":"NEW YORK"},"postal_code":{"input":"12211","score":0,"source":"10001"},"street":{"input":"123 New york","score":53,"source":"123 MAIN ST"},"subdivision":{"input":"NY","score":100,"source":"NY"}},"score":60},"id":"usacmp_9PzaFDW6ZoTWki","relationships":{"source_record":{"data":{"id":"usaddr_aM8bzBPEKfxF5F","type":"us_address"}}},"type":"us_address_comparison"},{"attributes":{"components":{"city":{"input":"New York","score":0,"source":"EAGLETON"},"postal_code":{"input":"12211","score":0,"source":"46002"},"street":{"input":"123 New york","score":0,"source":null},"subdivision":{"input":"NY","score":0,"source":"IN"}},"score":0},"id":"usacmp_5EXjcK91zksSRR","relationships":{"source_record":{"data":{"id":"usaddr_4Q2pvzuUdzVXF8","type":"us_address"}}},"type":"us_address_comparison"},{"attributes":{"components":{"city":{"input":"New York","score":0,"source":"PAWNEE"},"postal_code":{"input":"12211","score":0,"source":"46001"},"street":{"input":"123 New york","score":53,"source":"123 MAIN ST"},"subdivision":{"input":"NY","score":0,"source":"IN"}},"score":26},"id":"usacmp_1PGc3pqq4YAm1W","relationships":{"source_record":{"data":{"id":"usaddr_6snLCGkxUTqU17","type":"us_address"}}},"type":"us_address_comparison"},{"attributes":{"components":{"day":{"input":1,"score":null,"source":null},"month":{"input":12,"score":null,"source":null},"year":{"input":1999,"score":0,"source":1974}},"score":0},"id":"bthcmp_8t4LRJeaaggpLC","relationships":{"source_record":{"data":{"id":"bth_bTPVCajeUU419s","type":"birth"}}},"type":"birth_comparison"},{"attributes":{"components":{"day":{"input":1,"score":0,"source":18},"month":{"input":12,"score":0,"source":1},"year":{"input":1999,"score":0,"source":1975}},"score":0},"id":"bthcmp_9VVbKvQccvCdH2","relationships":{"source_record":{"data":{"id":"bth_egfTsCKKLBowi5","type":"birth"}}},"type":"birth_comparison"},{"attributes":{"components":{"day":{"input":1,"score":0,"source":18},"month":{"input":12,"score":0,"source":1},"year":{"input":1999,"score":0,"source":1976}},"score":0},"id":"bthcmp_eXCADcUPpZ31ED","relationships":{"source_record":{"data":{"id":"bth_8ngnEUy7aVwLSu","type":"birth"}}},"type":"birth_comparison"},{"attributes":{"components":{"number":{"input":"+***********","score":0,"source":"+***********"}},"score":0},"id":"phncmp_6moaPwCodrPbtK","relationships":{"source_record":{"data":{"id":"phn_2MA3X88vKxhTw9","type":"phone"}}},"type":"phone_comparison"},{"attributes":{"components":{"number":{"input":"+***********","score":0,"source":"+11122334455"}},"score":0},"id":"phncmp_b8jGtjqi29iVU2","relationships":{"source_record":{"data":{"id":"phn_dCG9oLTyKHCpYZ","type":"phone"}}},"type":"phone_comparison"},{"attributes":{"components":{"number":{"input":"+***********","score":0,"source":"+19876543212"}},"score":0},"id":"phncmp_bAinkg1qwYFsDy","relationships":{"source_record":{"data":{"id":"phn_8CA7G3MCQHWMVM","type":"phone"}}},"type":"phone_comparison"},{"attributes":{"components":{"number":{"input":"+***********","score":0,"source":"+12345678909"}},"score":0},"id":"phncmp_ccC7uHDLs1Yueg","relationships":{"source_record":{"data":{"id":"phn_9s6eW9wKu4byBr","type":"phone"}}},"type":"phone_comparison"},{"attributes":{"components":{"number":{"input":"+***********","score":0,"source":"+11236547890"}},"score":0},"id":"phncmp_2mr1GcS3msLdrk","relationships":{"source_record":{"data":{"id":"phn_eyzEio7NRAckwE","type":"phone"}}},"type":"phone_comparison"},{"attributes":{"components":{"first":{"input":"Ashi","score":0,"source":"BEN"},"last":{"input":"Yogi","score":0,"source":"WYATT"},"middle":{"input":null,"score":null,"source":null}},"score":11},"id":"namcmp_aTJzzgZtHuqrgi","relationships":{"source_record":{"data":{"id":"nam_2iiB9YWFuzVuyu","type":"name"}}},"type":"name_comparison"},{"attributes":{"components":{"first":{"input":"Ashi","score":0,"source":"LESLIE"},"last":{"input":"Yogi","score":0,"source":"KNOPE-WYATT"},"middle":{"input":null,"score":null,"source":null}},"score":0},"id":"namcmp_cs7z4kLx8XRDyH","relationships":{"source_record":{"data":{"id":"nam_2Xow6mvb2NnffZ","type":"name"}}},"type":"name_comparison"},{"attributes":{"components":{"first":{"input":"Ashi","score":0,"source":"LESLIE"},"last":{"input":"Yogi","score":0,"source":"KNOPE"},"middle":{"input":null,"score":null,"source":"BARBARA"}},"score":0},"id":"namcmp_1iCyvMp4GZMcft","relationships":{"source_record":{"data":{"id":"nam_cTKEfZErBSWxq3","type":"name"}}},"type":"name_comparison"},{"attributes":{"components":{"area":{"input":null,"score":null,"source":"MTEx"},"group":{"input":null,"score":null,"source":"MjI="},"serial":{"input":"MTExMQ==","score":0,"source":"MzMzMw=="}},"score":0},"id":"ssncmp_7RgGiA1TE1PTR2","relationships":{"source_record":{"data":{"id":"ssn_74K8uDUmFGZqcV","type":"ssn"}}},"type":"ssn_comparison"},{"attributes":{"components":{"area":{"input":null,"score":null,"source":"MTIz"},"group":{"input":null,"score":null,"source":"NDU="},"serial":{"input":"MTExMQ==","score":0,"source":"Njc4OA=="}},"score":0},"id":"ssncmp_1tXqxKEugjNrve","relationships":{"source_record":{"data":{"id":"ssn_ct2cJySkN2fykC","type":"ssn"}}},"type":"ssn_comparison"},{"attributes":{"components":{"area":{"input":null,"score":null,"source":"MTIz"},"group":{"input":null,"score":null,"source":"NDU="},"serial":{"input":"MTExMQ==","score":0,"source":"Njc4OQ=="}},"score":0},"id":"ssncmp_chDkTGx1orDxMD","relationships":{"source_record":{"data":{"id":"ssn_bjYu9fPweh2msH","type":"ssn"}}},"type":"ssn_comparison"}],"request_id":"UFZPJ3DIUKRBL6R"} []"}
{"@timestamp":"2025-08-04T11:33:45.316452+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"0c58d6fc0733897d3888a15dac479513","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"saveCognitoResponsePostingHistory(Line: 2300) - Save Cognito response posting history for phone no: ********** []"}
{"@timestamp":"2025-08-04T11:33:46.485105+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"0c58d6fc0733897d3888a15dac479513","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"Topic: network-consumer-cognito-data posted to kafka returned response: {"error_code":200,"cluster_id":"lkc-577jyz","topic_name":"network-consumer-cognito-data","partition_id":0,"offset":4854,"timestamp":"2025-08-04T11:33:46.353Z","value":{"type":"JSON","size":13103}}  []"}
{"@timestamp":"2025-08-04T11:33:46.489815+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"0c58d6fc0733897d3888a15dac479513","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Factories\PlaidValidationRules\PlaidRulesWithSSN::enrichPlaidData(Line: 34) - ====STARTING COGNITO RULE ENGINE WITH SSN AS INPUT==== []"}
{"@timestamp":"2025-08-04T11:33:46.490030+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"0c58d6fc0733897d3888a15dac479513","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Factories\PlaidValidationRules\PlaidRulesWithSSN::_lookupDecisionTable(Line: 187) - ====Looking up in to decision table==== []"}
{"@timestamp":"2025-08-04T11:33:46.494616+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"0c58d6fc0733897d3888a15dac479513","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Factories\PlaidValidationRules\PlaidRulesWithSSN::checkPlaidRules(Line: 130) - Checking up plaid rules. 2 record(s) found based on average score. []"}
{"@timestamp":"2025-08-04T11:33:46.494747+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"0c58d6fc0733897d3888a15dac479513","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Factories\PlaidValidationRules\PlaidRulesWithSSN::checkPlaidRules(Line: 149) - Checking up plaid rules. Death date found. []"}
{"@timestamp":"2025-08-04T11:33:46.494791+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"0c58d6fc0733897d3888a15dac479513","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Factories\PlaidValidationRules\PlaidRulesWithSSN::checkPlaidRules(Line: 156) - Checking if plaid returned empty dataset []"}
{"@timestamp":"2025-08-04T11:33:46.507552+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"0c58d6fc0733897d3888a15dac479513","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Factories\PlaidValidationRules\PlaidRulesWithSSN::checkPlaidRules(Line: 181) - Final result ready. Returning final result. {"cognito_search_id":"idnsch_bRBpz9YBfWHTNG","session_id":"b1272264b2e9ea510cc04180143affad","input_name":"Ashi  Yogi","input_phone":"**********","input_address":"123 New york New York NY 12211","input_ssn":"MTExMQ==","input_dob":"1999-12-01","deaths":[{"day":24,"month":2,"year":2015}],"average_scores":[14,5],"scores":[{"average_score":14,"phone_score":-1,"name_score":"11","address_score":-1,"ssn_score":-1,"birth_score":-1,"phone_components":"{\"number\":{\"input\":\"+***********\",\"score\":0,\"source\":\"+***********\"}}","name_components":"{\"first\":{\"input\":\"Ashi\",\"score\":0,\"source\":\"BEN\"},\"last\":{\"input\":\"Yogi\",\"score\":0,\"source\":\"WYATT\"},\"middle\":{\"input\":null,\"score\":null,\"source\":null}}","address_components":"{\"city\":{\"input\":\"New York\",\"score\":0,\"source\":\"SHERMAN OAKS\"},\"postal_code\":{\"input\":\"12211\",\"score\":0,\"source\":\"91423\"},\"street\":{\"input\":\"123 New york\",\"score\":0,\"source\":\"4620 WORTSER AVE APT 101\"},\"subdivision\":{\"input\":\"NY\",\"score\":0,\"source\":\"CA\"}}","ssn_components":"{\"area\":{\"input\":null,\"score\":null,\"source\":\"MTEx\"},\"group\":{\"input\":null,\"score\":null,\"source\":\"MjI=\"},\"serial\":{\"input\":\"MTExMQ==\",\"score\":0,\"source\":\"MzMzMw==\"}}","birth_components":"{\"day\":{\"input\":1,\"score\":null,\"source\":null},\"month\":{\"input\":12,\"score\":null,\"source\":null},\"year\":{\"input\":1999,\"score\":0,\"source\":1974}}"},{"average_score":14,"phone_score":-1,"name_score":-1,"address_score":"60","ssn_score":-1,"birth_score":-1,"phone_components":"{\"number\":{\"input\":\"+***********\",\"score\":0,\"source\":\"+11122334455\"}}","name_components":"{\"first\":{\"input\":\"Ashi\",\"score\":0,\"source\":\"BEN\"},\"last\":{\"input\":\"Yogi\",\"score\":0,\"source\":\"WYATT\"},\"middle\":{\"input\":null,\"score\":null,\"source\":null}}","address_components":"{\"city\":{\"input\":\"New York\",\"score\":100,\"source\":\"NEW YORK\"},\"postal_code\":{\"input\":\"12211\",\"score\":0,\"source\":\"10001\"},\"street\":{\"input\":\"123 New york\",\"score\":53,\"source\":\"123 MAIN ST\"},\"subdivision\":{\"input\":\"NY\",\"score\":100,\"source\":\"NY\"}}","ssn_components":"{\"area\":{\"input\":null,\"score\":null,\"source\":\"MTEx\"},\"group\":{\"input\":null,\"score\":null,\"source\":\"MjI=\"},\"serial\":{\"input\":\"MTExMQ==\",\"score\":0,\"source\":\"MzMzMw==\"}}","birth_components":"{\"day\":{\"input\":1,\"score\":null,\"source\":null},\"month\":{\"input\":12,\"score\":null,\"source\":null},\"year\":{\"input\":1999,\"score\":0,\"source\":1974}}"},{"average_score":5,"phone_score":-1,"name_score":-1,"address_score":-1,"ssn_score":-1,"birth_score":-1,"phone_components":"{\"number\":{\"input\":\"+***********\",\"score\":0,\"source\":\"+19876543212\"}}","name_components":"{\"first\":{\"input\":\"Ashi\",\"score\":0,\"source\":\"LESLIE\"},\"last\":{\"input\":\"Yogi\",\"score\":0,\"source\":\"KNOPE-WYATT\"},\"middle\":{\"input\":null,\"score\":null,\"source\":null}}","address_components":"{\"city\":{\"input\":\"New York\",\"score\":0,\"source\":\"EAGLETON\"},\"postal_code\":{\"input\":\"12211\",\"score\":0,\"source\":\"46002\"},\"street\":{\"input\":\"123 New york\",\"score\":0,\"source\":null},\"subdivision\":{\"input\":\"NY\",\"score\":0,\"source\":\"IN\"}}","ssn_components":"{\"area\":{\"input\":null,\"score\":null,\"source\":\"MTIz\"},\"group\":{\"input\":null,\"score\":null,\"source\":\"NDU=\"},\"serial\":{\"input\":\"MTExMQ==\",\"score\":0,\"source\":\"Njc4OA==\"}}","birth_components":"{\"day\":{\"input\":1,\"score\":0,\"source\":18},\"month\":{\"input\":12,\"score\":0,\"source\":1},\"year\":{\"input\":1999,\"score\":0,\"source\":1975}}"},{"average_score":5,"phone_score":-1,"name_score":-1,"address_score":"26","ssn_score":-1,"birth_score":-1,"phone_components":"{\"number\":{\"input\":\"+***********\",\"score\":0,\"source\":\"+12345678909\"}}","name_components":"{\"first\":{\"input\":\"Ashi\",\"score\":0,\"source\":\"LESLIE\"},\"last\":{\"input\":\"Yogi\",\"score\":0,\"source\":\"KNOPE\"},\"middle\":{\"input\":null,\"score\":null,\"source\":\"BARBARA\"}}","address_components":"{\"city\":{\"input\":\"New York\",\"score\":0,\"source\":\"PAWNEE\"},\"postal_code\":{\"input\":\"12211\",\"score\":0,\"source\":\"46001\"},\"street\":{\"input\":\"123 New york\",\"score\":53,\"source\":\"123 MAIN ST\"},\"subdivision\":{\"input\":\"NY\",\"score\":0,\"source\":\"IN\"}}","ssn_components":"{\"area\":{\"input\":null,\"score\":null,\"source\":\"MTIz\"},\"group\":{\"input\":null,\"score\":null,\"source\":\"NDU=\"},\"serial\":{\"input\":\"MTExMQ==\",\"score\":0,\"source\":\"Njc4OQ==\"}}","birth_components":"{\"day\":{\"input\":1,\"score\":0,\"source\":18},\"month\":{\"input\":12,\"score\":0,\"source\":1},\"year\":{\"input\":1999,\"score\":0,\"source\":1976}}"},{"average_score":5,"phone_score":-1,"name_score":-1,"address_score":-1,"ssn_score":-1,"birth_score":-1,"phone_components":"{\"number\":{\"input\":\"+***********\",\"score\":0,\"source\":\"+11236547890\"}}","name_components":"{\"first\":{\"input\":\"Ashi\",\"score\":0,\"source\":\"LESLIE\"},\"last\":{\"input\":\"Yogi\",\"score\":0,\"source\":\"KNOPE\"},\"middle\":{\"input\":null,\"score\":null,\"source\":\"BARBARA\"}}","address_components":"{\"city\":{\"input\":\"New York\",\"score\":0,\"source\":\"PAWNEE\"},\"postal_code\":{\"input\":\"12211\",\"score\":0,\"source\":\"46001\"},\"street\":{\"input\":\"123 New york\",\"score\":53,\"source\":\"123 MAIN ST\"},\"subdivision\":{\"input\":\"NY\",\"score\":0,\"source\":\"IN\"}}","ssn_components":"{\"area\":{\"input\":null,\"score\":null,\"source\":\"MTIz\"},\"group\":{\"input\":null,\"score\":null,\"source\":\"NDU=\"},\"serial\":{\"input\":\"MTExMQ==\",\"score\":0,\"source\":\"Njc4OQ==\"}}","birth_components":"{\"day\":{\"input\":1,\"score\":0,\"source\":18},\"month\":{\"input\":12,\"score\":0,\"source\":1},\"year\":{\"input\":1999,\"score\":0,\"source\":1976}}"}],"X_average_score_max":14,"X_phone_score_max":-1,"X_name_score_max":"11","X_address_score_max":"60","X_ssn_score_max":-1,"X_dob_score_max":-1,"X_ssn_score_100_count":0,"X_phone_score_100_count":0,"X_date_of_death":0,"source_phone":"+11236547890","source_name":"BEN  WYATT","source_dob":"1-18-1976","source_ssn":"Njc4OQ==","source_address":"123 MAIN ST NEW YORK NY 10001","outcome_status":"Manual Review","outcome_mustsubmitid":"Y (Default).","outcome_reason":"Decision table matched NONE. 2 record(s) found based on average score.  Date of death found. Needs checking. "}"}
{"@timestamp":"2025-08-04T11:33:46.515395+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"0c58d6fc0733897d3888a15dac479513","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\RegistrationController::_getAssessment(Line: 671) - Identity validation failed. Please upload identity documents manually. []"}
{"@timestamp":"2025-08-04T11:34:21.456013+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"0276b1d34af7de833366ca446e405166","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\RegistrationController::redirectToValidStep(Line: 1819) - Existing session found for phone number: ********** []"}
{"@timestamp":"2025-08-04T11:34:21.615615+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"0276b1d34af7de833366ca446e405166","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\RegistrationController::redirectToValidStep(Line: 1947) - Redirecting to step: 5 []"}
{"@timestamp":"2025-08-04T11:34:21.617648+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"0276b1d34af7de833366ca446e405166","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\LoginController::_checkForExistingRegistrationSession(Line: 244) - A registration session with Phone number ********** exists. Redirecting to next valid step []"}
{"@timestamp":"2025-08-04T11:34:31.785965+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"16da68cd0743268c6ca232d07776b856","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Middleware\RegistrationSessionMiddleware::handle(Line: 34) - Registration session is valid and redirecting to API. []"}
{"@timestamp":"2025-08-04T11:34:31.926407+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"16da68cd0743268c6ca232d07776b856","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\RegistrationController::uploadIdentityDocuments(Line: 1719) -  Invalid file type. []"}
{"@timestamp":"2025-08-04T11:34:40.043421+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"dfe4f6f0ca18086cca0c3f486d2b2ffc","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\RegistrationController::redirectToValidStep(Line: 1819) - Existing session found for phone number: ********** []"}
{"@timestamp":"2025-08-04T11:34:40.094508+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"dfe4f6f0ca18086cca0c3f486d2b2ffc","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\RegistrationController::redirectToValidStep(Line: 1947) - Redirecting to step: 5 []"}
{"@timestamp":"2025-08-04T11:34:40.096545+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"dfe4f6f0ca18086cca0c3f486d2b2ffc","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\LoginController::_checkForExistingRegistrationSession(Line: 244) - A registration session with Phone number ********** exists. Redirecting to next valid step []"}
{"@timestamp":"2025-08-04T11:35:00.634755+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"57f2f37845b8c454601bd985e0d02bfe","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Middleware\RegistrationSessionMiddleware::handle(Line: 34) - Registration session is valid and redirecting to API. []"}
{"@timestamp":"2025-08-04T11:35:00.773400+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"57f2f37845b8c454601bd985e0d02bfe","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\RegistrationController::uploadIdentityDocuments(Line: 1723) -  Uploading the identity documents............. []"}
{"@timestamp":"2025-08-04T11:35:05.974345+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"57f2f37845b8c454601bd985e0d02bfe","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\RegistrationController::uploadIdentityDocuments(Line: 1728) -  Document front side uploaded to s3 successfully. []"}
{"@timestamp":"2025-08-04T11:35:06.836514+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"57f2f37845b8c454601bd985e0d02bfe","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\RegistrationController::uploadIdentityDocuments(Line: 1736) -  Document back side uploaded to s3 successfully. []"}
{"@timestamp":"2025-08-04T11:35:06.868432+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"57f2f37845b8c454601bd985e0d02bfe","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\RegistrationController::uploadIdentityDocuments(Line: 1778) -  Documents uploaded successfully []"}
{"@timestamp":"2025-08-04T11:35:29.416159+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"e37d2f423f9770ee6993701f60442472","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\RegistrationController::redirectToValidStep(Line: 1819) - Existing session found for phone number: ********** []"}
{"@timestamp":"2025-08-04T11:35:29.463343+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"e37d2f423f9770ee6993701f60442472","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\RegistrationController::redirectToValidStep(Line: 1947) - Redirecting to step:  []"}
{"@timestamp":"2025-08-04T11:35:29.465634+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"e37d2f423f9770ee6993701f60442472","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\LoginController::_checkForExistingRegistrationSession(Line: 244) - A registration session with Phone number ********** exists. Redirecting to next valid step []"}
{"@timestamp":"2025-08-04T11:46:00.559607+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"53467e7653531082888c82b1b56b17ee","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\RegistrationController::redirectToValidStep(Line: 1819) - Existing session found for phone number: ********** []"}
{"@timestamp":"2025-08-04T11:46:00.688950+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"53467e7653531082888c82b1b56b17ee","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\RegistrationController::redirectToValidStep(Line: 1947) - Redirecting to step:  []"}
{"@timestamp":"2025-08-04T11:46:00.691297+00:00","V":"V-4107b98","EC2":"Local-Server","IP":"::1","TID":"53467e7653531082888c82b1b56b17ee","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\LoginController::_checkForExistingRegistrationSession(Line: 244) - A registration session with Phone number ********** exists. Redirecting to next valid step []"}
{"@timestamp":"2025-08-04T11:47:04.009595+00:00","V":"V-4107b98","EC2":"Local-Server","IP":null,"TID":"b461ee628e774689","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"ERROR","MESSAGE":"SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry 'Rejected' for key 'status_master.status' (Connection: mysql, SQL: insert into `status_master` (`id`, `status`, `code`, `updated_at`, `created_at`) values (4fe0f0202885a834398dfa8558a626c9, Rejected, 724, 2025-08-04 11:47:03, 2025-08-04 11:47:03)) {"exception":"[object] (Illuminate\\Database\\UniqueConstraintViolationException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry 'Rejected' for key 'status_master.status' (Connection: mysql, SQL: insert into `status_master` (`id`, `status`, `code`, `updated_at`, `created_at`) values (4fe0f0202885a834398dfa8558a626c9, Rejected, 724, 2025-08-04 11:47:03, 2025-08-04 11:47:03)) at C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\illuminate\\database\\Connection.php:820)\n[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry 'Rejected' for key 'status_master.status' at C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\illuminate\\database\\MySqlConnection.php:53)"}"}
{"@timestamp":"2025-08-04T11:52:53.010679+00:00","V":"V-4107b98","EC2":"Local-Server","IP":null,"TID":"a95d21933b61c24b","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"ERROR","MESSAGE":"SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry 'Rejected' for key 'status_master.status' (Connection: mysql, SQL: insert into `status_master` (`id`, `status`, `code`, `updated_at`, `created_at`) values (d9cb0ae35fa183eaf9b684f9cf6d903c, Rejected, 724, 2025-08-04 11:52:52, 2025-08-04 11:52:52)) {"exception":"[object] (Illuminate\\Database\\UniqueConstraintViolationException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry 'Rejected' for key 'status_master.status' (Connection: mysql, SQL: insert into `status_master` (`id`, `status`, `code`, `updated_at`, `created_at`) values (d9cb0ae35fa183eaf9b684f9cf6d903c, Rejected, 724, 2025-08-04 11:52:52, 2025-08-04 11:52:52)) at C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\illuminate\\database\\Connection.php:820)\n[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry 'Rejected' for key 'status_master.status' at C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\illuminate\\database\\MySqlConnection.php:53)"}"}
{"@timestamp":"2025-08-04T11:54:08.969401+00:00","V":"V-4107b98","EC2":"Local-Server","IP":null,"TID":"b438e870339b5db6","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"ERROR","MESSAGE":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'IF NOT EXISTS `primary_contact_last_updated_at` DATETIME NULL DEFAULT NULL AFTER' at line 2 (Connection: mysql, SQL: ALTER TABLE `petitions`             ADD COLUMN IF NOT EXISTS `primary_contact_last_updated_at` DATETIME NULL DEFAULT NULL AFTER `primary_contact_person_title`,             ADD COLUMN IF NOT EXISTS `secondary_contact_last_updated_at` DATETIME NULL DEFAULT NULL AFTER `secondary_contact_person_title`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'IF NOT EXISTS `primary_contact_last_updated_at` DATETIME NULL DEFAULT NULL AFTER' at line 2 (Connection: mysql, SQL: ALTER TABLE `petitions`\r\n            ADD COLUMN IF NOT EXISTS `primary_contact_last_updated_at` DATETIME NULL DEFAULT NULL AFTER `primary_contact_person_title`,\r\n            ADD COLUMN IF NOT EXISTS `secondary_contact_last_updated_at` DATETIME NULL DEFAULT NULL AFTER `secondary_contact_person_title`) at C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\illuminate\\database\\Connection.php:825)\n[previous exception] [object] (PDOException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'IF NOT EXISTS `primary_contact_last_updated_at` DATETIME NULL DEFAULT NULL AFTER' at line 2 at C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\illuminate\\database\\Connection.php:565)"}"}
{"@timestamp":"2025-08-04T11:54:35.068646+00:00","V":"V-4107b98","EC2":"Local-Server","IP":null,"TID":"68b28f2770e9a4f4","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"ERROR","MESSAGE":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'IF NOT EXISTS `rejection_reason` VARCHAR(100) NULL DEFAULT NULL AFTER `status_id' at line 2 (Connection: mysql, SQL: ALTER TABLE `petitions`             ADD COLUMN IF NOT EXISTS `rejection_reason` VARCHAR(100) NULL DEFAULT NULL AFTER `status_id`,             ADD COLUMN IF NOT EXISTS `rejection_comments` TEXT NULL DEFAULT NULL AFTER `rejection_reason`         ) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'IF NOT EXISTS `rejection_reason` VARCHAR(100) NULL DEFAULT NULL AFTER `status_id' at line 2 (Connection: mysql, SQL: ALTER TABLE `petitions`\r\n            ADD COLUMN IF NOT EXISTS `rejection_reason` VARCHAR(100) NULL DEFAULT NULL AFTER `status_id`,\r\n            ADD COLUMN IF NOT EXISTS `rejection_comments` TEXT NULL DEFAULT NULL AFTER `rejection_reason`\r\n        ) at C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\illuminate\\database\\Connection.php:825)\n[previous exception] [object] (PDOException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'IF NOT EXISTS `rejection_reason` VARCHAR(100) NULL DEFAULT NULL AFTER `status_id' at line 2 at C:\\Users\\<USER>\\Desktop\\CanPay\\centralized-api\\vendor\\illuminate\\database\\Connection.php:565)"}"}
