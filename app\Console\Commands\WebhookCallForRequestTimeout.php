<?php
namespace App\Console\Commands;

use App\Http\Factories\Webhook\WebhookFactory;
use App\Models\StatusMaster;
use App\Models\TransactionDetails;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class WebhookCallForRequestTimeout extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'requesttimeout:webhookcall';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command call a webhook for request time out status update for modified transaction.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->merchantWebhook = new WebhookFactory();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Webhook call process started for request time-out status...");
        //get Awaiting Consumer Approval status details
        $awaiting_consumer_approval_status = getStatus(AWAITINGCONSUMERAPPROVAL);
        $from_date = Carbon::now('UTC')->subHour();
        $to_date = Carbon::now('UTC');
        $web_hook_column_name = ENV('API_ENVIRONMENT') . '_webhook_url';

        $allTransactions = TransactionDetails::on(MYSQL_RO)->select('transaction_details.id as transaction_id', 'transaction_details.transaction_number as transaction_number', 'transaction_details.intent_id as intent_id', 'emi.passthrough_param as passthrough_param', 'rmm.' . $web_hook_column_name . ' as api_url')
            ->selectRaw("(CASE
                WHEN (modified_transaction.id != '') THEN modified_transaction.amount
                ELSE transaction_details.amount
            END) as tot_amount,
            (CASE
                WHEN (modified_transaction.id != '') THEN modified_transaction.tip_amount
                ELSE transaction_details.tip_amount
            END) as tip_amount")
            ->selectRaw("'Request Timeout' as status")
            ->leftJoin('transaction_details as modified_transaction', 'modified_transaction.id', '=', 'transaction_details.change_request_transaction_ref_no')
            ->join('terminal_master as tm', 'tm.id', '=', 'transaction_details.terminal_id')
            ->join('merchant_stores as ms', 'ms.id', '=', 'tm.merchant_store_id')
            ->join('registered_merchant_master as rmm', 'rmm.id', '=', 'ms.merchant_id')
            ->join('ecommerce_merchant_intents as emi', 'emi.intent_id', '=', 'transaction_details.intent_id')
            ->whereRaw("rmm.$web_hook_column_name IS NOT NULL")
            ->whereNull('transaction_details.transaction_ref_no')
            ->where('transaction_details.change_request', 1)
            ->where('transaction_details.consumer_approval_for_change_request', $awaiting_consumer_approval_status)
            ->whereBetween('transaction_details.expiration_datetime_in_utc', [$from_date, $to_date])
            ->orderBy('transaction_details.local_transaction_time', 'DESC')->get();
        if (!empty($allTransactions)) {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Total " . count($allTransactions) . " webhook calls started for request time-out status.");
            foreach ($allTransactions as $transaction) {
                $this->merchantWebhook->sendToWebHook($transaction);
            }
        } else {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No transactions found in webhook call for request time-out status.");
        }
    }

}
