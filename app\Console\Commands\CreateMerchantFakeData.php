<?php
namespace App\Console\Commands;

use App\Http\Factories\FakeDataGenerator\FakeDataGeneratorFactory;
use Illuminate\Console\Command;

class CreateMerchantFakeData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'merchantfakedata:create {--merchants=} {--stores=} {--terminals=}';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will generate test data based on inputs';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->fakedatagenerator = new FakeDataGeneratorFactory();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $params = [
            'no_of_merchants' => $this->option('merchants'),
            'no_of_stores_under_single_merchant' => $this->option('stores'),
            'no_of_terminals_under_single_store' => $this->option('terminals'),
        ];
        $this->info("Processing....");
        $this->fakedatagenerator->populateMerchantData($params);
        $this->info("Merchant Fake Data Generated Successfully.");
    }
}
