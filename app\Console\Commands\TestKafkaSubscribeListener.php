<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;

class TestKafkaSubscribeListener extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:kafkalistener';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will test the Kafka subscribe listener';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // $this->info("Kafka listener started...");
        // $conf = new \RdKafka\Conf();
        // $conf->set('bootstrap.servers', ENV('KAFKA_BOOTSTRAP_SERVER'));
        // $conf->set('security.protocol', ENV('KAFKA_SECURITY_PROTOCOL'));
        // $conf->set('sasl.mechanism', 'PLAIN');
        // $conf->set('sasl.username', ENV('KAFKA_USERNAME'));
        // $conf->set('sasl.password', ENV('KAFKA_PASSWORD'));
        // $conf->set('group.id', 'group');
        // $conf->set('auto.offset.reset', 'earliest');

        // $topicConf = new \RdKafka\TopicConf();

        // $conf->setDefaultTopicConf($topicConf);

        // $consumer = new \RdKafka\KafkaConsumer($conf);

        // $consumer->subscribe([ENV('KAFKA_TOPIC_FOR_ALARM_NOTIFICATION')]);

        // while (true) {
        //     $message = $consumer->consume(30 * 1000);
        //     switch ($message->err) {
        //         case RD_KAFKA_RESP_ERR_NO_ERROR:
        //             $this->info($message->payload);
        //             $payload = json_decode($message->payload);
        //             if (isset($payload->code) && isset($payload->name)) {
        //                 $this->_postDataToSharingClickup($payload);
        //                 $this->_postDataToSlack($payload);
        //                 $this->_sendMailAlert($payload);
        //             } else {
        //                 $this->info("payload key not found");
        //             }
        //             sleep(10);
        //             $this->info("------------------------------------------------------------------------------------------------------------------------------------------------------");
        //             break;
        //         case RD_KAFKA_RESP_ERR__PARTITION_EOF:
        //             $this->info("No more messages; will wait for more");
        //             break;
        //         case RD_KAFKA_RESP_ERR__TIMED_OUT:
        //             $this->info("Timed out");
        //             break;
        //         default:
        //             throw new \Exception($message->errstr(), $message->err);
        //             throw new \Exception($message->errstr(), $message->err);
        //             break;
        //     }
        // }
    }

    private function _postDataToSharingClickup($payload)
    {
        $curl = curl_init();
        $body = [
            "name" => $payload->code . ' - ' . $payload->name,
            "description" => property_exists($payload, 'evidences') ? $payload->evidences : "",
            "status" => "Open",
            "priority" => 3,
            "due_date_time" => false,
            "notify_all" => true,
            "parent" => null,
            "links_to" => null,
        ];
        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://api.clickup.com/api/v2/list/900201707436/task',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($body),
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json',
                'Authorization: ' . ENV('CLICKUP_AUTHORIZATION_TOKEN'),
            ),
        ));

        curl_exec($curl);

        curl_close($curl);
    }

    private function _postDataToSlack($payload)
    {
        $curl = curl_init();
        $warningEmoji = ":warning:";
        $evidence = property_exists($payload, 'evidences') ? json_encode($payload->evidences) : "";
        $body = [
            "text" => $warningEmoji . ' ' . $payload->code . ' - ' . $payload->name . "\n \n Message: " . $evidence . "\n \n - END OF ALARM -",
        ];

        curl_setopt_array($curl, array(
            CURLOPT_URL => '*******************************************************************************',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($body),
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json',
            ),
        ));

        curl_exec($curl);

        curl_close($curl);
    }

    private function _sendMailAlert($payload)
    {
        $params['from_email'] = FROM_MAIL_INFO;
        $params['subject'] = $payload->code . ' - ' . $payload->name;
        $params['body'] = property_exists($payload, 'evidences') ? json_encode($payload->evidences) : "";
        $emails = ENV('EMAILS_FOR_KAFKA_LISTENER');
        foreach (explode(',', $emails) as $valEmails) {
            $params['email'] = $valEmails;
            $this->_sendMail($params);
        }
    }
    private function _sendMail($params)
    {
        $params['type'] = EMAIL;
        $notification_channel = getNotificationChannel($params);
        Mail::mailer($notification_channel->val)->send(['html' => 'emails.mail'], ['html' => $params['body']], function ($message) use ($params) {
            $message->to($params['email'])->subject($params['subject']);
            $message->from($params['from_email'], FROM_MAIL_NAME);
        });
    }
}
