<?php

namespace App\Console\Commands;

use App\Http\Factories\Akoya\AkoyaFactory;
use App\Http\Factories\Mx\MxFactory;
use App\Http\Factories\PurchasePower\PurchasePowerFactory;
use App\Models\BankAccountInfo;
use App\Models\ConsumerAccountBalanceTemp2;
use App\Models\JobRunnerHistory;
use App\Models\MxCheckBalanceCallHistory;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class BankBalanceUpdater extends Command
{
    private $purchasePower = null;
    private $parellelPpEnabled = null;
    private $NewPpAlgoEnabled = null;
    private $jobData = null;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:bankbalance {--mode=} {--csv=} {--segment=}';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command makes call to finicity to fetch consumer bank balance and stores it into DB.';

    private $csvFile = "";
    private $chunkSize = 500;

    private $segmentID = null;
    private $segmentMax = 6;
    private $segmentArray = [
        '1' => ["0", "1"],
        '2' => ["2", "3", "4"],
        '3' => ["5", "6"],
        '4' => ["7", "8", "9"],
        '5' => ["a", "b", "c"],
        '6' => ["d", "e", "f"],
    ];

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->akoyaFactory = new AkoyaFactory();
        $this->mxFactory = new MxFactory();
        $this->purchasePower = new PurchasePowerFactory();
        $this->pending = getStatus(PENDING);
        $this->returned = getStatus(RETURNED);
        $this->parellelPpEnabled = getSettingsValue('prallel_pp_algo', 0); // Check wheather the parrellel purchase power algo is enabled or disabled
        $this->NewPpAlgoEnabled = getSettingsValue('enable_new_pp_algo', 0); // Check wheather the new purchase power algo is enabled or disabled
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $mode = $this->option('mode');
        $this->segmentID = $this->option('segment');
        
        $this->jobData = [
            'job_name' => $this->getName(),
            'job_type' => 'bank-balance-update-segment-' . $this->segmentID,
        ];
        if ($mode === 'calculate') {
            addUpdateJobRunnerHistory($this->jobData, JOB_START, 'bank-balance-update');
        }

        // Check wheather the cron is enabled or disabled
        $checkCronEnabled = getSettingsValue('enable_finicity_bank_balance_update', 0);
        if ($checkCronEnabled == 0) {
            Log::channel('bank-balance-update')->warning(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "NOT starting Finicity Consumer Balance update and Purchase Power calculation. It is DISABLED in settings.");
            return false;
        }

        if (empty($this->segmentID) || $this->segmentID < 0 || $this->segmentID > $this->segmentMax) {
            echo "Invalid segment name specified. Exiting...";
            return;
        }

        Log::channel('bank-balance-update')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "#################################### Segment: $this->segmentID #######################################");

        $this->csvFile = $this->option('csv');
        if (empty($this->csvFile)) {
            echo "CSV File name not specified. Exiting...";
            return;
        }

        $this->csvFile = $this->csvFile . "-" . $this->segmentID; // Add the segment ID to the filename.

        if ($mode === 'calculate') {
            Log::channel('bank-balance-update')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Segment: $this->segmentID: Starting consumer balance update and Purchase Power calculation. Will export to csv file $this->csvFile...");
            $records = $this->_preparePurchasePowerData();
            Log::channel('bank-balance-update')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Segment: $this->segmentID: Finished consumer balance update and Purchase Power calculation. No of records exported: $records.");
            echo "Finished consumer balance update and Purchase Power calculation. No of records exported: $records.";
        } elseif ($mode === 'load') {
            Log::channel('bank-balance-update')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Segment: $this->segmentID: Loading consumer balance and Purchase Power data. Will load from csv file $this->csvFile...");
            $records = $this->_loadPurchasePowerData();
            Log::channel('bank-balance-update')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Segment: $this->segmentID: Finished loading consumer balance and Purchase Power data. No of records loaded: $records.");
            echo "Finished loading consumer balance and Purchase Power data. No of records loaded: $records.";
            // Update job runner history
            $jobDetails = JobRunnerHistory::where($this->jobData)->whereRaw('date(started_at) = CURRENT_DATE()')->orderBy('created_at', 'DESC')->first();
            if ($jobDetails) {
                addUpdateJobRunnerHistory($this->jobData, JOB_COMPLETED, 'bank-balance-update', $jobDetails->id);
            }
        } else {
            Log::channel('bank-balance-update')->warning(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Segment: $this->segmentID: No operation specified. Exiting.");
        }
    }

    private function _preparePurchasePowerData()
    {
        $mst_offset = getMstTime();

        $csvFileHandle = fopen($this->csvFile, "w");

        Log::channel('bank-balance-update')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Segment: $this->segmentID: Getting all records... will segmentize after retrieving.");

        DB::Statement("DROP TEMPORARY TABLE IF EXISTS temp_balance_update_first" . $this->segmentID . "");
        DB::Statement("DROP TEMPORARY TABLE IF EXISTS temp_balance_update_second" . $this->segmentID . "");

        DB::Statement("CREATE TEMPORARY TABLE temp_balance_update_first" . $this->segmentID . "(uid varchar(40), phone varchar(20), registration_ref_id varchar(40), `id` varchar(40) NOT NULL, ref_no VARCHAR(40) DEFAULT NULL, banking_solution_id varchar(40) DEFAULT NULL, fed_bank_id INT DEFAULT NULL, `user_id` varchar(40) DEFAULT NULL,`merchant_id` varchar(40) DEFAULT NULL,`bank_name` varchar(100) DEFAULT NULL,`banker` varchar(100) DEFAULT NULL,`fees_account_number` varchar(50) DEFAULT NULL,`card_number` varchar(16) DEFAULT NULL,`routing_no` varchar(32) DEFAULT NULL,`account_no` varchar(32) DEFAULT NULL,`account_type` varchar(32) DEFAULT NULL,`institution_id` varchar(60) DEFAULT NULL,`institution_login_id` varchar(255) DEFAULT NULL,`account_id` varchar(255) DEFAULT NULL,`finicity_id` varchar(60) DEFAULT NULL,`mx_consumer_id` varchar(100) DEFAULT NULL,`mx_member_guid` varchar(255) DEFAULT NULL,`username` varchar(255) DEFAULT NULL,`password` varchar(255) DEFAULT NULL,`secret_key_url` varchar(255) DEFAULT NULL,`token` varchar(255) DEFAULT NULL,`qr_url` varchar(255) DEFAULT NULL,`one_time_transaction_limit` int(11) DEFAULT NULL,`external_validation_type` varchar(100) DEFAULT NULL,`account_verified` tinyint(4) NOT NULL DEFAULT 0,`mobile_verified` tinyint(4) NOT NULL DEFAULT 0,`email_verified` tinyint(4) NOT NULL DEFAULT 0,real_account_no_required TINYINT(1) DEFAULT 0,`status` varchar(40) NOT NULL,`created_at` datetime NOT NULL,`updated_at` datetime NOT NULL,smb_code VARCHAR(5),purchase_power DECIMAL(10,2) ,user_created_at DATETIME, user_migrated_at DATETIME, user_type TINYINT, banking_solution_name VARCHAR(50) DEFAULT NULL, id_token TEXT DEFAULT NULL, purchase_power_source VARCHAR(255) DEFAULT NULL, is_algo_based TINYINT(1) DEFAULT 0, mx_row_number INT DEFAULT 0)");

        DB::Statement("CREATE TEMPORARY TABLE temp_balance_update_second" . $this->segmentID . "(uid varchar(40), phone varchar(20), registration_ref_id varchar(40), `id` varchar(40) NOT NULL, ref_no VARCHAR(40) DEFAULT NULL, banking_solution_id varchar(40) DEFAULT NULL, fed_bank_id INT DEFAULT NULL, `user_id` varchar(40) DEFAULT NULL,`merchant_id` varchar(40) DEFAULT NULL,`bank_name` varchar(100) DEFAULT NULL,`banker` varchar(100) DEFAULT NULL,`fees_account_number` varchar(50) DEFAULT NULL,`card_number` varchar(16) DEFAULT NULL,`routing_no` varchar(32) DEFAULT NULL,`account_no` varchar(32) DEFAULT NULL,`account_type` varchar(32) DEFAULT NULL,`institution_id` varchar(60) DEFAULT NULL,`institution_login_id` varchar(255) DEFAULT NULL,`account_id` varchar(255) DEFAULT NULL,`finicity_id` varchar(60) DEFAULT NULL,`mx_consumer_id` varchar(100) DEFAULT NULL,`mx_member_guid` varchar(255) DEFAULT NULL,`username` varchar(255) DEFAULT NULL,`password` varchar(255) DEFAULT NULL,`secret_key_url` varchar(255) DEFAULT NULL,`token` varchar(255) DEFAULT NULL,`qr_url` varchar(255) DEFAULT NULL,`one_time_transaction_limit` int(11) DEFAULT NULL,`external_validation_type` varchar(100) DEFAULT NULL,`account_verified` tinyint(4) NOT NULL DEFAULT 0,`mobile_verified` tinyint(4) NOT NULL DEFAULT 0,`email_verified` tinyint(4) NOT NULL DEFAULT 0,real_account_no_required TINYINT(1) DEFAULT 0,`status` varchar(40) NOT NULL,`created_at` datetime NOT NULL,`updated_at` datetime NOT NULL,smb_code VARCHAR(5),purchase_power DECIMAL(10,2), user_created_at DATETIME, user_migrated_at DATETIME, user_type TINYINT,banking_solution_name VARCHAR(50) DEFAULT NULL, id_token TEXT DEFAULT NULL, purchase_power_source VARCHAR(255) DEFAULT NULL, is_algo_based TINYINT(1) DEFAULT 0, mx_row_number INT DEFAULT 0)");

        DB::Statement("INSERT INTO temp_balance_update_first" . $this->segmentID . "
        WITH deduplicated_users AS (
            SELECT u.user_id AS uid, u.phone, u.registration_ref_id, bai.id AS bai_id, bai.ref_no, bai.banking_solution_id, bai.fed_bank_id, bai.user_id AS bai_user_id, bai.merchant_id, bai.bank_name, bai.banker, bai.fees_account_number, bai.card_number, bai.routing_no, bai.account_no, bai.account_type, bai.institution_id, bai.institution_login_id, bai.account_id, bai.finicity_id, bai.mx_consumer_id, bai.mx_member_guid, bai.username, bai.password, bai.secret_key_url, bai.token, bai.qr_url, bai.one_time_transaction_limit, bai.external_validation_type, bai.account_verified, bai.mobile_verified, bai.email_verified, bai.real_account_no_required, bai.status, bai.created_at AS bai_created_at, bai.updated_at AS bai_updated_at, smb.code AS bai_status_code, u.purchase_power, u.created_at AS user_created_at, u.migrated_at, u.existing_user, bsm.banking_solution_name, art.id_token, u.purchase_power_source, u.is_algo_based, ROW_NUMBER() OVER (PARTITION BY CASE WHEN bsm.banking_solution_name = '" . MX . "' THEN bai.mx_member_guid ELSE bai.id END ORDER BY u.user_id) AS rn
            FROM users u FORCE INDEX(idx_bank_link_type)
            INNER JOIN user_roles ur FORCE INDEX(idx_user_roles_name) ON ur.role_name = 'Consumer' AND ur.role_id = u.role_id
            INNER JOIN status_master sm ON sm.id = u.status AND sm.code IN ('" . USER_ACTIVE . "', '" . SUSPECTED_FRAUD . "', '" . RESTRICTED_USER . "')
            INNER JOIN user_bank_account_info bai FORCE INDEX(fk_user_bank_account_info_user_id) ON bai.user_id = u.user_id AND bai.account_id != ''
            INNER JOIN banking_solution_masters bsm ON bai.banking_solution_id = bsm.id
            LEFT JOIN status_master smb ON smb.id = bai.status AND smb.code IN ('" . BANK_ACTIVE . "', '" . BANK_INACTIVE . "', '" . BANK_DISABLE . "')
            LEFT JOIN akoya_refresh_tokens art ON art.consumer_id = u.user_id AND art.institution_id = bai.institution_id
            WHERE u.bank_link_type = 1 AND bsm.banking_solution_name != '" . FINICITY . "' AND bai.mx_user_action_needed = 0 AND bai.mx_non_actionable_status_detected = 0
        )
        SELECT uid, phone, registration_ref_id, bai_id, ref_no, banking_solution_id, fed_bank_id, bai_user_id, merchant_id, bank_name, banker, fees_account_number, card_number, routing_no, account_no, account_type, institution_id, institution_login_id, account_id, finicity_id, mx_consumer_id, mx_member_guid, username, password, secret_key_url, token, qr_url, one_time_transaction_limit, external_validation_type, account_verified, mobile_verified, email_verified, real_account_no_required, status, bai_created_at, bai_updated_at, bai_status_code, purchase_power, user_created_at, migrated_at, existing_user, banking_solution_name, id_token, purchase_power_source, is_algo_based, rn
        FROM deduplicated_users
        WHERE rn = 1");

        DB::Statement("INSERT INTO temp_balance_update_second" . $this->segmentID . "
        SELECT DISTINCT temp_balance_update_first" . $this->segmentID . ".* FROM temp_balance_update_first" . $this->segmentID . "
        LEFT JOIN transaction_details td FORCE INDEX(idx_account_id) ON td.account_id = temp_balance_update_first" . $this->segmentID . ".id AND td.represent_count <=2
        LEFT JOIN return_reason_masters rm FORCE INDEX(idx_canpay) ON rm.canpay_represent = 1 AND rm.new_banking = 0 AND td.return_reason = rm.id
        WHERE if(td.status_id = '" . $this->returned . "' OR td.status_id = '" . $this->pending . "',temp_balance_update_first" . $this->segmentID . ".smb_code IN ('" . BANK_ACTIVE . "', '" . BANK_INACTIVE . "','" . BANK_DISABLE . "'),temp_balance_update_first" . $this->segmentID . ".smb_code IN ('" . BANK_ACTIVE . "', '" . BANK_INACTIVE . "')) AND (temp_balance_update_first" . $this->segmentID . ".id IS NOT NULL OR rm.id IS NOT NULL)");

        $consumersAll = DB::select("SELECT temp_balance_update_second" . $this->segmentID . ".*, cab.id AS refresh_balance_called, cab.balance AS last_balance
        FROM temp_balance_update_second" . $this->segmentID . "
        LEFT JOIN (
            SELECT cab1.account_id, cab1.id, cab1.balance
            FROM consumer_account_balances cab1
            WHERE `source` IN ('" . ADMIN_REFRESH_BALANCE . "', '" . CONSUMER_LOGIN . "')
            AND DATE(CONVERT_TZ(cab1.created_at, '+00:00', ?)) = ?
            GROUP BY cab1.account_id
            HAVING MAX(cab1.created_at)
        ) cab ON cab.account_id = temp_balance_update_second" . $this->segmentID . ".id", [$mst_offset, Carbon::now('MST')->toDateString()]);

        $consumers = []; // Only consumers falling into this segment

        Log::channel('bank-balance-update')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Segment: $this->segmentID: Retrieved all records. Total: " . count($consumersAll));

        // First, copy all records matching this segment to a new array.
        foreach ($consumersAll as $consumer) {

            $lastChar = substr($consumer->uid, -1);

            if (in_array($lastChar, $this->segmentArray[$this->segmentID])) {
                $consumers[] = $consumer;
            }
        }

        Log::channel('bank-balance-update')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Segment: $this->segmentID: Records in this segment: " . count($consumers) . " of " . count($consumersAll) . " total");

        $records = 0;

        fputcsv($csvFileHandle, $this->_prepareHeader()); // Writing to CSV file
        $elapsedSecondsForSegment = 1;
        $segmentStartedAt = time(); // When a segment started.
        $chunkStartedAt = time(); // When a chunk started.
        if (count($consumers) > 0) {
            foreach ($consumers as $consumer) {
                $time_now = Carbon::now(); // Get current time for temp table's created_at and updated_at
                $balanceNPurchasePower = $this->_updateBankBalanceAndCalculate($consumer);
                if ($balanceNPurchasePower != null) {
                    // Creating Bulk insert array
                    $data = [];
                    $data['consumer_id'] = $consumer->uid;
                    $data['account_id'] = $consumer->id;
                    $data['balance'] = $balanceNPurchasePower['effectiveBalance'];
                    $data['purchase_power'] = $balanceNPurchasePower['purchasePower'];
                    $data['purchase_power_reason'] = $balanceNPurchasePower['purchasePowerReason'];
                    $data['purchase_power_source'] = $balanceNPurchasePower['purchasePowerSource'];
                    $data['response_raw_balance'] = $balanceNPurchasePower['balance'];
                    $data['response_available_balance'] = $balanceNPurchasePower['availableBalanceAmount'];
                    $data['banking_solution_response'] = $balanceNPurchasePower['banking_solution_response'];
                    $data['error'] = isset($balanceNPurchasePower['error']) ? $balanceNPurchasePower['error'] : 0;
                    $data['do_not_update'] = $consumer->refresh_balance_called != null ? 1 : 0; // column added to check if refresh balance is called or not
                    $data['created_at'] = $time_now;
                    $data['updated_at'] = $time_now;
                    $data['status_id'] = $consumer->status;
                    fputcsv($csvFileHandle, $data); // Writing to CSV file
                    $records++;
                }

                if (($records % $this->chunkSize) == 0) {
                    $timeNow = time();
                    $elapsedSecondsForSegment = ($timeNow - $segmentStartedAt) == 0 ? 1 : ($timeNow - $segmentStartedAt); // if the elaspsed time zero, just set it to 1.

                    $elapsedSecondsForChunk = ($timeNow - $chunkStartedAt) == 0 ? 1 : ($timeNow - $chunkStartedAt); // if the elaspsed time zero, just set it to 1.

                    $throughPutSegment = round($records / $elapsedSecondsForSegment, 1);
                    $throughPutChunk = round($this->chunkSize / $elapsedSecondsForChunk, 1);
                    Log::channel('bank-balance-update')->debug(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Segment: $this->segmentID: Fetched balance for $records rows of " . count($consumers) . ". Throughput - Chunk: $throughPutChunk/sec, Segment: $throughPutSegment/sec...");
                    $chunkStartedAt = time(); // Reset the chunk start time.
                }
            }
        }

        $throughPutSegment = round($records / $elapsedSecondsForSegment, 1);
        Log::channel('bank-balance-update')->debug(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Segment: $this->segmentID: Completed fetching balance for " . count($consumers) . ". Throughput - Segment: $throughPutSegment/sec.");
        fclose($csvFileHandle);
        return $records;
    }

    private function _prepareHeader()
    {
        return ['consumer_id', 'account_id', 'balance', 'purchase_power', 'purchase_power_reason', 'response_raw_balance', 'response_available_balance', 'banking_solution_response', 'error', 'do_not_update', 'created_at', 'updated_at', 'status_id'];
    }

    private function _updateBankBalanceAndCalculate($consumer)
    {
        $skip_balance_fetch = 0;
        try {
            if ($consumer->banking_solution_name == AKOYA) {
                // Check Akoya Id Token is available or not for previous consumer
                // If not available then skip balance the fetch API call
                if ($consumer->id_token) {
                    Log::channel('bank-balance-update')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Initiating Akoya balance fetch API call for consumer ID: " . $consumer->user_id);
                    $balanceNPurchasePower = $this->akoyaFactory->getConsumerAccountBalance($consumer->account_id, $consumer->user_id, 1, $consumer->id_token);
                } else {
                    Log::channel('bank-balance-update')->error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Akoya Id Token is not available for consumer: " . $consumer->user_id);
                    $balanceNPurchasePower = ["availableBalanceAmount" => 0, "error" => 1, "balance" => 0, "effectiveBalance" => 0, "banking_solution_response" => "Akoya ID token not found for Consumer: " . $consumer->user_id];
                }
            } elseif ($consumer->banking_solution_name == MX) {
                // Fetch the last record for the specified user_id and account_id
                $lastRecord = MxCheckBalanceCallHistory::where('consumer_id', $consumer->user_id)
                    ->where('mx_account_id', $consumer->account_id)
                    ->whereNotNull('completed_at')
                    ->whereNotNull('response_received_at')
                    ->orderBy('created_at', 'desc')
                    ->first();

                $interval = env('BALANCE_CHECK_API_CALL_INTERVAL');
                $currentTime = Carbon::now();

                if (!empty($lastRecord)) {
                    $lastResponseTime = Carbon::parse($lastRecord->response_received_at);
                    $isOlderThanInterval = $lastResponseTime->lt($currentTime->subMinutes($interval));

                    if ($isOlderThanInterval) {
                        Log::channel('bank-balance-update')->info(
                            __METHOD__ . " (" . __LINE__ . ") - " . "Last Check Balance API call was older than " . $interval . " minutes. " . "Proceeding to call the Check Balance API for Consumer: " . $consumer->user_id
                        );
                        $bank_details = BankAccountInfo::where(['account_id' => $consumer->account_id])->first();
                        $balanceNPurchasePower = $this->mxFactory->checkBalance($bank_details, $consumer->user_id, SCHEDULED_BALANCE_FETCH);
                        Log::channel('bank-balance-update')->info(
                            __METHOD__ . " (" . __LINE__ . ") - " . "Check Balance API call successful for Consumer: " . $consumer->user_id
                        );
                        return null;
                    } else {
                        Log::channel('bank-balance-update')->warning(
                            __METHOD__ . " (" . __LINE__ . ") - " . "Last Check Balance API call at: " . $lastRecord->response_received_at . " for Consumer: " . $consumer->user_id . " which is less than " . $interval . " minutes. Skipping the API call."
                        );
                        $skip_balance_fetch = 1;
                        $balanceNPurchasePower = ["availableBalanceAmount" => 0, "error" => 1, "balance" => 0, "effectiveBalance" => $consumer->last_balance, "banking_solution_response" => "Last Check Balance API call at: " . $lastRecord->response_received_at . " for Consumer: " . $consumer->user_id . " which is less than " . $interval . " minutes. Skipped the API call."];
                    }
                } else {
                    Log::channel('bank-balance-update')->info(
                        __METHOD__ . " (" . __LINE__ . ") - " . "No previous Check Balance API call records found for Consumer: " . $consumer->user_id . ". Proceeding to call the Check Balance API."
                    );
                    $bank_details = BankAccountInfo::where(['account_id' => $consumer->account_id])->first();
                    $balanceNPurchasePower = $this->mxFactory->checkBalance($bank_details, $consumer->user_id, SCHEDULED_BALANCE_FETCH);
                    Log::channel('bank-balance-update')->info(
                        __METHOD__ . " (" . __LINE__ . ") - " . "Check Balance API call successful for Consumer: " . $consumer->user_id
                    );
                    return null;
                }
            } else {
                // No banking solution found
                Log::channel('bank-balance-update')->error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No banking solution id found for Consumer: " . $consumer->user_id);
                $balanceNPurchasePower = ["availableBalanceAmount" => 0, "error" => 1, "balance" => 0, "effectiveBalance" => 0, "banking_solution_response" => "No banking solution id found for Consumer: " . $consumer->user_id];
            }
            Log::channel('bank-balance-update')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Banking Solution Name: " . $consumer->banking_solution_name);
            Log::channel('bank-balance-update')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Response returned from Banking Solution Factory: " . json_encode($balanceNPurchasePower));
        } catch (\Exception $e) {
            Log::channel('bank-balance-update')->error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "There was some problem trying to fetch balance for: " . $consumer->user_id, [EXCEPTION => $e]);
            $balanceNPurchasePower = ["availableBalanceAmount" => 0, "error" => 1, "balance" => 0, "effectiveBalance" => 0, "banking_solution_response" => "There was a problem in calling " . $consumer->banking_solution_name . " API"];
            return null;
        }
        $account_balance = $balanceNPurchasePower["effectiveBalance"];

        if (!isset($balanceNPurchasePower['error']) && $skip_balance_fetch == 0) {
            // Prepare data for purchase power calculation
            $purchasePowerCalculationData = [
                'consumer_id' => $consumer->uid,
                'account_id' => $consumer->id,
                'account_no' => $consumer->account_no,
                'balance' => $account_balance,
                'updated_at' => Carbon::now(),
                'parellel_pp_enabled' => $this->parellelPpEnabled,
                'enable_new_pp_algo' => $this->NewPpAlgoEnabled,
                'consumer_registration_date' => $consumer->user_type == 0 ? $consumer->user_created_at : $consumer->user_migrated_at,
                'registration_ref_id' => isset($consumer->registration_ref_id) ? $consumer->registration_ref_id : '',
                'purchase_power_source' => $consumer->purchase_power_source,
                'api_call_not_needed' => 1,
                'is_algo_based' => $consumer->is_algo_based,
            ];
            $purchasePowerRAW = $this->purchasePower->calculatePurchasePowerRaw($purchasePowerCalculationData);
        } else {
            $purchasePowerRAW["purchasePower"] = $consumer->purchase_power;
            $purchasePowerRAW["reason"] = $skip_balance_fetch == 0 ? "Consumer old purchase power restored due to exception in " . $consumer->banking_solution_name . "." : "Consumer old purchase power restored as the Balance Fetch occurred in less than " . $interval . " minutes. Skipped the API call.";
        }

        $balanceNPurchasePower['purchasePower'] = $purchasePowerRAW["purchasePower"];
        $balanceNPurchasePower['purchasePowerReason'] = $purchasePowerRAW["reason"];
        $balanceNPurchasePower['purchasePowerSource'] = $consumer->purchase_power_source;

        return $balanceNPurchasePower;
    }

    private function _loadPurchasePowerData()
    {
        Log::channel('bank-balance-update')->debug(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Segment: $this->segmentID: Clearing consumer_account_balances_temp2 table...");
        DB::select("DELETE FROM consumer_account_balances_temp2 where segment_id=?", [$this->segmentID]);

        $totalLines = $this->_countLines($this->csvFile);

        $csvFileHandle = fopen($this->csvFile, "r");
        $records = 0;
        $queue = [];
        $firstLine = true;
        while (($csvData = fgetcsv($csvFileHandle)) !== false) {
            if ($firstLine) {
                $firstLine = false;
                continue;
            }
            $data = [];
            $data['consumer_id'] = $csvData[0];
            $data['account_id'] = $csvData[1];
            $data['balance'] = $csvData[2];
            $data['purchase_power'] = $csvData[3];
            $data['purchase_power_reason'] = $csvData[4];
            $data['purchase_power_source'] = $csvData[5];
            $data['response_raw_balance'] = $csvData[6];
            $data['response_available_balance'] = $csvData[7];
            $data['banking_solution_response'] = $csvData[8];
            $data['error'] = $csvData[9];
            $data['do_not_update'] = $csvData[10]; // column added to check if refresh balance is called or not
            $data['created_at'] = $csvData[11];
            $data['updated_at'] = $csvData[12];
            $data['status_id'] = $csvData[13];
            $data['segment_id'] = $this->segmentID;

            $records++;
            $queue[] = $data;
            if (count($queue) == $this->chunkSize) {

                ConsumerAccountBalanceTemp2::insert($queue);
                Log::channel('bank-balance-update')->debug(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Segment: $this->segmentID: Loaded $records rows of " . $totalLines . "...");
                $queue = [];
            }
        }

        if (!empty($queue)) { // Processing the remaining queue items.
            $tmp = count($queue);
            ConsumerAccountBalanceTemp2::insert($queue);
            Log::channel('bank-balance-update')->debug(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Segment: $this->segmentID: Loaded left over $tmp rows...");
        }
        $this->_copyToMasterTables();
        return $records;
    }

    private function _copyToMasterTables()
    {
        $source = SCHEDULED_BALANCE_FETCH;
        Log::channel('bank-balance-update')->debug(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Segment: $this->segmentID: Updating Master Table consumer_account_balances...");
        // insert into history table
        DB::select("INSERT INTO consumer_account_balances SELECT CONCAT(unix_timestamp(),id), consumer_id, account_id, status_id, balance, purchase_power, purchase_power_reason,purchase_power_source, response_raw_balance, response_available_balance, banking_solution_response, 0, error, '" . $source . "',  do_not_update, created_at, updated_at FROM consumer_account_balances_temp2 where segment_id=?", [$this->segmentID]);

        // update pp into users table
        Log::channel('bank-balance-update')->debug(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Segment: $this->segmentID: Updating Purchase power in users table...");
        DB::select("UPDATE users u INNER JOIN consumer_account_balances_temp2 cabt ON u.user_id = cabt.consumer_id INNER JOIN status_master sm ON cabt.status_id = sm.id SET u.purchase_power = cabt.purchase_power, u.updated_at = cabt.updated_at WHERE segment_id=? AND cabt.purchase_power IS NOT NULL AND u.disable_automatic_purchase_power = 0 AND sm.code = '" . BANK_ACTIVE . "' AND cabt.do_not_update = 0 ", [$this->segmentID]);
    }

    private function _print_mem()
    {
        $mem_usage = round(memory_get_usage() / 1048576); // Currently used memory
        $mem_peak = round(memory_get_peak_usage() / 1048576); // Peak memory usage in MB
        echo "Using $mem_usage MB of memory. Peak was $mem_peak MB\n";
        Log::channel('bank-balance-update')->debug(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Segment: $this->segmentID: Using $mem_usage MB of memory. Peak was $mem_peak MB");
    }

    private function _countLines($file)
    {
        $linecount = 0;
        $handle = fopen($file, "r");
        while (!feof($handle)) {
            $line = fgets($handle);
            $linecount++;
        }
        fclose($handle);
        return $linecount - 2; // Removing header and trailing row from CSV
    }
}
