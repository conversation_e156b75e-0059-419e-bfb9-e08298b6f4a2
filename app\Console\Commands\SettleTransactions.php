<?php

namespace App\Console\Commands;

use App\Models\TransactionDetails;
use App\Models\TransactionReleaseDate;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class SettleTransactions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'settle:transactions {--release_date=}';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will fetch transactions from Transaction Release dates table for today and will settle the transactions and points for that.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        if ($this->option('release_date')) {
            $release_date = $this->option('release_date');
        } else {
            $release_date = date('Y-m-d');
        }

        $success = getStatus(SUCCESS);
        $pending = getStatus(PENDING);
        $transactions = TransactionReleaseDate::join('transaction_details', 'transaction_details.id', '=', 'transaction_release_dates.transaction_id')
            ->join('users', 'users.user_id', '=', 'transaction_details.consumer_id')
            ->join('timezone_masters', 'timezone_masters.id', '=', 'transaction_details.timezone_id')
            ->where('transaction_release_dates.release_date', '<=', $release_date)
            ->where('transaction_details.status_id', $pending)
            ->where('transaction_release_dates.invalid', 0)
            ->select('transaction_details.*', 'timezone_masters.timezone_name', 'users.algo_user_type')->get();
        if (sizeof($transactions) != 0) {
            foreach ($transactions as $transaction) {
                // update the parent transaction
                TransactionDetails::where('id', $transaction->id)->update(array('status_id' => $success));
                // Settle Points associated with the Transaction
                updateRewardStatus($transaction, SETTLE_TRANSACTION);
                // created new success row
                createSuccessTransaction($transaction, SETTLE_TRANSACTION);
                // Check and update the user's algo_user_type
                checkAndUpdateAlgoUserType($transaction);
                Log::channel('settle-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction ID : " . $transaction->id . "  release successfully.");
                $this->info("Transaction ID: " . $transaction->id . " updated successfully.");
            }
        } else {
            Log::channel('settle-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No Transactions found to update for Date: " . $release_date);
            $this->info("No Transactions found to update for Date: " . $release_date);
        }
    }
}
