<?php
namespace App\Console\Commands;

use App\Models\TransactionDetails;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class UpdatePostingBlockFlag extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:postingblockflag';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will update all the transactions with posting block flag 1 to 0.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->_updateTransaction();
    }
    /**
     * This function fetches bank balance for all consumers who has added bank from finicity and keeps record in the database
     */
    private function _updateTransaction()
    {
        $this->info("Fetching all the transactions...");
        Log::channel('update-posting-block')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "===========UPDATE IS POSTING BLOCK CRON STARTED RUNNING==========");
        Log::channel('update-posting-block')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Fetching all the post blocking transactions.");

        //fetching all the non blocked pending transaction that are yet to be posted into acheck21
        $transactions = TransactionDetails::where('is_posting_blocked', 1)->get();

        if (sizeof($transactions) != 0) {
            foreach ($transactions as $transaction) {
                $transaction->is_posting_blocked = 0;
                $transaction->save();
            }
            Log::channel('update-posting-block')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "All transactions updated successfully.");
            Log::channel('update-posting-block')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "===========UPDATE IS POSTING BLOCK CRON FINISHED RUNNING==========");
            $this->info("Transactions updated successfully.");
        } else {
            Log::channel('update-posting-block')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No transactions found to update.");
            $this->info("No transactions found to update.");
        }
    }
}
