<?php
namespace App\Console\Commands;

use App\Http\Clients\Acheck21HttpClient;
use App\Http\Factories\Transaction\TransactionFactory;
use App\Models\Acheck21DocumentIdHistory;
use App\Models\ReturnRepresentHistory;
use App\Models\TransactionDetails;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class RepresentR01Return extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'represent:r01transaction {--transaction_ids=}';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command represents returned transaction through acheck21 which are R01.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->transaction = new TransactionFactory();
        $this->acheck = new Acheck21HttpClient();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->info("Representing return transactions...");
        $success = getStatus(SUCCESS);
        $failed = getStatus(FAILED);
        // represent R01 transactions for finicity linked consumers
        $this->_representR01Transaction($success, $failed);
    }

    private function _representR01Transaction($success, $failed)
    {
        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "=========== REPESENTING R01 RETURN TRANSACTIONS FOR FINICITY LINKED BANK ACCOUNTS MANUALLY===============");
        //fetch all the approved by consumer returned transactions
        $id_array = explode(",", $this->option('transaction_ids'));
        $transactions = TransactionDetails::whereIn('id', $id_array)->get();
        if (sizeof($transactions) != 0) {
            foreach ($transactions as $transaction) {
                // storing into history table for each transaction
                $history = new ReturnRepresentHistory();
                $history->consumer_id = $transaction->consumer_id;
                $history->transaction_id = $transaction->id;
                $history->amount = $transaction->amount + $transaction->tip_amount;
                $history->is_manual = 0;
                try {
                    Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Representing transaction to acheck21 for transaction amount: " . $transaction->consumer_bank_posting_amount . " with id: " . $transaction->id);
                    $reference_transaction = TransactionDetails::join('status_master', 'transaction_details.status_id', '=', 'status_master.id')->where('transaction_details.transaction_ref_no', $transaction->id)->where('status_master.code', PROCESSED_FOR_ACHECK21)->orderBy("transaction_details.created_at", 'DESC')->first();
                    $response = $this->acheck->representTransaction($reference_transaction->acheck_document_id);
                    if ($response == 204) {
                        $this->_createTransaction($transaction, $reference_transaction->acheck_document_id, $transaction->account_id);
                        // update the parent transaction
                        $transaction->is_represented = 1;
                        $transaction->transaction_represented = $transaction->account_id;
                        $transaction->represent_count = $transaction->represent_count + 1;
                        $transaction->save();
                        //adding details to store into history table
                        $history->outcome = "Success. Old transaction represented into acheck21. Executed from daily scheduler.";
                        $history->reason_representable = 1;
                        $history->status_id = $success;
                        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction represented for transaction amount: " . $transaction->consumer_bank_posting_amount . " with id: " . $transaction->id);
                    } else {
                        //adding details to store into history table
                        $history->outcome = $response;
                        $history->reason_representable = 1;
                        $history->status_id = $failed;
                        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck21 could not represent the transaction for transaction amount: " . $transaction->consumer_bank_posting_amount . " with id: " . $transaction->id);
                    }
                    $history->save();
                } catch (\Exception $e) {
                    //adding details to store into history table
                    $history->outcome = $e;
                    $history->status_id = $failed;
                    Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "There was a problem trying to represent transaction with id: " . $transaction->id, [EXCEPTION => $e]);
                    $history->save();
                    continue;
                }
            }
            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transactions represented successfully.");
            $this->info("Transactions represented successfully.");
        } else {
            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No transactions found to represent.");
            $this->info("No transactions found to represent.");
        }
    }
    private function _createTransaction($transaction, $doc_id, $account_id)
    {
        //create a new transaction
        $transaction_details = new TransactionDetails();
        $transaction_details->transaction_number = $transaction->transaction_number;
        $transaction_details->transaction_ref_no = $transaction->id;
        $transaction_details->user_id = $transaction->user_id;
        $transaction_details->consumer_id = $transaction->consumer_id;
        $transaction_details->terminal_id = $transaction->terminal_id;
        $transaction_details->account_id = $account_id;
        $transaction_details->transaction_time = Carbon::now();
        $transaction_details->local_transaction_time = Carbon::now($transaction->timezone_name);
        $transaction_details->local_transaction_year = date("Y", strtotime($transaction_details->local_transaction_time));
        $transaction_details->local_transaction_month = date("m", strtotime($transaction_details->local_transaction_time));
        $transaction_details->local_transaction_date = date("Y-m-d", strtotime($transaction_details->local_transaction_time));
        $transaction_details->timezone_id = $transaction->timezone_id;
        $transaction_details->consumer_bank_posting_amount = $transaction->consumer_bank_posting_amount;
        $transaction_details->reward_amount_used = $transaction->reward_amount_used;
        $transaction_details->reward_point_used = $transaction->reward_point_used;
        $transaction_details->return_representment_reward_deduction_amount = $transaction->return_representment_reward_deduction_amount;
        $transaction_details->amount = $transaction->amount;
        $transaction_details->tip_amount = $transaction->tip_amount;
        $transaction_details->tip_type = $transaction->tip_type;
        $transaction_details->tip_add_time = $transaction->tip_add_time;
        $transaction_details->used_qr_id = $transaction->used_qr_id;
        $transaction_details->status_id = getStatus(PROCESSED_FOR_ACHECK21);
        $transaction_details->transaction_type_id = $transaction->transaction_type_id;
        $transaction_details->transaction_place = ACHECK21;
        $transaction_details->acheck_document_id = $doc_id;
        $transaction_details->is_represented = 1;
        $transaction_details->save();
        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction details was stored successfully.");

        //Add Document ID in Acheck21DocumentIdHistory table and update Previous Ignore Flags
        $this->_addDocumentIdHistory($transaction_details);
    }

    private function _addDocumentIdHistory($transaction)
    {
        //Update the ignore flags for the previous flags
        Acheck21DocumentIdHistory::where(['transaction_ref_no' => $transaction->transaction_ref_no])->update(['ignore_flag' => 1]);

        Log::channel('post-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Ignore Flag updated for Transation Ref No. : " . $transaction->transaction_ref_no);

        //Add new Document ID in Acheck21DocumentIdHistory table
        $document_history = new Acheck21DocumentIdHistory();
        $document_history->transaction_id = $transaction->id;
        $document_history->transaction_ref_no = $transaction->transaction_ref_no;
        $document_history->amount = $transaction->amount + $transaction->tip_amount;
        $document_history->acheck_document_id = $transaction->acheck_document_id;
        $document_history->save();

        Log::channel('post-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Document ID added for Transation ID : " . $transaction->id);
    }
}
