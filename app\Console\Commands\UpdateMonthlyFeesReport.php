<?php
namespace App\Console\Commands;

use App\Models\MonthlyFeesTable;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class UpdateMonthlyFeesReport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:monthlyfeesreport';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command updates the final fees of current month for all stores that has been successfully stored.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        // current year and month
        $this->current_year = Carbon::now()->year;
        $this->current_month = Carbon::now()->month;
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->_updateMonthlyFeesTable();
    }
    /**
     * This function updates the final status for all the transactions that has been successfully settled
     */
    private function _updateMonthlyFeesTable()
    {
        Log::channel('update-current-month-fees')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Fetching current month fees for all stores...");
        $this->info("Fetching fees...");
        $active = getStatus(ACTIVE);
        // fetch all fees of all stores
        $storewise_monthly_fees_sql = "SELECT ms.id as store_id, COALESCE(SUM(if(td.local_transaction_year = ? AND td.local_transaction_month = ? AND td.merchant_id IS NOT NULL AND td.entry_type = '" .DEBIT. "', td.amount, 0)),0) as total_fees FROM transaction_details AS td
        INNER JOIN merchant_stores as ms
                ON td.merchant_id = ms.merchant_id
        where ms.status = ?
        GROUP BY ms.id";
        $searchArray = [$this->current_year, $this->current_month, $active];
        $fees = DB::connection(MYSQL_RO)->select($storewise_monthly_fees_sql, $searchArray);
        if (sizeof($fees) != 0) {

            foreach ($fees as $fee) {
               $storeFeesDetails = MonthlyFeesTable::where('store_id', $fee->store_id)->first();
               if (!empty($storeFeesDetails)) {
                    $this->_updateFee($fee, $storeFeesDetails->id);
               } else {
                    $this->_insertFee($fee);
               }
            }
            Log::channel('update-current-month-fees')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Fees updated successfully.");
            $this->info("Fees updated successfully.");
        }

    }
    private function _insertFee($data)
    {
        //create a new record
        $monthly_fees_details = new MonthlyFeesTable();
        $monthly_fees_details->store_id = $data->store_id;
        $monthly_fees_details->fees_amount = $data->total_fees;
        $monthly_fees_details->year = $this->current_year;
        $monthly_fees_details->month = $this->current_month;
        $monthly_fees_details->save();
        Log::channel('update-current-month-fees')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Fees details stored successfully for store Id: " . $data->store_id);
    }
    private function _updateFee($data, $id)
    {
        //update fees details
        MonthlyFeesTable::where('id', $id)->update([
            'fees_amount' => $data->total_fees,
            'year' => $this->current_year,
            'month' => $this->current_month
        ]);
        Log::channel('update-current-month-fees')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Fees details updated successfully for store Id: " . $data->store_id);
    }
}
